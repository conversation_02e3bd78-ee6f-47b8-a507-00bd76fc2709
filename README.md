# 工程目录结构
wfo-frontend/
├── ci                      //cicd 相关
├── public                  //静态文件夹
├── src             
│   ├── common              //公共页面，包括头部导航栏、侧边导航栏等
│   ├── component           //公共组件，包括消息卡片、工单详情侧边抽屉
│   ├── request             //后端axios请求api封装
│   ├── router              //路由封装            
│   ├── util                //工具类方法封装
│   ├── pages               //各详细页面
│   │   ├── doingOrder      //进行中的工单页面
│   │   ├── historyOrder    //历史工单页面
│   │   ├── login           //登录页面
│   │   └── serverOrder     //服务器相关申请页面
│   ├── index.js            //入口文件
│   └── style.css
├── .env.prodution          //生产环境参数配置
└── .env.development        //开发环境参数配置

# 启动命令
npx yarn start

# 打包命令
npx yarn build