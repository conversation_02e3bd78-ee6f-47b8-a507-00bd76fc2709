# 评论功能前端详细设计-迭代一
---

## 项目结构与总体设计

我们将遵循您在概要设计中提出的**容器/展示组件分离**模式。`CommentSection`作为核心容器，封装所有与评论相关的业务逻辑、状态管理和API交互。其子组件`CommentList`和`CommentInput`则作为纯粹的展示组件，负责UI渲染，使得整体结构清晰，易于维护和迭代。

### 目录结构

为了确保架构和目录的完整性，我们将一次性创建好所有迭代中涉及的前端文件。迭代2及之后的功能将在对应文件中用注释和占位函数标明，确保V1版本代码的简洁性。

```
src/
├── components/
│   └── CommentSection/
│       ├── index.js           # 评论区主容器组件 (迭代1实现)
│       ├── CommentList.js     # 评论列表展示组件 (迭代1实现)
│       ├── CommentInput.js    # 评论输入框展示组件 (迭代1实现)
│       └── styles.css         # 评论区专属样式 (迭代2再实现，用于固定输入框等布局)
└── services/
    └── commentService.js      # 封装评论相关的所有API请求 (迭代1实现)
```

### 整体逻辑和交互时序图

以下时序图描述了用户进入工单详情页后，从加载评论到发表一条新评论的完整前端工作流程。

```mermaid
sequenceDiagram
    participant OD as components/OrderDetailDrawer
    participant CS as components/CommentSection/index.js
    participant API as services/commentService.js
    participant CL as components/CommentList.js
    participant CI as components/CommentInput.js

    OD->>CS: 渲染，传入 props: orderId, canComment
    activate CS
    CS->>API: fetchComments(orderId, 1)
    API-->>CS: { data: [comments...], meta: { has_more: true } }
    CS->>CS: 更新 state: comments, page, hasMore
    CS->>CL: 渲染，传入 props: comments, hasMore
    CS->>CI: 渲染，传入 props: isDisabled, onSubmit
    deactivate CS

    User->>CI: 输入 "新评论内容"
    User->>CI: 点击 "发表"
    CI->>CS: 调用 props.onSubmit("新评论内容")

    activate CS
    CS->>CS: 设置 state: isSubmitting = true
    CS->>API: postComment(orderId, "新评论内容")
    API-->>CS: 返回 { id: "new-comment-id", ... }
    CS->>API: fetchComments(orderId, 1) # 重新获取第一页以保证数据最新
    API-->>CS: { data: [new_comments...], meta: { has_more: true } }
    CS->>CS: 更新 state: comments, page, hasMore, isSubmitting = false
    deactivate CS
```

## API接口定义

前端将依赖由`services/commentService.js`封装的后端API。这些接口定义了前后端的数据契约。

1.  **获取评论列表**
    *   **方法:** `GET`
    *   **路径:** `/api/v1/orders/{orderId}/comments?page={page}&page_size={pageSize}`
    *   **说明:** 分页获取指定工单的评论。

2.  **发表新评论**
    *   **方法:** `POST`
    *   **路径:** `/api/v1/orders/{orderId}/comments`
    *   **说明:** 创建一条新评论，内容在请求体中。

## 数据实体结构深化

前端应用状态中管理的数据实体如下。

```mermaid
erDiagram
    COMMENT {
        string id PK
        string content
        string created_at
        object author
    }

    AUTHOR {
        string id PK
        string name
        string avatar_url
    }

    COMMENT ||--|{ AUTHOR : "authored by"
```

*   **Comment:** 代表单条评论对象，是评论列表`comments`状态数组中的基本单元。
*   **Author:** 嵌套在`Comment`对象中，表示评论的作者信息。

## 模块化文件详解 (File-by-File Breakdown)

### `services/commentService.js`

a. **文件用途说明**
该文件是一个API服务层，专门用于封装所有与评论相关的HTTP请求。它将API调用的具体实现（如使用`axios`或`fetch`）与业务逻辑组件解耦，便于统一处理请求、响应和错误。

c. **函数/方法详解**

#### **`fetchComments(orderId, page)`**

*   **用途:** 获取指定工单的评论列表。
*   **输入参数:**
    *   `orderId` (string): 工单ID。
    *   `page` (number): 要获取的页码。
*   **输出数据结构:** `Promise<{{data: Comment[], meta: {has_more: boolean}}>`
*   **实现流程:**

    ```mermaid
    flowchart TD
        A[开始] --> B{拼接URL和查询参数};
        B --> C[发起 GET 请求];
        C --> D{请求是否成功?};
        D -- 是 --> E[返回 response.data];
        D -- 否 --> F[抛出错误];
        E --> G[结束];
        F --> G;
    ```

#### **`postComment(orderId, content)`**

*   **用途:** 向指定工单提交一条新评论。
*   **输入参数:**
    *   `orderId` (string): 工单ID。
    *   `content` (string): 评论内容。
*   **输出数据结构:** `Promise<Comment>`
*   **实现流程:**

    ```mermaid
    flowchart TD
        A[开始] --> B["构造请求体 { content }"];
        B --> C[发起 POST 请求];
        C --> D{请求是否成功?};
        D -- 是 --> E[返回 response.data];
        D -- 否 --> F[抛出错误];
        E --> G[结束];
        F --> G;
    ```

### `components/CommentSection/index.js`

a. **文件用途说明**
评论功能的**容器组件**。负责管理评论列表的状态（数据、分页、加载状态）、处理用户交互（发表、加载更多），并调用`commentService`与后端通信。

b. **组件接口图 (Props & State)**

```mermaid
classDiagram
    class CommentSection {
        <<Component>>
        +Props
        string orderId
        boolean canComment
        +State
        Comment[] comments
        number page
        boolean hasMore
        boolean isLoading
        boolean isSubmitting
        Error error
        +Functions
        fetchComments()
        handlePostComment(content)
        handleLoadMore()
    }
```

c. **函数/方法详解**

#### **`useEffect` (on Mount)**

*   **用途:** 组件首次加载时，获取第一页评论数据。
*   **输入参数:** 无。
*   **输出数据结构:** 无 (副作用：更新组件状态)。
*   **实现流程:**

    ```mermaid
    flowchart TD
        A[组件挂载] --> B["调用 fetchComments(orderId, 1)"];
        B --> C{API调用成功?};
        C -- 是 --> D[更新 state: comments, page=1, hasMore, isLoading=false];
        C -- 否 --> E[更新 state: error, isLoading=false];
        D --> F[结束];
        E --> F;
    ```

#### **`handlePostComment(content)`**

*   **用途:** 处理子组件传递上来的发表评论事件。
*   **输入参数:** `content` (string): 评论文本。
*   **输出数据结构:** 无 (副作用：调用API并刷新列表)。
*   **实现流程:**

    ```mermaid
    sequenceDiagram
        participant User as 用户
        participant Me as handlePostComment
        participant API as services/commentService.js

        User->>Me: 触发提交
        Me->>Me: 设置 state.isSubmitting = true
        Me->>API: postComment(orderId, content)
        API-->>Me: 成功
        Me->>API: fetchComments(orderId, 1)
        API-->>Me: 返回最新评论列表
        Me->>Me: 更新 state.comments, state.page=1, state.isSubmitting=false
    ```

#### **`handleLoadMore()`**

*   **用途:** 加载下一页评论数据，并追加到现有列表。
*   **输入参数:** 无。
*   **输出数据结构:** 无 (副作用：更新组件状态)。
*   **实现流程:**

    ```mermaid
    flowchart TD
        A[用户点击加载更多] --> B[设置 state.isLoading = true];
        B --> C["计算 nextPage = state.page + 1"];
        C --> D["调用 fetchComments(orderId, nextPage)"];
        D --> E{API调用成功?};
        E -- 是 --> F["更新 state.comments = [...oldComments, ...newComments]"];
        F --> G["更新 state.page = nextPage, hasMore, isLoading=false"];
        E -- 否 --> H[更新 state.error, isLoading=false];
        G --> I[结束];
        H --> I;
    ```

### `components/CommentSection/CommentList.js`

a. **文件用途说明**
纯**展示组件**。负责接收评论数组并将其渲染为列表。当需要加载更多时，显示一个按钮并触发父组件传递的回调。

b. **组件接口图 (Props)**

```mermaid
classDiagram
    class CommentList {
        <<Presentational Component>>
        +Props
        Comment[] comments
        boolean hasMore
        boolean isLoading
        Function onLoadMore
        +Future Props (Iter 2)
        ref listContainerRef
    }
```

c. **函数/方法详解**

#### **渲染逻辑**

*   **用途:** 根据props渲染UI。
*   **实现流程:**
    ```mermaid
    flowchart TD
        A[开始] --> B["遍历 props.comments 数组"];
        B --> C["为每条 comment 渲染一个列表项 (<li>)"];
        C --> D{"props.hasMore 是否为 true?"};
        D -- 是 --> E["渲染 '加载更多' 按钮"];
        E --> F{"按钮的 onClick 事件绑定到 props.onLoadMore"};
        D -- 否 --> G["不渲染按钮"];
        F --> G
        G --> H["显示 '正在加载...' 的提示 (如果 props.isLoading 为 true)"];
        H --> I[结束];
    ```

#### **`// 迭代2再实现: 滚动到底部`**

*   **用途:** 页面初次加载或新评论发表后，自动滚动到列表底部。
*   **实现方式:** 使用`useLayoutEffect`和`ref`来操作DOM，用固定返回值占位。

### `components/CommentSection/CommentInput.js`

a. **文件用途说明**
纯**展示组件**。提供一个受控的文本输入框和发表按钮。

b. **组件接口图 (Props)**

```mermaid
classDiagram
    class CommentInput {
        <<Presentational Component>>
        +Props
        boolean isDisabled
        boolean isSubmitting
        Function onSubmit
    }
```

c. **函数/方法详解**

#### **`handleInputChange(event)`**

*   **用途:** 更新内部的文本状态。
*   **实现流程:**
    ```mermaid
    flowchart TD
        A[用户输入] --> B[获取 event.target.value];
        B --> C[调用 setInputValue 更新内部 state];
        C --> D[结束];
    ```

#### **`handleSubmit()`**

*   **用途:** 处理点击发表按钮的事件。
*   **实现流程:**
    ```mermaid
    flowchart TD
        A[点击发表按钮] --> B{输入内容是否为空或超过150字符?};
        B -- 是 --> C[提示错误，不执行操作];
        B -- 否 --> D["调用 props.onSubmit(inputValue)"];
        D --> E["清空输入框 (setInputValue(''))"];
        C --> F[结束];
        E --> F;
    ```

### `components/CommentSection/styles.css`

*   **迭代2再实现，用固定返回值占位**
*   **文件用途说明:** 存放评论区专属样式。在迭代2中，将实现固定底部输入框的布局样式。

## 迭代演进依据

这份详细设计为未来的迭代演进提供了坚实的基础：

1.  **架构完整性:** 目录结构和文件划分一次成型。`styles.css`虽然在迭代1中为空，但它的存在明确了未来样式隔离的位置。
2.  **职责单一:** `CommentSection`容器与`CommentList`/`CommentInput`展示组件的分离，使得未来UI/UE的优化（如迭代2的懒加载和固定布局）可以被隔离在`CommentList.js`和`styles.css`中，而无需触碰核心的业务逻辑。
3.  **逻辑封装:** `commentService.js`将所有API调用细节封装起来。未来如果API变动或需要引入更复杂的HTTP客户端（如添加统一的interceptor），只需修改此文件，所有上层组件不受影响。
4.  **清晰的演进路径:**
    *   **懒加载 (Iter 2):** 从“加载更多”按钮升级到基于`IntersectionObserver`的无限滚动，仅需修改`CommentList.js`，为它添加一个观察器逻辑来触发`onLoadMore`即可。
    *   **实时更新:** 未来若要从轮询升级到WebSocket，只需在`CommentSection/index.js`中替换`useEffect`里的轮询逻辑为WebSocket事件监听器，其他组件完全无需改动。
    *   **权限驱动UI:** 前端完全依赖`canComment`这个prop。未来任何复杂的权限变更，只要后端能正确计算并返回这个布尔值，前端的UI（如`CommentInput`的禁用状态）就能自动、正确地响应，无需修改前端代码。