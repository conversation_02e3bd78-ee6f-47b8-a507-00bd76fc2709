# **工单评论功能 V1.0 - Web端前端需求文档 (PRD)**

## **1. 需求概述 (Overview)**

### 1.1. 功能目标
为Web端的工单详情页提供一个纯文本评论功能，允许与工单相关的用户进行交流和信息同步。

### 1.2. 核心用户流程
用户进入工单详情页 -> 查看历史评论 -> 输入评论内容 -> 发表评论 -> 页面实时更新评论列表。

## **2. 页面组件与UI规格 (Page Components & UI Specs)**

### 2.1. 评论区整体布局
*   评论区位于工单详情页的特定区域（通常在主信息下方）。
*   由**评论列表 (2.2)** 和**评论输入区 (2.3)** 两部分构成。
*   评论输入区始终位于评论列表的下方。

### 2.2. 评论列表 (Comment List)
*   **2.2.1. 列表行为**
    *   **首次加载：** 页面加载时，默认显示最新的 **10** 条评论。
    *   **滚动加载（懒加载）：**
        *   当用户向上滚动列表至顶部时，自动触发加载更早的历史评论。
        *   每次触发，向后端请求 **10** 条历史评论。
        *   在请求数据期间，列表顶部需显示一个加载指示器（如：旋转的菊花图标和“加载中...”文字）。
    *   **加载完毕：** 当所有历史评论加载完毕后，不再触发加载，加载指示器消失。
    *   **定位：** 页面首次加载完成时，视图需自动滚动，将**最新一条评论**完整展示在可视区域内。

*   **2.2.2. 评论条目 (Comment Item)**
    *   **结构与样式：**
        *   **第一行：** `[头像]` `[姓名]` `[发布时间]`
        *   **第二行：** `[评论内容]` (评论内容需支持自动换行)
    *   **元素规格：**
        *   **头像：**
            *   类型：文字头像。
            *   生成规则：根据用户姓名生成。两个字的姓名取全名（如“李四”->“李四”）；三个字的姓名取后两个字（如“张三丰”->“三丰”）。
            *   异常处理：若无法获取用户名，则显示一个统一的**通用默认头像**。
        *   **姓名：**
            *   显示用户的真实姓名。
            *   异常处理：若无法获取用户名，则显示为**“未知用户”**。
        *   **发布时间：**
            *   格式：采用混合时间格式。24小时内发表的显示为相对时间（如：“5分钟前”，“2小时前”）；超过24小时的显示为绝对时间（如：“2025-07-16 10:30”）。
        *   **评论内容：**
            *   显示纯文本评论。
            *   异常处理：如果从后端获取到的某条评论数据内容为空，前端**不应渲染该条评论**。

*   **2.2.3. 空状态 (Empty State)**
    *   如果当前工单没有任何评论，评论列表区域应显示友好的提示文案，如：“**暂无评论，快来发表第一条吧！**”

### 2.3. 评论输入区 (Comment Input Area)
*   **2.3.1. 组件构成**
    *   一个多行文本输入框（Textarea）。
    *   一个字符计数器。
    *   一个“发表”按钮。

*   **2.3.2. 交互逻辑**
    *   **输入框：**
        *   提示文字 (Placeholder)：如“请输入评论内容...”。
        *   内容限制：纯文本，上限 **150** 字符。
    *   **字符计数器：**
        *   实时显示用户已输入的字符数，格式为 `当前字数/150`，例如 `58/150`。
        *   当输入内容超过150字符时，计数器数字部分需**标红**（如：`152/150`），且“发表”按钮置灰。
    *   **“发表”按钮：**
        *   点击后，触发提交评论的动作。
        *   在等待后端接口响应期间，按钮应置为**禁用状态**，文案变为“**提交中...**”，以防止重复提交。
        *   校验：点击时若输入框内容为空，或内容超过150字符，则不发起请求，并通过`message`组件提示错误。

## **3. 权限与状态 (Permissions & States)**

### 3.1. 核心权限逻辑
*   前端通过页面初始化时后端接口返回的布尔值字段 `canComment` (true/false) 来判断当前用户是否拥有评论权限。

### 3.2. 不同状态下的UI表现
*   **有权限评论 (`canComment: true`)：**
    *   评论输入区完全可用。
*   **无权限评论 (`canComment: false`)：**
    *   此状态通常由“工单已完结”触发。
    *   评论输入区**整体置灰**，变为不可用状态。
    *   输入框内显示提示文案，如：“**工单已完结，无法发表评论**”。
    *   “发表”按钮禁用。

## **4. 实时性与数据同步 (Real-time & Data Sync)**

### 4.1. 发表评论（乐观UI）
*   当用户点击“发表”并成功收到后端**同步成功**的响应后，前端必须**立即**执行以下操作，无需等待后台推送：
    1.  将用户刚刚提交的评论内容（包括用户名、头像、内容和本地生成的近似时间）**追加到评论列表的底部**。其外观与普通已确认评论无差别。
    2.  **清空**评论输入框的内容。
    3.  重置字符计数器为 `0/150`。

### 4.2. 接收新评论（轮询）
*   **触发条件：** 用户停留在工单详情页时。
*   **轮询机制：**
    *   前端应**每隔5秒**自动向后端发起一次请求，检查是否有新评论。
    *   **智能启停：** 当用户切换到其他浏览器标签页或最小化窗口时，此轮询应**自动暂停**；当用户返回该页面时，轮询应**自动恢复**。
*   **数据处理：**
    *   当轮询请求返回新评论数据时，前端应将这些新评论追加到列表底部。
    *   **排序保证：** 为确保最终一致性，在追加新评论或任何需要重新渲染列表的场景下，都必须严格按照评论数据中的**服务器时间戳**对整个列表进行排序。

## **5. 错误处理 (Error Handling)**

*   所有与评论功能相关的错误提示，统一采用 Ant Design 的 `message` 全局提示组件（从页面顶部滑出，数秒后自动消失）。
*   **需处理的错误场景及建议文案（文案由后端接口返回）：**
    *   **前端校验：**
        *   提交内容为空：“评论内容不能为空”
        *   提交内容超长：“评论内容不能超过150个字符”
    *   **后端返回：**
        *   无权限操作：“您没有权限发表评论”
        *   工单已完结：“操作失败，工单已完结”
        *   网络或服务器错误：“网络开小差了，请稍后重试”

## **6. V1.0范围外的非功能性需求**
*   **DOM回收机制：** 鉴于单工单评论上限为50条，V1.0暂不考虑因DOM元素过多导致的性能优化。