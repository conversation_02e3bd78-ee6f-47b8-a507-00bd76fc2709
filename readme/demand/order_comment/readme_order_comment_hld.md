# 前端概要设计

前端架构将遵循**容器组件(Container Components)**与**展示组件(Presentational Components)**分离的模式。`OrderDetailDrawer` 作为现有容器，将包含一个新的、负责管理自身所有逻辑的容器组件 `<CommentSection />`。

这种分层可以确保职责单一，`OrderDetailDrawer` 继续负责工单的整体业务流，而 `<CommentSection />` 则专注于评论功能的实现。

下图展示了用户打开工单详情抽屉后，查看评论并发表一条新评论的核心交互流程：

```mermaid
sequenceDiagram
    participant User as 用户
    participant OrderDetailDrawer as 抽屉容器
    participant CommentSection as 评论区容器
    participant API as 后端接口

    User->>OrderDetailDrawer: 点击查看工单详情
    OrderDetailDrawer->>OrderDetailDrawer: 显示抽屉 (drawerVisible = true)
    OrderDetailDrawer->>API: 1. 请求工单详情 (GET /orders/{id})
    API-->>OrderDetailDrawer: 返回工单数据 (含 can_comment: true)
    OrderDetailDrawer->>CommentSection: 2. 渲染评论区并传入 orderId 和 can_comment
    
    CommentSection->>API: 3. 请求第一页评论 (GET /orders/{id}/comments)
    CommentSection->>CommentSection: 启动10秒轮询计时器
    API-->>CommentSection: 返回评论列表
    CommentSection->>CommentSection: 更新内部状态，渲染评论
    
    User->>CommentSection: 4. 输入评论并点击发表
    CommentSection->>API: 5. 提交新评论 (POST /orders/{id}/comments)
    API-->>CommentSection: 返回成功
    
    CommentSection->>CommentSection: 6. 立即重置并触发轮询
    CommentSection->>API: 7. 再次请求评论 (获取最新列表)
    API-->>CommentSection: 返回包含新评论的列表
    CommentSection->>CommentSection: 更新状态，渲染最新评论列表

```

## 组件拆分(Components)

我们将新增一个独立的评论功能模块，并将其拆分为以下几个React组件：

*   **`OrderDetailDrawer` (现有，容器组件)**
    *   **核心职责:** 维持其现有职责，管理工单详情的展示、审批、转审等操作。
    *   **新增职责:** 在获取工单详情后，将 `orderId` 和后端返回的 `can_comment` 权限标志作为 props 传递给 `<CommentSection />` 组件。

*   **`<CommentSection />` (新增，容器组件)**
    *   **核心职责:** 评论功能的“大脑”。
    *   接收 `orderId` 和 `can_comment` 作为 props。
    *   管理评论列表数据、加载状态、分页信息 (`page`, `hasMore`)。
    *   **负责所有与评论API的交互:** 获取评论列表、发表新评论。
    *   **实现10秒轮询逻辑:** 在组件挂载时启动计时器，在组件卸载时清除计时器，防止内存泄漏。
    *   向其子组件传递所需的 props 和回调函数。

*   **`<CommentList />` (新增，展示组件)**
    *   **核心职责:** “只负责展示”。
    *   接收 `comments` 数组作为 props 并渲染列表。
    *   基于 `hasMore` prop 决定是否显示“加载更多”按钮。
    *   触发一个由父组件传入的 `onLoadMore` 函数。

*   **`<CommentInput />` (新增，展示组件)**
    *   **核心职责:** “只负责输入”。
    *   渲染文本输入框和发表按钮。
    *   接收 `isDisabled` (基于 `can_comment` 决定) 和 `onSubmit` 回调函数作为 props。
    *   管理输入框的内部文本状态，但在提交时调用父组件的 `onSubmit` 函数，将评论内容传递出去。

## 目录结构树(Directory Tree)

我们将在 `components` 目录下创建一个新文件夹 `CommentSection` 来组织所有与评论功能相关的新组件，以实现高内聚。

```
src/
└── components/
    ├── OrderDetailDrawer/
    │   └── index.js         // 现有的工单详情抽屉组件
    └── CommentSection/        // (新增) 评论功能模块目录
        ├── index.js           // CommentSection 容器组件
        ├── CommentList.js     // (新增) 评论列表展示组件
        └── CommentInput.js    // (新增) 评论输入区展示组件
```

## 数据流(Data Flow)

**场景: 用户发布一篇新文章并通知其关注者 (在我们的场景下是 “用户发表一条新评论”)**

此场景的数据流动完全封装在 `<CommentSection />` 组件内部，清晰地展示了其作为“容器”的角色。

1.  **用户交互:** 用户在 `<CommentInput />` 组件渲染的文本框中输入 "这是一条测试评论"。
2.  **事件触发:** 用户点击“发表”按钮。`<CommentInput />` 组件的 `onClick` 事件被触发。
3.  **回调调用:** `<CommentInput />` 调用从 props 中接收到的 `onSubmit` 函数，并将当前输入框的文本 "这是一条测试评论" 作为参数传入。即 `props.onSubmit("这是一条测试评论")`。
4.  **状态管理与API请求:** 该 `onSubmit` 函数是在父组件 `<CommentSection />` 中定义的。
    *   函数被触发后，`<CommentSection />` 首先可能会设置一个加载状态，如 `isSubmitting: true`，以禁用发表按钮，防止重复提交。
    *   然后，它调用API方法：`postComment(orderId, "这是一条测试评论")`。
5.  **API响应与状态更新:**
    *   后端处理请求，成功后返回 `200 OK`。
    *   在API请求的 `.then()` 回调中，`<CommentSection />` 会立即调用其内部的 `fetchComments()` 方法来刷新整个评论列表，确保获取到包含自己刚刚发表内容在内的最新数据。
    *   同时，将 `isSubmitting` 状态重新设为 `false`。
6.  **UI重新渲染:** `fetchComments()` 获取到最新评论列表后，会调用 `setComments([...newComments])` 更新组件状态。React检测到状态变化，自动重新渲染 `<CommentList />`，用户界面上便出现了最新的评论列表。

**交互时序图:**

```mermaid
sequenceDiagram
    participant User as 用户
    participant CommentInput as 输入组件
    participant CommentSection as 评论区容器
    participant API as 后端接口

    User->>CommentInput: 输入评论文本
    User->>CommentInput: 点击 "发表" 按钮
    CommentInput->>CommentSection: 调用 props.onSubmit("评论内容")
    
    activate CommentSection
    CommentSection->>CommentSection: 设置提交状态 (e.g., isSubmitting = true)
    CommentSection->>API: POST /api/v1/orders/{id}/comments (body: { content: "..." })
    API-->>CommentSection: 返回 200 OK
    
    CommentSection->>API: GET /api/v1/orders/{id}/comments?page=1
    API-->>CommentSection: 返回最新的评论列表数据
    CommentSection->>CommentSection: 更新 state.comments 和 isSubmitting = false
    deactivate CommentSection
    
    Note over CommentInput, CommentSection: React根据新state自动重新渲染UI

```

## 数据模型设计(Data Model Design)

前端组件所需的核心数据实体及其关系如下。后端API应提供聚合后的数据。

**前端数据结构 (TypeScript/JSDoc):**
```javascript
/**
 * @typedef {object} Comment
 * @property {string} id - 评论的唯一ID
 * @property {string} content - 评论的文本内容
 * @property {string} created_at - 发表时间 (ISO 8601 格式)
 * @property {object} author - 发表者信息
 * @property {string} author.id - 用户ID
 * @property {string} author.name - 用户名
 * @property {string} [author.avatar_url] - 用户头像URL (可选)
 */

/**
 * @typedef {object} OrderDetail
 * // ... 其他工单详情字段
 * @property {boolean} can_comment - 当前用户是否可以评论
 */
```

**实体关系图 (ER Diagram):**

```mermaid
erDiagram
    USERS {
        string user_id PK
        string name
        string avatar_url
    }
    ORDERS {
        string order_id PK
        string title
        string requester_id FK
    }
    COMMENTS {
        string comment_id PK
        string content
        datetime created_at
        string order_id FK
        string author_id FK
    }

    USERS ||--o{ ORDERS : "is requester of"
    ORDERS ||--|{ COMMENTS : "has"
    USERS ||--|{ COMMENTS : "authors"
```

## API接口定义

前端评论功能依赖以下几个关键API端点。

1.  **获取工单详情 (修改现有接口)**
    *   **Endpoint:** `GET /api/v1/orders/{orderId}`
    *   **说明:** 在现有的返回数据中，必须追加一个布尔类型的字段 `can_comment`，由后端根据需求 `2.0` 的复杂权限规则计算得出。

2.  **获取评论列表 (新增接口)**
    *   **Endpoint:** `GET /api/v1/orders/{orderId}/comments`
    *   **Query Params:**
        *   `page` (number, optional, default: 1): 页码。
        *   `page_size` (number, optional, default: 20): 每页数量。
    *   **说明:** 用于分页加载评论。响应体应包含评论数组和分页信息。
    *   **成功响应 (200 OK):**
        ```json
        {
          "data": [
            {
              "id": "comment-123",
              "content": "请尽快处理。",
              "created_at": "2025-07-17T10:00:00Z",
              "author": { "id": "user-abc", "name": "张三" }
            }
          ],
          "meta": {
            "has_more": true
          }
        }
        ```

3.  **发表新评论 (新增接口)**
    *   **Endpoint:** `POST /api/v1/orders/{orderId}/comments`
    *   **说明:** 发表一条新评论。后端必须在执行操作前，严格根据缓存或数据库进行权限校验。
    *   **请求体 (Request Body):**
        ```json
        {
          "content": "这是一条不超过150字符的新评论。"
        }
        ```
    *   **成功响应 (201 Created):** 返回新创建的评论对象。
    *   **失败响应 (403 Forbidden):** 无权限或工单已完结。

## 迭代演进依据

这份设计方案之所以易于迭代演进，主要基于以下几点：

1.  **高度封装和内聚:** `<CommentSection>` 作为一个独立的“黑盒”，封装了所有评论相关的状态、逻辑和API调用。未来任何对评论功能的修改（如从轮询升级到WebSocket）都只发生在这个组件内部，不会影响到 `OrderDetailDrawer` 或其他任何外部组件。

2.  **职责分离清晰:** 通过将功能拆分为容器和纯展示组件，我们的演进路径非常清晰：
    *   **想改变外观？** 只需修改 `<CommentList />` 或 `<CommentInput />`。
    *   **想改变逻辑？** 只需修改 `<CommentSection />`。

3.  **简单的起点 (KISS):**
    *   **轮询机制:** 我们选择了最简单的轮询方案，它无需复杂的连接管理，健壮且易于实现。如果未来需要真·实时，可以将 `<CommentSection />` 中的轮询逻辑替换为WebSocket/SSE处理器，这是一个隔离的、可控的升级。
    *   **“加载更多”替代无限滚动:** 基于评论数不多的判断，V1.0的`<CommentList />`可以先实现一个“加载更多”按钮。这比实现无限滚动（需要处理滚动事件监听、节流、边界判断）简单得多。当需求明确时，升级为无限滚动也只是修改`<CommentList />`内部的逻辑。

4.  **后端驱动的权限 (Decoupling):** 设计中最关键的一点是，前端不参与任何复杂的权限计算。我们只依赖后端提供的 `can_comment` 标志。这意味着未来如果权限规则发生天翻地覆的变化（例如增加了“管理员”角色），前端代码**一行都不需要改**，这极大地提高了系统的可维护性和演进速度。