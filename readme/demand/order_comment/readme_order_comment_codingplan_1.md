# **前端编码计划 - 迭代一**。

---

### **1. 最终代码目录结构**

```
src/
├── components/
│   ├── CommentSection/
│   │   ├── index.js              # 容器组件：管理状态和逻辑
│   │   ├── CommentList.js        # 展示组件：渲染评论列表
│   │   ├── CommentInput.js       # 展示组件：渲染评论输入区
│   │   └── styles.css            # 样式文件 (本迭代为空，仅占位)
│   └── OrderDetailDrawer/
│       └── index.js              # 【受影响】现有工单详情抽屉组件
├── services/
│   └── commentService.js         # 业务服务层：调用api.js，为未来业务逻辑预留
└── request/
    └── api.js                    # API请求层：项目中所有HTTP请求的唯一出口
```

### **2. 核心模块职责**

*   **`CommentSection/index.js` (容器组件):** 负责评论区的整体业务逻辑，管理评论数据、分页、加载状态，并处理用户交互事件，通过调用 `commentService` 完成业务动作。
*   **`CommentList.js` / `CommentInput.js` (展示组件):** 纯UI组件，接收`props`并渲染界面，将用户操作通过回调函数通知给父组件。
*   **`commentService.js` (业务服务层):** 作为组件与API层的桥梁，调用 `api.js` 中的函数来执行请求。它是未来添加数据转换、缓存等前端业务逻辑的理想场所。
*   **`api.js` (API请求层):** 负责定义和执行所有与后端通信的HTTP请求。项目中所有API调用都应由此文件导出。

### **3. 实现流程图**

```mermaid
graph TD
    A[<b>步骤 1:</b> 建立架构基础] --> B[<b>步骤 2:</b> 开发静态UI组件]
    B --> C[<b>步骤 3:</b> 开发容器组件(Mock)]
    C --> D[<b>步骤 4:</b> 集成到工单详情页]
    D --> E[<b>步骤 5:</b> 实现评论发表(真实API)]
    E --> F[<b>步骤 6:</b> 实现列表加载(真实API)]
    F --> G[<b>步骤 7:</b> 完善与收尾]
    G --> H[<b>完成</b> ✅]
```

---

### **4. 渐进式小步迭代编码步骤**

#### **步骤 1: 建立架构基础 (可验证)**

1.  **编码:**
    *   创建上文所述的所有新文件和目录。
    *   在 `src/request/api.js` 中，编写**模拟**的API函数。

    ```javascript
    // src/request/api.js
    const mockAuthor = { id: 'user-1', name: '模拟用户', avatar_url: 'https://via.placeholder.com/40' };
    export const fetchComments = (orderId, page = 1) => {
      return new Promise(res => setTimeout(() => res({ data: { data: Array.from({ length: 10 }, (_, i) => ({ id: `c-${page}-${i}`, content: `这是第${page}页的模拟评论`, created_at: new Date().toISOString(), author: mockAuthor })), meta: { has_more: page < 3 } } }), 500));
    };
    export const postComment = (orderId, content) => {
      return new Promise(res => setTimeout(() => res({ data: { id: `new-${Date.now()}`, content, created_at: new Date().toISOString(), author: mockAuthor } }), 500));
    };
    ```
    *   在 `src/services/commentService.js` 中，导入并转发 `api.js` 的调用。

    ```javascript
    // src/services/commentService.js
    import * as api from '../request/api';
    export const fetchComments = (orderId, page) => api.fetchComments(orderId, page);
    export const postComment = (orderId, content) => api.postComment(orderId, content);
    ```

2.  **职责说明:** 完成了三层架构（API层、服务层、组件层）的搭建，并使用模拟数据解耦了前后端依赖。
3.  **验证:**
    *   项目可以无错误地编译和启动。
    *   可以在浏览器开发者工具的控制台中，成功调用 `commentService.fetchComments()` 并看到返回的模拟数据。

#### **步骤 2: 开发静态UI组件 (可验证)**

1.  **编码:**
    *   **`CommentInput.js`**: 创建一个包含受控 `textarea` 和 `button` 的组件。使用 `useState` 管理输入值和字符数。实现客户端校验（非空/长度<150）。
    *   **`CommentList.js`**: 创建一个接收 `comments` 数组并将其渲染为 `<ul>` 列表的组件。根据 `hasMore` prop 显示/隐藏“加载更多”按钮。

2.  **职责说明:** 创建了无业务逻辑、可复用的UI组件。
3.  **验证:**
    *   在 `OrderDetailDrawer/index.js` 中临时导入这两个组件，并用静态假数据渲染。
    *   **检查点:** 评论列表和输入框在页面上显示正确。输入框功能（输入、字符统计、校验）工作正常。完成后移除临时代码。

#### **步骤 3: 开发容器组件并连接模拟数据 (可验证)**

1.  **编码 (`components/CommentSection/index.js`):**
    *   创建 `CommentSection` 容器组件，`import { fetchComments } from '../../services/commentService'`。
    *   使用 `useState` 管理 `comments`, `page`, `hasMore`, `isLoading` 等状态。
    *   使用 `useEffect` 在组件首次加载时调用 `fetchComments`，并将返回的模拟数据更新到 `state` 中。
    *   将状态和回调函数（此时可为空函数）作为 `props` 传递给 `CommentList` 和 `CommentInput`。

2.  **职责说明:** 将UI组件与数据流（模拟）连接起来，形成了功能模块的雏形。
3.  **验证:**
    *   在 `OrderDetailDrawer` 中临时渲染 `<CommentSection orderId="any-mock-id" canComment={true} />`。
    *   **检查点:** 页面正确显示了包含10条模拟评论的列表和“加载更多”按钮。

#### **步骤 4: 集成到工单详情页 (可验证)**

1.  **编码 (`components/OrderDetailDrawer/index.js`):**
    *   正式导入 `CommentSection`。
    *   在 `render` 方法的 `<Drawer>` 内容区的合适位置（如备注下方）添加 `<CommentSection />`。
    *   传递真实的 `props`:
        *   `orderId={this.state.orderInfo.id}`
        *   `canComment={this.state.orderInfo.status !== 'CLOSED'}` (或根据你的具体状态值)

2.  **职责说明:** 将评论功能模块正式嵌入到现有业务页面中。
3.  **验证:**
    *   打开任意工单详情抽屉。
    *   **检查点:** 评论区被正确渲染。对于“未完结”的工单，输入框可用；对于“已完结”的工单，输入框被禁用。

#### **步骤 5: 实现评论发表功能 (真实API) (可验证)**

1.  **编码:**
    *   **`src/request/api.js`**: 将 `postComment` 函数修改为真实的 `axios` (或 `fetch`) API调用。
        ```javascript
        import axios from 'axios';
        export const postComment = (orderId, content) => {
          return axios.post(`/api/v1/orders/${orderId}/comments`, { content });
        };
        ```
    *   **`CommentSection/index.js`**: 实现 `handlePostComment` 函数，调用 `commentService.postComment`，并在 `.then()` 中调用 `fetchComments(orderId, 1)` 以刷新列表。

2.  **职责说明:** 完成了发表评论功能的闭环，从用户输入到后端存储再到界面刷新。
3.  **验证:**
    *   打开一个“未完结”工单，发表一条新评论。
    *   **检查点:** 浏览器网络工具中可以看到成功的 `POST` 请求。列表自动刷新，新评论显示在最上方。

#### **步骤 6: 实现列表加载功能 (真实API) (可验证)**

1.  **编码:**
    *   **`src/request/api.js`**: 将 `fetchComments` 函数修改为真实的API调用。
        ```javascript
        import axios from 'axios';
        export const fetchComments = (orderId, page = 1) => {
            return axios.get(`/api/v1/orders/${orderId}/comments`, { params: { page, page_size: 10 } });
        };
        ```
    *   **`CommentSection/index.js`**: 实现 `handleLoadMore` 函数，调用 `commentService.fetchComments` 并将返回的新数据追加到现有 `comments` 数组中。

2.  **职责说明:** 完成了评论列表的初始化加载和分页加载功能。
3.  **验证:**
    *   打开一个评论较多的工单。
    *   **检查点:** 初始评论列表从后端正确加载。点击“加载更多”按钮，可以看到成功的 `GET` 请求，并且新评论被追加到列表末尾。

#### **步骤 7: 完善与收尾 (可验证)**

1.  **编码 (`CommentSection/index.js`):**
    *   在所有调用 `commentService` 的地方添加 `.catch()` 错误处理逻辑。
    *   当捕获到错误时，更新一个 `error` 状态，并在UI上显示一个错误提示（如 AntD 的 `<Alert>` 组件）。

2.  **职责说明:** 增强了应用的用户体验和健壮性。
3.  **验证:**
    *   通过浏览器工具模拟API请求失败。
    *   **检查点:** 页面上出现友好的错误提示信息，而不是崩溃或无响应。整个应用在评论功能V1.0范围内行为符合预期。

---

至此，迭代一的全部开发步骤已规划完毕。请按此计划执行。