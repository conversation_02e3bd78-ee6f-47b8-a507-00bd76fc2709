# **前端编码计划 - 迭代二**

---

### **1. 代码目录结构与模块影响分析**

#### **1.1 最终目录结构 (Iteration 2 Completion)**

本次迭代将新增 `styles.css`、`models/` 目录及其中文件，并对 `CommentList.js` 和 `OrderDetailDrawer/index.js` 进行修改。

```
src/
├── components/
│   └── CommentSection/
│       ├── index.js           # 评论区容器 (本次迭代将修改)
│       ├── CommentList.js     # 评论列表 (本次迭代将修改)
│       ├── CommentInput.js    # 评论输入区 (无变更)
│       └── styles.css         # (新增) 评论区专属样式
├── models/                    # (新增) 全局数据模型目录
│   └── feishuCard.js        # (新增) 飞书卡片数据结构定义
├── services/
│   ├── commentService.js      # (无变更)
│   └── feishuService.js     # (新增) 飞书API服务占位文件
└── views/                     # (假设的视图目录)
    └── OrderDetailDrawer/
        └── index.js           # 工单详情抽屉 (本次迭代将修改)
```

#### **1.2 受影响的现有模块说明**

1.  **`views/OrderDetailDrawer/index.js`**:
    *   **影响**: 这是评论功能的入口。我们需要修改此文件，以将 `CommentSection` 组件集成到其渲染树中。
    *   **适配/扩展**:
        *   引入 `CommentSection` 组件。
        *   在组件的 `render` 方法中，找到合适的位置（例如，在工单详情下方）渲染 `<CommentSection />`。
        *   从自身的 `state` 或 `props` 中获取当前工单的 `order_id`，并将其作为 `prop` 传递给 `CommentSection`。

2.  **`components/CommentSection/index.js`**:
    *   **影响**: 作为容器组件，它需要为懒加载功能管理分页状态。
    *   **适配/扩展**:
        *   增加新的状态来管理分页逻辑，如 `page`, `hasMore`, `isLoading`。
        *   实现 `onLoadMore` 函数，该函数负责调用 `commentService` 来获取下一页（即更早的）评论数据，并更新状态。
        *   将 `onLoadMore`, `hasMore`, `isLoading` 等状态作为 `props` 传递给 `CommentList` 组件。

3.  **`components/CommentSection/CommentList.js`**:
    *   **影响**: 这是本次迭代的核心交互实现区。
    *   **适配/扩展**:
        *   实现“自动滚动到底部”的功能。
        *   实现“懒加载”功能，这需要使用 `IntersectionObserver` API。

### **2. 整体开发流程**

我们将采用分层、自底向上的方法，先搭建好结构，然后集成，再逐一实现具体的用户体验功能。

```mermaid
flowchart TD
    subgraph Legend [图例]
        direction LR
        A_ext((现有模块))
        A_new[[新增模块]]
        A_mod[/修改模块/]
    end

    subgraph Step 1-2: 结构与集成
        B_Drawer((OrderDetailDrawer)) -- 传入orderId --> C_CS[/CommentSection/]
    end

    subgraph Step 3-6: 功能实现
        D_CSS[[styles.css]] -- 美化布局 --> C_CS
        C_CS -- 管理分页状态/提供加载函数 --> E_CL[/CommentList/]
        E_CL -- 触发onLoadMore --> C_CS
        E_CL -- 实现UI交互 --> F_User(用户)
    end
    
    subgraph Step 7: 未来准备
        G_FeishuM[[models/feishuCard.js]]
        H_FeishuS[[services/feishuService.js]]
    end

    A_ext --> B_Drawer
    A_new --> D_CSS
    A_mod --> C_CS
    A_mod --> E_CL
    A_new --> G_FeishuM
    A_new --> H_FeishuS
```

### **3. 渐进式小步迭代开发步骤**

---

#### **步骤 1: 搭建新文件结构**

**目标:** 创建本次迭代所需的新文件，并填入占位内容，确保项目结构完整性。

1.  **创建样式文件:** 在 `src/components/CommentSection/` 目录下，创建 `styles.css` 文件。内容暂时留空。
2.  **创建模型目录与文件:** 在 `src/` 目录下创建 `models/` 目录，并在其中创建 `feishuCard.js` 文件。
    *   在 `feishuCard.js` 中添加一行注释：`// 迭代二：定义飞书卡片数据结构`
3.  **创建服务占位文件:** 在 `src/services/` 目录下创建 `feishuService.js` 文件。
    *   在 `feishuService.js` 中添加一行注释：`// 迭代三：实现飞书卡片相关API调用`

**验证:**
*   检查文件系统，确认以上三个文件已在正确的位置创建。
*   重新启动前端开发服务器，确保应用能够正常编译和运行。

---

#### **步骤 2: 评论区组件基础集成**

**目标:** 将迭代一已完成的 `CommentSection` 组件渲染到工单详情抽屉中。

1.  **修改 `OrderDetailDrawer/index.js`:**
    *   在文件顶部，`import` `CommentSection` 组件: `import CommentSection from '@/components/CommentSection';` (路径请根据实际项目结构调整)。
    *   在 `render` 方法的JSX中，找到一个合适的位置（建议在展示工单详情信息的 `Descriptions` 组件之后），添加 `<CommentSection />`。
    *   从 `this.state.orderInfo` 或 `this.props.orderId` 中获取工单ID，并作为 `prop` 传递：`<CommentSection orderId={this.state.orderInfo.order_id} />`。

**验证:**
*   启动应用，打开任意一个工单的详情抽屉。
*   确认可以看到评论区组件（包含输入框和评论列表）。此时它应该可以加载和发表评论，但布局和高级交互尚未实现。

```mermaid
sequenceDiagram
    participant User as 用户
    participant OrderListPage as 工单列表页
    participant OrderDetailDrawer as 工单详情抽屉
    participant CommentSection as 评论区组件
    participant commentService as 评论服务

    User->>OrderListPage: 点击“查看详情”
    OrderListPage->>OrderDetailDrawer: 显示抽屉并传入orderId
    OrderDetailDrawer->>OrderDetailDrawer: 请求并加载工单详情
    OrderDetailDrawer->>CommentSection: 渲染并传入orderId
    CommentSection->>commentService: fetchComments(orderId)
    commentService-->>CommentSection: 返回评论数据
    CommentSection->>OrderDetailDrawer: 显示评论列表
    OrderDetailDrawer->>User: 展示包含评论区的完整抽屉
```

---

#### **步骤 3: 实现固定布局样式**

**目标:** 应用CSS，使评论输入框固定在底部，评论列表区域可滚动。

1.  **修改 `CommentSection/index.js`:**
    *   引入样式文件: `import './styles.css';`
    *   给组件的根 `div` 添加一个 `className`，例如 `className="comment-section-container"`。
2.  **编辑 `CommentSection/styles.css`:**
    *   添加以下CSS规则：
        ```css
        .comment-section-container {
          display: flex;
          flex-direction: column;
          /* 建议设置一个明确的高度或让其父容器提供高度约束 */
          height: 400px; /* 或 flex: 1; 如果父级是flex容器 */
          border-top: 1px solid #f0f0f0;
          margin-top: 16px;
        }

        /* 假设CommentList组件的根元素是 .comment-list-wrapper */
        .comment-list-wrapper {
          flex: 1 1 auto;
          overflow-y: auto; /* 关键：使其可滚动 */
          padding: 8px 16px;
        }

        /* 假设CommentInput组件的根元素是 .comment-input-wrapper */
        .comment-input-wrapper {
          flex-shrink: 0; /* 关键：防止被压缩 */
          padding: 16px;
          border-top: 1px solid #f0f0f0;
        }
        ```
3.  **适配 `CommentList.js` 和 `CommentInput.js`:**
    *   确保 `CommentList` 和 `CommentInput` 组件的根元素分别有 `comment-list-wrapper` 和 `comment-input-wrapper` 的 `className`。

**验证:**
*   刷新页面并打开工单详情。
*   评论区的布局应该变为：输入框固定在底部，当评论内容超出容器高度时，评论列表部分出现滚动条。

---

#### **步骤 4: 实现自动滚动到底部**

**目标:** 当评论列表更新时（如加载完成、新评论提交），视图自动滚动到最新一条评论。

1.  **修改 `CommentList.js`:**
    *   引入 `useRef` 和 `useLayoutEffect` from React。
    *   创建一个 `ref` 用于指向列表的容器元素：`const listContainerRef = useRef(null);`
    *   将此 `ref` 附加到评论列表的滚动容器上：`<div ref={listContainerRef} className="comment-list-wrapper">...</div>`
    *   添加 `useLayoutEffect` Hook：
        ```jsx
        useLayoutEffect(() => {
          if (listContainerRef.current) {
            listContainerRef.current.scrollTop = listContainerRef.current.scrollHeight;
          }
        }, [comments]); // 依赖项是评论数组
        ```

**验证:**
*   打开一个有多条评论的工单详情，列表应自动定位在最底部。
*   发表一条新评论，列表应保持在最底部，新评论立即可见，无需手动滚动。

---

#### **步骤 5: 为懒加载准备状态管理**

**目标:** 在 `CommentSection` 容器组件中添加懒加载所需的状态和逻辑。

1.  **修改 `CommentSection/index.js`:**
    *   在现有的 `useState` 基础上，新增分页相关的状态：
        ```jsx
        const [page, setPage] = useState({ currentPage: 1, hasMore: true });
        const [isLoading, setIsLoading] = useState(false);
        ```
    *   修改初次加载逻辑，使其与新状态集成。
    *   创建 `handleLoadMore` 函数：
        ```jsx
        const handleLoadMore = async () => {
          if (isLoading || !page.hasMore) return; // 防止重复加载

          setIsLoading(true);
          // 假设 commentService.fetchComments 返回 { comments: [], hasMore: boolean }
          const nextPage = page.currentPage + 1;
          const { comments: olderComments, hasMore } = await commentService.fetchComments(orderId, nextPage);

          if (olderComments && olderComments.length > 0) {
            // 将旧评论追加到列表顶部
            setComments(prevComments => [...olderComments, ...prevComments]);
          }
          setPage({ currentPage: nextPage, hasMore });
          setIsLoading(false);
        };
        ```
    *   将 `handleLoadMore`, `page.hasMore`, 和 `isLoading` 作为 `props` 传递给 `<CommentList />`。

**验证:**
*   应用应能正常运行。功能上无可见变化，但 `CommentSection` 组件内部已具备了加载更多数据的能力。可以通过React DevTools检查 `CommentSection` 的 `state` 和 `props` 是否正确。

---

#### **步骤 6: 实现懒加载（无限滚动）**

**目标:** 在 `CommentList.js` 中使用 `IntersectionObserver` 监听滚动，并在到达顶部时触发数据加载。

1.  **修改 `CommentList.js`:**
    *   引入 `useRef`, `useEffect`。
    *   创建一个 `ref` 用于“哨兵”元素：`const sentinelRef = useRef(null);`
    *   在 `useEffect` 中设置 `IntersectionObserver`：
        ```jsx
        useEffect(() => {
          const observer = new IntersectionObserver(
            (entries) => {
              if (entries[0].isIntersecting && hasMore) {
                onLoadMore();
              }
            },
            { root: listContainerRef.current } // 使用列表容器作为观察的根
          );

          const currentSentinel = sentinelRef.current;
          if (currentSentinel) {
            observer.observe(currentSentinel);
          }

          return () => {
            if (currentSentinel) {
              observer.unobserve(currentSentinel);
            }
          };
        }, [onLoadMore, hasMore]); // 依赖项
        ```
    *   在JSX的列表顶部渲染“哨兵”元素和加载指示器：
        ```jsx
        return (
          <div ref={listContainerRef} className="comment-list-wrapper">
            {hasMore && <div ref={sentinelRef} style={{ height: '1px' }} />}
            {isLoading && <div>Loading more...</div>}
            {/* ... 渲染评论列表 ... */}
          </div>
        );
        ```

**验证:**
*   打开一个评论数量超过一页的工单。
*   向上滚动评论列表。
*   当滚动到最顶部时，应看到 "Loading more..." 提示。
*   检查浏览器的网络(Network)标签页，应有新的 `GET /api/v1/orders/{orderId}/comments?page=2` 请求发出。
*   请求成功后，更早的评论被加载并显示在列表顶部。

---

#### **步骤 7: 定义飞书卡片数据模型**

**目标:** 完成 `models/feishuCard.js` 文件的内容，为后续迭代提供清晰的数据结构契约。

1.  **编辑 `models/feishuCard.js`:**
    *   用JSDoc或TypeScript类型定义 `StatefulCard`, `EventCard` 等数据结构，如详细设计文档中所示。
        ```javascript
        /**
         * @typedef {object} FeishuStatefulCard
         * @property {'StatefulCard'} card_type
         * @property {object} header
         * @property {CommentDisplayArea} comment_display_area
         * @property {CommentInputArea} comment_input_area
         */

        /**
         * @typedef {object} CommentDisplayArea
         * @property {'comment_display'} component_type
         * @property {Array<object>} comments
         */
        // ... 其他定义
        ```

**验证:**
*   打开 `src/models/feishuCard.js` 文件，确认其中包含完整的JSDoc类型定义。
*   应用可正常运行，此步骤为纯代码结构优化，不影响功能。

---

至此，前端迭代二的全部开发步骤已制定完毕。请按顺序执行，每一步完成后进行验证。