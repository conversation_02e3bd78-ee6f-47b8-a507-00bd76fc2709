# 前端迭代二：详细设计文档

## 项目结构与总体设计

我们将延续迭代一的**容器/展示组件分离**模式。所有新增的体验优化逻辑将被优雅地封装在现有组件内部，而为飞书功能所做的准备工作将体现为新的、独立的模型定义文件，确保了对现有稳定结构的最小侵入。

### 目录结构

目录结构保持完整性，包含了为未来迭代（3和4）预留的占位文件。迭代二将重点实现`styles.css`并引入新的`models`目录。

```
src/
├── components/
│   └── CommentSection/
│       ├── index.js           # 评论区主容器组件 (迭代1已实现, 无需变更)
│       ├── CommentList.js     # 评论列表展示组件 (迭代2增强)
│       ├── CommentInput.js    # 评论输入区展示组件 (迭代1已实现, 无需变更)
│       └── styles.css         # 评论区专属样式 (迭代2实现)
├── models/                    # (新增) 全局数据模型目录
│   └── feishuCard.js        # (新增) 飞书卡片数据结构定义 (迭代2实现)
├── services/
│   ├── commentService.js      # 封装评论API请求 (迭代1已实现, 无需变更)
│   └── feishuService.js     # (新增) 封装飞书卡片相关API (迭代3再实现, 用固定返回值占位)
└── hooks/                     # (新增) 可复用Hooks目录
    └── useIntersectionObserver.js # (新增) 封装IntersectionObserver逻辑 (迭代3再实现, 迭代2逻辑暂时内联在组件中)
```

### 整体逻辑和交互时序图

此图聚焦于迭代二的核心功能：**懒加载评论**。它展示了用户向上滚动时，`CommentList`组件如何通过`IntersectionObserver`触发父容器`CommentSection`的数据加载流程。

```mermaid
sequenceDiagram
    participant User as 用户
    participant CL as components/CommentList.js
    participant CS as components/CommentSection/index.js
    participant API as services/commentService.js

    User->>CL: 向上滚动评论列表
    Note over CL: 列表顶部的"哨兵"元素进入视口
    CL->>CL: IntersectionObserver触发回调
    CL->>CS: 调用 props.onLoadMore()

    activate CS
    CS->>CS: 检查是否正在加载或已无更多数据
    Note over CS: if (isLoading || !hasMore) return;
    CS->>CS: 设置 state.isLoading = true
    CS->>API: fetchComments(orderId, nextPage)
    API-->>CS: 返回更早的评论和分页信息
    CS->>CS: 更新 state: comments, page, hasMore, isLoading = false
    deactivate CS

    Note over CL, CS: React根据新state自动重新渲染UI, 新评论追加到列表顶部
```

## API接口定义

迭代二不涉及任何新的API接口或对现有接口的修改。我们将继续复用迭代一中已定义的接口：
*   `GET /api/v1/orders/{orderId}/comments`
*   `POST /api/v1/orders/{orderId}/comments`

## 数据实体结构深化

我们在迭代一的数据实体基础上，新增飞书卡片的数据模型定义。

```mermaid
erDiagram
    STATEFUL_CARD {
        string card_type "PK, 'StatefulCard'"
        object header
        object comment_display_area
        object comment_input_area
    }
    EVENT_CARD {
        string card_type "PK, 'EventCard'"
        object header
        object content_area
    }
    COMMENT_DISPLAY_AREA {
        string component_type "'comment_display'"
        array comments
    }
    COMMENT_INPUT_AREA {
        string component_type "'comment_input'"
        string placeholder_text
    }

    STATEFUL_CARD ||--|{ COMMENT_DISPLAY_AREA : "contains"
    STATEFUL_CARD ||--|{ COMMENT_INPUT_AREA : "contains"

```
*   **StatefulCard (长生命周期卡片):** 用于持续交互的卡片，必须包含评论展示区和输入区。
*   **EventCard (瞬时通知卡片):** 用于一次性通知的卡片。
*   **CommentDisplayArea / CommentInputArea:** 作为卡片内的标准化组件模块。

## 模块化文件详解 (File-by-File Breakdown)

### `components/CommentSection/CommentList.js`

a. **文件用途说明**
(增强) 在迭代一的基础上，此组件新增了**懒加载**和**自动滚动到底部**的交互逻辑。

b. **组件接口图 (Props & Internal Refs)**
```mermaid
classDiagram
    class CommentList {
        <<Presentational Component>>
        +Props
        Comment[] comments
        boolean hasMore
        boolean isLoading
        Function onLoadMore
        +Internal
        Ref sentinelRef
        Ref listContainerRef
        +Lifecycle/Effects
        useEffect(onLoadMore)
        useLayoutEffect(comments)
    }
```

c. 对于每个函数/方法，提供以下信息：

#### **函数/方法详解**

##### **`useLayoutEffect` (自动滚动到底部 - 2.1.2)**
- **用途:** 在评论列表（`props.comments`）发生变化后，自动将视图滚动到列表最底部，确保用户能立刻看到最新评论。
- **输入参数:** 依赖项数组 `[props.comments]`
- **输出数据结构:** 无 (DOM副作用)
- **实现流程:**
    ```mermaid
    flowchart TD
        A[props.comments 更新] --> B[useLayoutEffect 触发];
        B --> C["获取 listContainerRef.current DOM节点"];
        C --> D{节点是否存在?};
        D -- 是 --> E["设置节点的 scrollTop = scrollHeight"];
        D -- 否 --> F[结束];
        E --> F;
    ```
    *注: 使用`useLayoutEffect`而非`useEffect`是为了确保在浏览器绘制前完成滚动，避免页面闪烁。*

##### **`useEffect` (懒加载 - 2.1.1)**
- **用途:** 设置`IntersectionObserver`来监听列表顶部的哨兵元素，当其可见时触发`onLoadMore`回调。
- **输入参数:** 依赖项数组 `[props.onLoadMore, props.hasMore]`
- **输出数据结构:** 无 (DOM副作用)
- **实现流程:**
    ```mermaid
    flowchart TD
        A[组件挂载或依赖项变更] --> B(创建 IntersectionObserver);
        B --> C["获取 sentinelRef.current DOM节点"];
        C --> D{哨兵节点是否存在?};
        D -- 是 --> E[Observer 开始观察哨兵节点];
        D -- 否 --> I[结束];
        E --> F[定义Observer回调函数];
        F --> G{哨兵元素是否进入视口 and hasMore为true?};
        G -- 是 --> H[调用 props.onLoadMore()];
        G -- 否 --> I;
        H --> I;
        J[组件卸载时] --> K[Observer 停止观察并断开连接];
        K --> I
    ```

##### **渲染逻辑 (增强)**
- **用途:** 在列表顶部渲染一个不可见的“哨兵”`div`元素，用于懒加载。
- **实现流程:**
    ```mermaid
    flowchart TD
        A[开始渲染] --> B{"hasMore 是否为 true?"};
        B -- 是 --> C["在列表顶部渲染一个 <div ref={sentinelRef}>"];
        B -- 否 --> D[不渲染哨兵元素];
        C --> E[渲染评论列表...];
        D --> E;
    ```

### `components/CommentSection/styles.css`

a. **文件用途说明**
(实现) 此文件为评论区提供专属样式，核心是实现迭代二要求的**输入框固定底部**的布局。

c. **CSS规则详解**
- **用途:** 创建一个flex布局，使评论列表可以自由滚动，而评论输入区固定在底部。
- **实现要点:**
    1.  **父容器 (`<CommentSection>`)**:
        *   `display: flex;`
        *   `flex-direction: column;`
        *   `height: 100%;` (或一个固定的高度)
    2.  **评论列表容器 (`<CommentList>`)**:
        *   `flex: 1 1 auto;` (或 `flex-grow: 1;`) 使其占据所有可用空间。
        *   `overflow-y: auto;` 使其内容超出时可滚动。
    3.  **评论输入区容器 (`<CommentInput>`)**:
        *   `flex-shrink: 0;` 防止其在空间不足时被压缩。
        *   可以添加 `border-top`, `padding` 等样式以优化视觉效果。

### `models/feishuCard.js`

a. **文件用途说明**
(新增) 定义系统内两种飞书卡片的核心数据结构。这是一个纯定义文件，不包含任何逻辑，为迭代三的卡片构建和发送功能提供统一、标准的模型。

c. **数据结构详解**
- **用途:** 以JSDoc或TypeScript类型的方式，明确定义卡片结构。
- **实现要点:**

```javascript
/**
 * @typedef {object} FeishuStatefulCard
 * @property {'StatefulCard'} card_type - 卡片类型标识
 * @property {object} header - 卡片标题
 * @property {CommentDisplayArea} comment_display_area - 评论展示区
 * @property {CommentInputArea} comment_input_area - 评论输入区
 */

/**
 * @typedef {object} FeishuEventCard
 * @property {'EventCard'} card_type - 卡片类型标识
 * @property {object} header - 卡片标题
 * @property {object} content_area - 核心内容区
 */

/**
 * @typedef {object} CommentDisplayArea
 * @property {'comment_display'} component_type
 * @property {Array<Comment>} comments - 最新5条评论
 */

/**
 * @typedef {object} CommentInputArea
 * @property {'comment_input'} component_type
 * @property {string} placeholder_text - 输入框提示文字
 */

// 迭代3再实现: 用固定返回值占位
export function buildStatefulCard(data) { return {}; }
export function buildEventCard(data) { return {}; }
```

### `services/feishuService.js`

a. **文件用途说明**
(占位) 为迭代三准备的API服务层，用于封装所有与飞书卡片更新、发送相关的后端接口调用。

c. **函数/方法详解**
#### **`updateCard(cardId, newCardJson)`**
- **用途:** (迭代3) 更新一个已存在的长生命周期卡片。
- **当前实现:** `// 迭代3再实现，用固定返回值占位`

#### **`sendNotification(userId, cardJson)`**
- **用途:** (迭代3) 向指定用户发送一张瞬时通知卡片。
- **当前实现:** `// 迭代3再实现，用固定返回值占位`


## 迭代演进依据

这份迭代二的设计严格遵循了迭代演进的原则，其优势体现在：

1.  **关注点分离 (Separation of Concerns):**
    *   **交互逻辑 vs 布局:** 懒加载和自动滚动的复杂交互逻辑被完全封装在`CommentList.js`的React Hooks中，而固定的布局则由独立的`styles.css`文件处理。两者解耦，修改其一不影响另一方。
    *   **数据模型 vs 视图:** 新增的`feishuCard.js`将飞书卡片的数据结构与任何React组件分离开。这使得后端和负责迭代三的开发者可以基于这个清晰的契约进行开发，而无需关心当前Web端的实现细节。

2.  **最小化变更:**
    *   迭代二的核心功能增强，仅需修改`CommentList.js`和`styles.css`两个文件。核心的容器组件`CommentSection/index.js`和API服务`commentService.js`完全没有改动，证明了迭代一架构的健壮性。

3.  **为未来铺路 (Paving the Way):**
    *   **预定义模型:** `feishuCard.js`的建立，为迭代三的复杂卡片交互功能提供了稳定的地基，避免了未来在实现功能时才匆忙定义数据结构。
    *   **预留服务层:** 创建`feishuService.js`占位文件，明确了未来飞书相关API的归属，使得项目结构从一开始就保持清晰和完整。当迭代三开始时，开发者能立刻找到正确的位置添加代码。