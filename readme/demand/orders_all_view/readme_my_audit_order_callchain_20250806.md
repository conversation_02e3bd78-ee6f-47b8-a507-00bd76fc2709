# WFO审批工单页面调用链分析

> 分析范围：审批工单页面完整调用逻辑  
> 入口文件：`src/pages/auditOrder/index.js`

## 页面功能概述

审批工单页面是WFO系统的核心功能页面，支持以下功能：
- 工单列表展示（包含工单号、标题、类型、状态等信息）
- 多条件搜索（工单号、标题、日期范围）
- 工单类型筛选（表格列筛选）
- 分页浏览
- 工单详情查看
- 工单审批操作

## 调用链

### 节点1: AuditOrder.componentDidMount
**所在代码文件：** `src/pages/auditOrder/index.js`  
**用途：** 组件挂载后的初始化方法，触发首次数据加载  
**输入参数：** 无  
**输出说明：** 无返回值，触发页面数据加载  

**实现流程：**
```mermaid
flowchart TD
    A[componentDidMount] --> B[调用 requestMyAuditPageOrder]
    B --> C[加载工单列表数据]
    C --> D[提取工单类型筛选选项]
```

### 节点2: AuditOrder.requestMyAuditPageOrder
**所在代码文件：** `src/pages/auditOrder/index.js`  
**用途：** 请求工单列表数据，支持多条件搜索和筛选  
**输入参数：** 无直接参数，使用组件state中的pageNum、pageSize、searchParams  
**输出说明：** 无返回值，更新组件state中的dataSource、total、loading等状态  

**实现流程：**
```mermaid
flowchart TD
    A[requestMyAuditPageOrder] --> B[设置loading=true]
    B --> C[构建request参数]
    C --> D[调用requestMyAuditOrderWithSearch API]
    D --> E{检查响应是否成功}
    E -->|ret===0| F[处理工单数据]
    E -->|ret!==0| G[处理错误情况]
    F --> H[更新工单类型筛选选项]
    H --> I[更新state: dataSource, total]
    G --> J[设置空数据和错误提示]
    I --> K[设置loading=false]
    J --> K
```

### 节点3: requestMyAuditOrderWithSearch
**所在代码文件：** `src/request/api.js`  
**用途：** 封装工单列表查询API请求，支持多条件搜索和筛选  
**输入参数：**
- `params.pageNum`: 页码（数字）
- `params.pageSize`: 每页数量（数字）
- `params.orderId`: 工单号模糊搜索（字符串）
- `params.title`: 工单标题模糊搜索（字符串）
- `params.appliedStartDate`: 申请开始日期（字符串）
- `params.appliedEndDate`: 申请结束日期（字符串）
- `params.orderTypes`: 工单类型筛选（数组）
- `params.filter_by_role`: 角色筛选（数组）

**输出说明：** 返回Promise，包含工单列表数据和分页信息  

**实现流程：**
```mermaid
flowchart TD
    A[requestMyAuditOrderWithSearch] --> B[参数转换和预处理]
    B --> C[调用service.post API]
    C --> D{请求是否成功}
    D -->|成功| E[返回响应数据]
    D -->|失败| F[捕获异常]
    F --> G[返回错误响应结构]
```

### 节点4: service.post
**所在代码文件：** `src/request/index.js`  
**用途：** axios封装的HTTP请求客户端，处理请求和响应拦截  
**输入参数：**
- `url`: API路径（字符串）
- `data`: 请求数据（对象）

**输出说明：** 返回Promise，包含处理后的响应数据  

**实现流程：**
```mermaid
sequenceDiagram
    participant C as Client
    participant I as Request Interceptor
    participant S as Server
    participant R as Response Interceptor
    
    C->>I: 发起POST请求
    I->>I: 添加请求头（Authorization、Content-Type）
    I->>S: 发送HTTP请求
    S->>R: 返回响应
    R->>R: 检查resp_common.ret
    alt ret === 0
        R->>C: 返回response.data
    else ret !== 0
        R->>R: 显示错误消息
        R->>C: 返回null
    end
```

### 节点5: SearchForm
**所在代码文件：** `src/components/SearchForm/index.js`  
**用途：** 搜索表单组件，提供工单号、标题、日期范围搜索功能  
**输入参数：**
- `initialSearchParams`: 初始搜索参数（对象）
- `onSearch`: 搜索回调函数
- `onValidationError`: 校验错误回调函数

**输出说明：** 无直接返回值，通过回调函数传递搜索参数  

**实现流程：**
```mermaid
flowchart TD
    A[用户输入搜索条件] --> B[实时校验输入]
    B --> C{校验是否通过}
    C -->|通过| D[更新组件state]
    C -->|不通过| E[显示错误信息]
    D --> F[用户点击搜索按钮]
    E --> F
    F --> G[执行handleSearchClick]
    G --> H[最终校验所有条件]
    H --> I{校验是否通过}
    I -->|通过| J[调用onSearch回调]
    I -->|不通过| K[调用onValidationError回调]
```

### 节点6: OrderTable
**所在代码文件：** `src/components/OrderTable/index.js`  
**用途：** 工单列表表格组件，展示工单数据并支持筛选和操作  
**输入参数：**
- `dataSource`: 工单数据数组
- `loading`: 加载状态
- `orderTypeFilters`: 工单类型筛选选项
- `selectedRoleTypes`: 已选择的角色类型
- `onViewDetail`: 查看详情回调函数
- `onTableChange`: 表格变化回调函数

**输出说明：** 无直接返回值，通过回调函数传递操作事件  

**实现流程：**
```mermaid
flowchart TD
    A[渲染表格列配置] --> B[显示工单数据]
    B --> C[用户交互]
    C --> D{交互类型}
    D -->|点击详情| E[调用onViewDetail]
    D -->|筛选操作| F[调用onTableChange]
    D -->|排序操作| F
    E --> G[打开工单详情抽屉]
    F --> H[触发数据重新加载]
```

## 整体用途

审批工单页面的调用链实现了一个完整的工单管理系统，主要功能包括：

1. **数据加载与展示**：通过组件初始化触发数据加载，使用API请求获取工单列表数据
2. **多条件搜索**：支持工单号、标题、日期范围等多维度搜索条件
3. **实时校验**：对用户输入进行实时校验，提供友好的错误提示
4. **筛选功能**：支持工单类型和角色类型的多选筛选
5. **分页浏览**：支持大数据量的分页显示和导航
6. **详情查看**：支持工单详情查看和审批操作

## 目录结构

调用链涉及到的文件及其所属的目录结构：

```
src/
├── pages/
│   └── auditOrder/
│       └── index.js                    # 主页面组件
├── components/
│   ├── SearchForm/
│   │   └── index.js                    # 搜索表单组件
│   ├── OrderTable/
│   │   └── index.js                    # 工单表格组件
│   ├── OrderPagination/
│   │   └── index.js                    # 分页组件
│   ├── OrderDetailDrawer/
│   │   └── index.js                    # 工单详情抽屉
│   └── common/
│       └── LoadingSkeleton/
│           └── index.js                    # 加载骨架屏组件
├── request/
│   ├── api.js                          # API接口封装
│   └── index.js                        # HTTP请求封装
└── util/
    ├── strHelper.js                    # 字符串工具函数
    └── timeHelper.js                   # 日期工具函数
```

## 调用时序图

以下是完成一个典型的工单列表加载请求的完整时序图：

```mermaid
sequenceDiagram
    participant U as User
    participant A as src/pages/auditOrder/index.js
    participant S as src/components/SearchForm/index.js
    participant T as src/components/OrderTable/index.js
    participant API as src/request/api.js
    participant HTTP as src/request/index.js
    participant Server as Backend Server
    
    Note over U,Server: 页面初始化加载流程
    U->>A: 访问审批工单页面
    A->>A: componentDidMount()
    A->>A: requestMyAuditPageOrder()
    A->>A: 设置loading=true
    A->>API: requestMyAuditOrderWithSearch(params)
    Note right of API: params: {pageNum:1, pageSize:10, orderId:'', title:'', ...}
    API->>API: 参数转换和预处理
    API->>HTTP: service.post('/order/my-audit', processedParams)
    HTTP->>HTTP: 请求拦截器添加请求头
    HTTP->>Server: HTTP POST 请求
    Server->>HTTP: 返回工单数据
    Note left of Server: {resp_common:{ret:0}, total:100, orders:[...], order_types:[...]}
    HTTP->>HTTP: 响应拦截器检查ret值
    HTTP->>API: 返回response.data
    API->>A: 返回处理后的数据
    A->>A: 处理工单数据和筛选选项
    A->>A: setState({dataSource, total, loading:false})
    A->>T: 传递dataSource到OrderTable
    T->>U: 渲染工单列表
    
    Note over U,Server: 用户搜索操作流程
    U->>S: 输入搜索条件
    S->>S: 实时校验输入
    U->>S: 点击搜索按钮
    S->>S: handleSearchClick()
    S->>S: 最终校验所有条件
    alt 校验通过
        S->>A: onSearch(searchParams)
        A->>A: handleSearch(searchParams)
        A->>A: 更新searchParams和pageNum=1
        A->>A: requestMyAuditPageOrder()
        Note right of A: 重复上面的API请求流程
    else 校验失败
        S->>A: onValidationError(errorMsg)
        A->>A: handleValidationError(errorMsg)
        A->>U: 显示全局错误提示
    end
    
    Note over U,Server: 表格筛选操作流程
    U->>T: 点击工单类型筛选
    T->>A: onTableChange(pagination, filters, sorter)
    A->>A: handleTableChange()
    A->>A: 更新searchParams.orderTypes
    A->>A: requestMyAuditPageOrder()
    Note right of A: 重复上面的API请求流程
    
    Note over U,Server: 查看工单详情流程
    U->>T: 点击详情按钮
    T->>A: onViewDetail(orderId, orderType)
    A->>A: handleViewDetail()
    A->>A: setState({selectedOrderId, selectedOrderType})
    A->>U: 渲染OrderDetailDrawer组件
```

## 页面展示情况和支持功能

### 页面布局结构

审批工单页面采用垂直布局结构，从上到下分为以下区域：

1. **搜索表单区域** - 位于页面顶部，提供多条件搜索功能
2. **表格展示区域** - 主体区域，显示工单列表数据
3. **分页导航区域** - 位于页面底部，提供分页功能

### 搜索表单功能

**支持的搜索条件：**
- **工单号搜索**：支持模糊匹配，最多50个字符
- **工单标题搜索**：支持模糊匹配，最多100个字符
- **申请日期范围**：支持开始日期和结束日期选择，支持时分秒精确搜索

**校验机制：**
- 实时输入校验，显示字符计数和错误提示
- 日期范围校验，防止结束日期早于开始日期
- 至少填写一个搜索条件的限制
- 校验失败时显示全局错误提示

### 工单列表显示

**表格列信息：**
- **单号**：显示工单ID，带有角色标签（待审/将审/已审/完结/抄送）
- **工单标题**：支持文本截断和悬浮提示
- **工单类型**：支持列筛选功能
- **总节点数**：工单审批流程的总阶段数
- **当前节点**：当前审批阶段
- **工单状态**：工单当前状态描述
- **申请人**：工单发起人邮箱
- **运维负责人**：运维负责人邮箱
- **申请时间**：工单创建时间
- **操作**：提供“详情”按钮

**筛选功能：**
- **工单类型筛选**：集成在表格列头，支持多选筛选
- **角色类型筛选**：支持按审批角色筛选（待审/将审/已审/完结/抄送）
- **后端筛选**：所有筛选操作都通过后端 API 实现，不受前端分页限制

### 加载和交互状态

**加载状态：**
- 数据加载时显示骨架屏动画
- 搜索和筛选操作时显示加载状态

**错误处理：**
- API 请求失败时显示错误提示
- 校验错误时显示全局警告消息
- 筛选状态保持，网络错误不影响用户选择

### 分页功能

**支持的分页操作：**
- 页码跳转：支持直接跳转到指定页码
- 每页数量调整：支持 10/20/30/50/100 条每页
- 分页信息显示：显示当前页码、总页数、总记录数

**分页交互逻辑：**
- 搜索时自动重置到第一页
- 筛选时自动重置到第一页
- 每页数量变更时重置到第一页

### 工单详情功能

**详情查看：**
- 点击表格中的“详情”按钮打开详情抽屉
- 显示工单完整信息和审批流程
- 支持工单备注编辑和评论功能

**审批操作：**
- 仅当前审批人可见审批按钮（同意/驳回）
- 支持审批人变更功能
- 审批操作后自动关闭详情页并显示操作结果

### 数据更新机制

**自动刷新场景：**
- 页面初始化加载
- 用户搜索操作
- 筛选条件变更
- 分页操作
- 审批操作完成后

**数据一致性保证：**
- 工单类型筛选选项从后端实时获取
- 每次搜索都更新筛选选项，保证数据同步
- 支持容错机制，网络异常时使用本地模拟数据
