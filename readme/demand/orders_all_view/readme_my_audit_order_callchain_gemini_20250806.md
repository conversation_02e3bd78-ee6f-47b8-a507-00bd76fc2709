
## 整体用途

该调用链完整地实现了“我的审批”工单页面的核心功能。整个流程始于`AuditOrder`页面组件的加载，它首先通过`requestMyAuditPageOrder`方法向后端请求需要当前用户审批的工单列表。用户的交互（搜索、筛选、分页）会触发新的数据请求，从而更新页面展示。

具体来说，其功能包括：
1.  **数据显示与渲染**：`AuditOrder`获取数据后，传递给`OrderTable`进行表格化展示，并通过`OrderPagination`组件实现分页控制。
2.  **搜索与查询**：`SearchForm`组件提供了按工单号、标题和申请日期的搜索功能。用户的输入会通过回调函数传递给`AuditOrder`，并作为参数用于API请求。
3.  **数据筛选**：`OrderTable`组件内置了基于工单类型和用户角色的筛选功能。用户的筛选操作同样会触发`AuditOrder`重新请求数据。
4.  **API通信**：所有的后端通信都由`src/request/api.js`中的`requestMyAuditOrderWithSearch`函数发起，该函数依赖于`src/request/index.js`中封装的`axios`实例`service`来执行实际的HTTP请求和统一的响应处理。
5.  **详情查看**：用户可以点击每行工单的“详情”按钮，`OrderDetailDrawer`组件会滑出，展示该工单的详细信息。

总而言之，这是一个典型的React数据驱动页面的实现，通过组件化将UI、状态管理和业务逻辑解耦，实现了清晰、可维护的代码结构。

## 目录结构

```
src/
├───pages/
│   └───auditOrder/
│       └───index.js          # 页面入口和主容器组件
├───components/
│   ├───SearchForm/
│   │   └───index.js          # 搜索表单组件
│   ├───OrderTable/
│   │   └───index.js          # 工单表格组件
│   ├───OrderPagination/
│   │   └───index.js          # 分页组件
│   ├───OrderDetailDrawer/
│   │   └───index.js          # 工单详情抽屉组件
│   └───RoleTag/
│       └───AuditRoleTagCell.js # 审批角色标签单元格组件
├───request/
│   ├───api.js                # API请求函数定义
│   └───index.js              # axios实例封装和拦截器配置
└───util/
    ├───strHelper.js          # 字符串处理工具
    └───timeHelper.js         # 时间处理工具
```

## 调用时序图

```mermaid
sequenceDiagram
    participant User
    participant SearchForm as src/components/SearchForm/index.js
    participant AuditOrder as src/pages/auditOrder/index.js
    participant OrderTable as src/components/OrderTable/index.js
    participant OrderPagination as src/components/OrderPagination/index.js
    participant API as src/request/api.js
    participant Axios as src/request/index.js
    participant Backend

    Note over AuditOrder: 页面加载 (componentDidMount)
    AuditOrder->>API: requestMyAuditPageOrder()
    API->>Axios: requestMyAuditOrderWithSearch({ pageNum: 1, pageSize: 10, ... })
    Axios->>Backend: POST /order/my-audit
    Backend-->>Axios: { resp_common, orders, total }
    Axios-->>API: response.data
    API-->>AuditOrder: returns response
    AuditOrder->>AuditOrder: setState({ dataSource, total, loading: false })
    AuditOrder->>OrderTable: re-render with dataSource
    AuditOrder->>OrderPagination: re-render with total

    Note over User: 用户输入搜索条件并点击搜索
    User->>SearchForm: 输入'工单123'
    User->>SearchForm: 点击 '搜索'
    SearchForm->>AuditOrder: onSearch({ orderId: '工单123', ... })
    AuditOrder->>API: requestMyAuditPageOrder()
    API->>Axios: requestMyAuditOrderWithSearch({ orderId: '工单123', ... })
    Axios->>Backend: POST /order/my-audit
    Backend-->>Axios: Filtered Data
    Axios-->>API: response.data
    API-->>AuditOrder: returns response
    AuditOrder->>AuditOrder: setState({ dataSource, total, ... })
    AuditOrder->>OrderTable: re-render with new dataSource

    Note over User: 用户点击表格筛选
    User->>OrderTable: 选择 '工单类型' 筛选
    OrderTable->>AuditOrder: onTableChange(pagination, { orderType: ['common'] }, sorter)
    AuditOrder->>API: requestMyAuditPageOrder()
    API->>Axios: requestMyAuditOrderWithSearch({ orderTypes: ['common'], ... })
    Axios->>Backend: POST /order/my-audit
    Backend-->>Axios: Filtered Data
    Axios-->>API: response.data
    API-->>AuditOrder: returns response
    AuditOrder->>AuditOrder: setState({ dataSource, total, ... })
    AuditOrder->>OrderTable: re-render with new dataSource

    Note over User: 用户点击分页
    User->>OrderPagination: 点击第 2 页
    OrderPagination->>AuditOrder: onPageChange(2)
    AuditOrder->>AuditOrder: setState({ pageNum: 2 })
    AuditOrder->>API: requestMyAuditPageOrder()
    API->>Axios: requestMyAuditOrderWithSearch({ pageNum: 2, ... })
    Axios->>Backend: POST /order/my-audit
    Backend-->>Axios: Page 2 Data
    Axios-->>API: response.data
    API-->>AuditOrder: returns response
    AuditOrder->>AuditOrder: setState({ dataSource, total, ... })
    AuditOrder->>OrderTable: re-render with new dataSource

```
