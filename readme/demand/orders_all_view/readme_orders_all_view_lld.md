# **工单管理模块前端详细设计 (V1.6 最终实现版)**

## **1. 项目结构与总体设计**

### **1.1 设计哲学与核心策略**
*   **职责分离(SoC):** 严格划分页面、组件和服务的职责。页面负责状态管理与逻辑编排；组件负责UI渲染与用户交互；服务层负责API调用与业务数据处理。
*   **状态管理:** 采用`pendingFilters`（准备中）和`appliedFilters`（已生效）分离的状态模型，精确映射PRD定义的复杂交互逻辑。
*   **组件化:** 新建专用的业务组件（如`ManageOrderTable`、`FilterTagList`），并封装复杂逻辑到自定义Hook中，避免过早的通用化抽象，同时保持主页面逻辑清晰。
*   **迭代演进:** 现有“我的审批工单”页面及其组件（`src/components/OrderTable/index.js`）保持不变，新功能与现有功能物理隔离，确保低风险、可控的迭代开发。

## **2. 目录结构**
```
src/
├── pages/
│   ├── auditOrder/                 # (不变) 现有审批工单页面
│   │   └── index.js
│   └── manageOrder/                # (新增) 工单管理页面目录
│       ├── components/
│       │   ├── ManageOrderTable.js # (新增) 工单管理页面专属表格组件
│       │   ├── FilterTagList.js    # (新增) 筛选条件标签列表组件
│       │   ├── AdvancedFilterModal.js # (新增) 高级筛选弹窗组件
│       │   └── ReportDashboard.js  # (迭代3再实现) 报表仪表盘，用固定返回值占位
│       └── index.js                # (新增) 工单管理页面主入口
├── components/
│   ├── OrderTable/
│   │   └── index.js                # (不变) “我的审批工单”页面使用的表格
│   ├── OrderPagination/
│   │   └── index.js                # (复用) 通用分页组件
│   ├── OrderDetailDrawer/
│   │   └── index.js                # (复用) 工单详情抽屉
│   └── SearchForm/          # (复用) 默认筛选区表单
│       └── index.js
├── services/
│   ├── order_service.js            # (新增) 封装工单相关的业务逻辑与API调用
│   └── permission_service.js       # (新增) 封装权限相关的API调用
├── request/
│   ├── api.js                      # (修改) 新增API接口定义
│   └── index.js                    # (不变) axios封装实例
├── constants/
│   └── order_constants.js          # (新增) 工单相关常量
├── hooks/
│   └── useAdvancedFilter.js        # (新增) 封装高级筛选弹窗逻辑的自定义Hook
└── util/
    └── slaHelper.js                # (新增) SLA高亮逻辑辅助函数
```

## **3. 整体逻辑和交互时序图**

### **3.1 核心交互流程**
页面加载时，首先检查用户权限。通过后，获取初始工单列表和筛选选项。用户的操作被分为“筛选指令”和“排序指令”。筛选指令通过`pendingFilters`和`appliedFilters`状态管理，由【搜索】按钮触发。排序指令则根据`isEqual(appliedFilters, pendingFilters)`的结果决定是直接请求还是弹窗确认。

### **3.2 时序图 (Mermaid Sequence Diagram)**
```mermaid
sequenceDiagram
    participant User
    participant Page as src/pages/manageOrder/index.js
    participant API as src/request/api.js
    participant Service as src/services/order_service.js

    User->>Page: 访问工单管理页面
    Page->>API: requestManagementPermission()
    API-->>Page: { has_permission: true }
    Page->>Page: componentDidMount() / useEffect()
    Page->>Service: getManageOrders(initialParams)
    Service->>API: requestManageOrder(initialParams)
    API-->>Service: GetManageOrdersResp (含orders, total, available_options)
    Service-->>Page: 返回格式化后的数据
    Page->>Page: setState({ appliedFilters, pendingFilters, dataSource, options })
    Page-->>User: 渲染初始列表和筛选区

    User->>Page: 修改筛选条件 (e.g., 输入单号)
    Page->>Page: 更新 state.pendingFilters
    Page-->>User: 筛选条件展示区出现灰色标签

    User->>Page: 点击【搜索】按钮
    Page->>Service: getManageOrders(state.pendingFilters)
    Service->>API: requestManageOrder(state.pendingFilters)
    API-->>Service: 返回筛选后的数据
    Service-->>Page: 更新列表数据和下拉选项
    Page->>Page: setState({ appliedFilters = pendingFilters, dataSource, options })
    Page-->>User: 标签变蓝，列表更新

    User->>Page: 点击可排序列的表头
    Page->>Page: 检查 !isEqual(appliedFilters, pendingFilters)
    alt 条件为 true (有未提交的筛选)
        Page-->>User: 弹出确认框
        User->>Page: 点击【确认】
        Page->>Page: 触发“搜索并排序”流程
    else 条件为 false
        Page->>Service: getManageOrders({ ...appliedFilters, ...newSorter })
        Service->>API: requestManageOrder(...)
        API-->>Service: 返回排序后的数据
        Service-->>Page: 更新列表数据
        Page->>Page: setState({ dataSource })
        Page-->>User: 列表按新顺序刷新
    end
```
## **4. API接口定义**

| 请求方法 | 路径 | 简要说明 |
| :--- | :--- | :--- |
| `GET` | `/order/permissions/management-order` | 检查当前用户是否有工单管理页的查看权限。 |
| `POST` | `/order/management-order` | 获取工单管理列表数据，支持复杂筛选、排序和分页。 |


## **5. 数据实体与枚举**

### **5.1 后端请求与响应体 (Protobuf Message)**

#### **`CheckManagementPermissionReq` & `CheckManagementPermissionResp`**
```protobuf
// 工单管理权限检查请求
message CheckManagementPermissionReq {}

// 工单管理权限检查响应
message CheckManagementPermissionResp {
    bool has_permission = 1;
}
```
#### **`GetManageOrdersReq`**
```protobuf
message GetManageOrdersReq {
    int32 page = 1;
    int32 page_size = 2;
    // 默认筛选区
    string order_id = 3;
    string title = 4;
    string applied_start_date = 5; // 格式: "YYYY-MM-DD HH:mm:ss"
    string applied_end_date = 6;   // 格式: "YYYY-MM-DD HH:mm:ss"
    
    // 高级筛选区
    repeated string order_types = 7;
    repeated string statuses = 8; // "审批中", "已完结", "已驳回"
    repeated string applicants = 9; // 申请人邮箱列表
    repeated string ops_leads = 10;   // 运维负责人邮箱列表
    repeated string cc_list = 11;     // 抄送人邮箱列表
    
    // 耗时筛选 (枚举值或字符串)
    string total_duration_filter = 12; 
    string node_stay_duration_filter = 13; 

    // 排序
    string sort_by = 14;
    string sort_order = 15; // "asc", "desc"
}
```

#### **`ManagedOrder` & `GetManageOrdersResp`**
```protobuf
// 用于工单管理列表的单个工单信息
message ManagedOrder {
    string order_id = 1;
    string title = 2;
    string order_type = 3;
    string status = 4;
    int32 total_nodes = 5;
    string current_node = 6;             // 格式: 当前阶段编号/当前阶段名称 (e.g., '3/财务审批')
    string applicant = 7;
    string ops_lead_email = 8;
    repeated string cc_list = 9;
    string apply_time = 10;
    string latest_update_time = 11;
    string total_duration_display = 12;           // 用于前端直接展示的耗时字符串, e.g., "2天3小时" or "-"
    string current_node_stay_duration_display = 13; // e.g., "1天2小时" or "-"
    string sla_level = 14;               // "normal", "warning", "critical"
}

// GetManageOrders的响应体
message GetManageOrdersResp {
    repeated ManagedOrder orders = 1;
    int32 total = 2;
    // --- 用于高级筛选弹窗的下拉选项 ---
    repeated string available_order_types = 3;
    repeated string available_applicants = 4;
    repeated string available_ops_leads = 5;
    repeated string available_cc_users = 6;
}
```

### **5.2 前后端交互枚举值**

| 字段名 | 前端传入值 | 后端处理/说明 |
| :--- | :--- | :--- |
| `statuses` | `审批中`, `已完结`, `已驳回` | 映射为`tb_order.result`的`0, 1, 2` |
| `total_duration_filter` | `over_1d`, `over_3d`, `over_1w` | 转换为秒数比较 |
| `node_stay_duration_filter` | `over_12h`, `over_24h`, `over_3d`| 转换为秒数比较 |
| `sort_by` | `apply_time`, `latest_update_time`, `total_duration`, `current_node_stay_duration` | 映射为数据库列名或计算列别名 |
| `sort_order` | `asc`, `desc` | 直接用于`ORDER BY`子句 |

## **6. 模块化文件详解**

### **6.1 `src/services/permission_service.js`**
**a. 文件用途说明:**
新增的服务层，专门用于封装权限相关的API调用。
**c. 函数/方法详解:**
#### `checkManagementPermission()`
- **用途:** 检查用户是否拥有工单管理页权限。
- **输入参数:** 无。
- **输出数据结构:** `Promise<boolean>` - 返回 `true` 或 `false`。
- **实现流程:**
  ```mermaid
  flowchart TD
      A[开始] --> B["调用 api.requestManagementPermission()"]
      B --> C{请求成功且ret===0?}
      C --"是"--> D["返回 response.data.has_permission"]
      C --"否"--> F["记录错误日志"]
      F --> G["返回 false"]
  ```

### **6.2 `src/services/order_service.js`**
**a. 文件用途说明:**
封装与工单管理相关的业务逻辑，将页面组件与原始的API调用解耦。
**c. 函数/方法详解:**
#### `getManageOrders(params)`
- **用途:** 获取并处理工单管理列表的数据。
- **输入参数:**
    - `params` (Object): 对应 `GetManageOrdersReq` 结构的对象。
- **输出数据结构:** `Promise<Object>`，返回 `{ success: true, data: GetManageOrdersResp }` 或 `{ success: false, message: '...' }`。
- **实现流程:**
  ```mermaid
  flowchart TD
      A["接收 params"] --> B["调用 api.requestManageOrder(params)"]
      B --> C{API响应是否成功且ret===0?}
      C --"是"--> D["返回 { success: true, data: response.data }"]
      C --"否"--> E["记录错误日志"]
      E --> F["返回 { success: false, message: &quot;获取失败&quot; }"]
  ```
### **6.3 `src/pages/manageOrder/index.js`**
**a. 文件用途说明:**
工单管理页面的主容器，核心状态管理与逻辑编排中心。
**b. 文件内类图 (或核心状态与方法):**
```mermaid
classDiagram
    class ManageOrderPage {
        -state_hasPermission: boolean
        -state_isLoading: boolean
        -state_dataSource: ManagedOrder[]
        -state_pagination: object
        -state_sorter: object
        -state_availableOptions: object
        -state_pendingFilters: object
        -state_appliedFilters: object

        +componentDidMount()
        +handleSearch()
        +handleReset()
        +handleTableChange()
        +handleFilterTagsChange()
        +openAdvancedFilterModal()
        +render()
    }

```
**c. 函数/方法详解:**
#### `handleTableChange(pagination, filters, sorter)`
- **用途:** 处理表格的排序变化。
- **输入参数:** Ant Design Table `onChange` 提供的参数。
- **输出数据结构:** 无 (触发state更新和API调用)。
- **实现流程:**
  ```mermaid
  flowchart TD
      A[开始] --> B["从 this.state 获取 pendingFilters 和 appliedFilters"]
      B --> C["调用 isEqual(pendingFilters, appliedFilters)"]
      C --> D{结果是否为 false?}
      D --"是 (不相等)"--> E["弹出确认框：&quot;您有未提交的筛选条件...&quot;"]
      E --"确认"--> F["执行 handleSearch(newSorter)"]
      E --"取消"--> G["结束"]
      D --"否 (相等)"--> H["直接使用 appliedFilters 和 newSorter 调用API获取数据"]
      F & H --> I["更新列表"]
  ```

### **6.4 `src/pages/manageOrder/components/ManageOrderTable.js`**
**a. 文件用途说明:**
新增的、工单管理页面专属的表格组件。其列定义（columns）硬编码于组件内部。
**c. 函数/方法详解:**
#### `ManageOrderTable(props)`
- **用途:** 渲染工单管理页的列表。
- **输入参数:**
    - `loading` (Boolean): 控制骨架屏。
    - `dataSource` (Array[`ManagedOrder`]): 工单数据。
    - `pagination` (Object): 分页器配置。
    - `sorter` (Object): 当前排序规则，用于高亮表头。
    - `onTableChange` (Function): 分页或排序时的回调。
- **输出数据结构:** JSX
- **实现流程:**
  ```mermaid
  flowchart TD
      subgraph getColumns
          direction LR
          A1[开始] --> B1["定义'单号'列, 并通过 render 函数添加状态标签"]
          B1 --> C1["定义'抄送人'列, 在 render 函数中处理截断和Tooltip(分隔符用逗号)"]
          C1 --> D1["定义支持排序的各列, 设置 sorter:true 和 sortOrder"]
          D1 --> E1["返回完整的 columns 数组"]
      end
      subgraph main
          A[接收 props] --> B["调用内部的 getColumns() 函数获取列配置"]
          B --> C["调用 slaHelper.getRowClassName 获取行样式函数"]
          C --> D["将 props, columns, rowClassName 传递给 antd 的 Table 组件"]
          D --> E["渲染表格"]
      end
  ```
### **6.5 `src/pages/manageOrder/components/FilterTagList.js`**
**a. 文件用途说明:**
新增的UI组件，负责渲染可交互的筛选条件标签列表，支持智能折叠与展开。
**c. 函数/方法详解:**
#### `FilterTagList(props)`
- **用途:** 根据 `pendingFilters` 和 `appliedFilters` 渲染灰色和蓝色的标签。
- **输入参数:**
    - `pendingFilters` (Object): 准备中的筛选条件。
    - `appliedFilters` (Object): 已生效的筛选条件。
    - `onRemoveTag` (Function): 移除某个字段筛选的回调。 `onRemoveTag(fieldKey)`
    - `onRemoveMultiSelectItem` (Function): 移除折叠项中某个子选项的回调。 `onRemoveMultiSelectItem(fieldKey, itemValue)`
- **输出数据结构:** JSX
- **实现流程 (以多选标签为例):**
  ```mermaid
  flowchart TD
      A["接收 props"] --> B["根据 appliedFilters 和 pendingFilters 的差异，确定各字段颜色"]
      B --> C["遍历各字段，生成标签"]
      C --> D{字段是否为多选项且超出宽度?}
      D --"是"--> E["渲染为可折叠标签，带[展开]按钮和一键移除的'x'"]
      D --"否"--> F["渲染为普通标签，带'x'"]
      E --> G["用户点击[展开]"]
      G --> H["渲染所有子选项，每个子选项带自己的'x'"]
      H --> I["用户点击子选项的'x'"]
      I --> J["调用 props.onRemoveMultiSelectItem"]
  ```

### **6.6 `src/hooks/useAdvancedFilter.js`**
**a. 文件用途说明:**
新增的自定义Hook，用于封装高级筛选弹窗的复杂逻辑，如状态管理、值变化判断等，使页面主组件更简洁。
**b. 文件内类图 (或核心状态与方法):**
```mermaid
classDiagram
    class useAdvancedFilter {
        <<hook>>
        -isModalVisible: boolean
        -initialValues: object
        +openModal(currentFilters)
        +handleOk(newValues)
        +handleCancel()
        +modalProps: object
    }
```
**c. 函数/方法详解:**
#### `openModal(currentFilters)`
- **用途:** 打开高级筛选弹窗，并记录打开时的筛选状态。
- **输入参数:**
    - `currentFilters` (Object): 当前的筛选条件值。
- **输出数据结构:** 无。
- **实现流程:**
  ```mermaid
  flowchart TD
      A[开始] --> B["使用 lodash.cloneDeep 深拷贝 currentFilters 到 state.initialValues"]
      B --> C["设置 isModalVisible = true"]
  ```
#### `handleOk(newValues)`
- **用途:** 点击弹窗【确定】按钮时，判断值是否变化，并回调给父组件。
- **输入参数:**
    - `newValues` (Object): 弹窗表单的当前值。
- **输出数据结构:** 无 (通过回调函数通信)。
- **实现流程:**
  ```mermaid
  flowchart TD
      A[开始] --> B["使用 lodash.isEqual 判断 isEqual(newValues, state.initialValues)"]
      B --> C{结果是否为 false?}
      C --"是 (有变化)"--> D["调用父组件传入的 onOk(newValues) 回调"]
      C --"否 (无变化)"--> E["不执行任何操作"]
      D & E --> F["设置 isModalVisible = false"]
  ```
### **6.7 `src/util/slaHelper.js`**
**a. 文件用途说明:**
新增的辅助函数文件，封装SLA高亮逻辑。
**c. 函数/方法详解:**
#### `getRowClassName(record)`
- **用途:** 根据后端返回的 `sla_level` 字段，返回对应的CSS类名。
- **输入参数:** `record` (ManagedOrder)
- **输出数据结构:** `String` - CSS类名，如 `'sla-critical'`, `'sla-warning'`, 或 `''`。
- **实现流程:**
  ```mermaid
  flowchart TD
      A[开始] --> B["获取 record.sla_level"]
      B --> C{sla_level 的值是?}
      C --"critical"--> D["返回 &quot;sla-critical&quot;"]
      C --"warning"--> E["返回 &quot;sla-warning&quot;"]
      C --"其他"--> F["返回 &quot;&quot;"]
  ```
## **7. 迭代演进依据**

1.  **最小化影响范围:** 本次设计严格遵守了“开放封闭原则”。通过新建页面、组件和服务，而**非修改**现有审批工单的逻辑，确保了新功能的开发不会对老功能产生任何回归风险。
2.  **按需抽象，避免过度设计:** 我们明确了`ManageOrderTable`是专属组件，其`columns`硬编码在内部，这避免了不必要的、过早的通用化抽象。这种务实的做法降低了当前迭代的复杂性，同时为未来真正的通用化需求保留了可能性。
3.  **逻辑封装与复用:** 复杂的弹窗交互逻辑被封装在`useAdvancedFilter` Hook中，复杂的标签展示逻辑被封装在`FilterTagList`组件中。这使得主页面`index.js`的逻辑非常清晰，只负责“做什么”（编排流程），而不关心“怎么做”（UI细节），极大地提高了代码的可读性和可维护性。
4.  **清晰的演进路径:** `ReportDashboard.js`的占位文件明确了系统的下一步发展方向。当需要开发报表功能时，团队可以无缝地在此基础上进行扩展，而不会对现有工单管理列表造成任何影响。