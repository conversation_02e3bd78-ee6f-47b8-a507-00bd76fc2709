# **产品需求文档：工单管理模块 (V1.6 - 最终交付版)**

## **1. 需求背景与目标**

### **1.1. 需求背景**
当前工单系统以个人视角（如“我申请的”、“我审批的”）为核心进行构建，缺乏一个全局的管理和监督视图。这导致部分具有管理职责的用户（例如部门主管、团队负责人、项目经理等）无法高效、全面地了解团队或系统内所有工单的整体状态、处理效率和潜在瓶颈，从而限制了管理效能和业务流程的透明度。

### **1.2. 目标用户**
本功能模块的核心目标用户，是在系统中同时扮演两种角色的“双重身份用户”：
*   **普通参与者：** 作为员工，他们会亲自提交工单（如请假、报销），或作为审批节点处理他人的工单。
*   **管理者/监督者：** 作为管理者，他们需要超越个人权限，对更广范围（如本部门、全公司）的工单进行监督、跟踪和分析。

### **1.3. 核心价值（本期目标）**
*   **提供全局视图：** 打破以“我”为中心的数据壁垒，为目标用户提供一个统一的、可查看所有授权范围内工单的中心化视图。
*   **主动发现问题：** 通过强大的筛选、排序以及SLA（服务等级协议）高亮功能，帮助管理者从海量数据中快速、直观地发现处理时间过长、可能存在风险的工单，实现从“被动查找”到“主动发现”的管理模式转变。
*   **提供严谨且流畅的操控体验：** 建立一套清晰、可预测且高效的复杂查询交互模型，确保功能强大的同时，用户体验依然出色。

## **2. 功能范围与设计**

### **2.1. 新增功能模块：“工单管理”页面**

#### **2.1.1. 页面入口与权限**
*   **页面名称：** 工单管理
*   **入口位置：** 在系统顶部导航区，作为新的顶级Tab页，与现有的“进行中工单”、“完结工单”、“我的审批工单”等Tab页并列。
*   **访问权限：** 该“工单管理”Tab页仅对被后台配置赋予了“查看所有工单”权限的角色可见。对于无此权限的普通用户，该Tab页应不可见。

#### **2.1.2. 核心交互模型：筛选指令与排序指令分离**
为了在功能的灵活性和交互的清晰性之间取得最佳平衡，整个页面的操作被定义为两种独立的指令类型，它们遵循不同的交互模式。

*   **A. 筛选指令 (Filtering Command)**
    *   **定义：** 任何旨在改变列表所展示“**数据子集**”的操作。所有筛选指令的生效，都由一个统一的【搜索】按钮控制。
    *   **交互流程 (三态系统)：**
        1.  **准备阶段 (灰色状态)：** 用户在**默认筛选区**或**【高级筛选】弹窗**中设置任何筛选条件。系统会立即在**“筛选条件展示区”**生成对应的**灰色**标签，作为待生效条件的视觉预览。在此阶段，系统不执行任何后台查询，列表数据保持不变。用户可点击灰色标签上的“x”图标来移除一个准备中的条件（此移除为纯前端操作，不触发查询）。
        2.  **提交阶段：** 用户完成所有筛选条件的准备后，点击页面上的【搜索】按钮。
        3.  **生效阶段 (蓝色状态)：** 系统根据所有准备中的条件执行后台查询，并刷新列表。查询成功后，“筛选条件展示区”中的所有灰色标签将全部变为**蓝色**，代表这些条件是当前列表数据的生效依据。
        4.  **再编辑阶段：**
            *   **修改或增加条件：** 当页面处于“生效阶段”（有蓝色标签）时，若用户再次修改或增删**任何筛选控件**（如输入框、高级筛选弹窗），所有蓝色标签将**立即全部变回灰色**，系统重新进入“准备阶段”，等待用户下一次点击【搜索】按钮。一旦变灰，此状态不可逆，直到下一次【搜索】或【重置】。
            *   **移除已生效条件：** 当用户点击某个**蓝色**条件标签上的“x”图标时，系统将**立即移除**该筛选条件，并**自动重新执行一次查询**，刷新列表和筛选条件展示区。

*   **B. 排序指令 (Sorting Command)**
    *   **定义：** 任何旨在改变当前已展示数据子集的“**排列顺序**”的操作。排序指令**即时生效**。
    *   **交互流程：**
        1.  **初始状态：** 页面首次加载时，列表数据默认按 `申请时间` 降序排列。为明确表示此规则，`申请时间` 这一列的表头排序图标应**默认呈“降序且高亮”**的状态。
        2.  **用户操作：** 用户点击任一可排序列的表头排序图标，列表将**立即**按照新的规则进行重排，被点击的排序图标更新为对应的高亮状态，而其他列的排序图标恢复默认状态。
        3.  **排序能力：** 本期需求**明确不支持**多字段的联合排序，以保持交互的简洁性。

#### **2.1.3. 筛选与排序的交互联动规则 (核心)**
*   **场景一 (筛选条件已生效 - 蓝色标签)：** 在此状态下，用户点击任一排序图标，系统将**立即**带着所有已生效的筛选条件和新的排序规则，**自动执行一次后台查询**，并刷新列表。此操作无需用户再次点击【搜索】按钮，以保证数据探索的流畅性。
*   **场景二 (筛选条件准备中 - 灰色标签)：** 在此状态下，用户点击任一排序图标，系统必须进行一次用户意图确认。此时，应**弹出一个确认框**，文字提示为：“您有未提交的筛选条件。要立即应用这些条件并排序吗？”
    *   **点击【确认】：** 系统执行操作，等同于用户点击了一次【搜索】按钮并紧接着应用排序。列表将根据准备中的筛选条件和新的排序规则进行刷新，筛选标签变为蓝色。
    *   **点击【取消】：** 关闭确认框，不执行任何操作，页面状态无任何变化。

#### **2.1.4. 页面组件与布局**
*   **A. 默认筛选区**
    *   **位置：** 页面顶部。
    *   **字段：** `单号` (文本输入框)、`标题` (文本输入框)、`申请开始日期` (日期选择器)、`申请结束日期` (日期选择器)。
*   **B. 高级筛选功能**
    *   在默认筛选区旁边，提供一个 **【更多筛选条件】** 按钮。点击后以**弹窗**形式提供所有额外的筛选条件。
    *   **弹窗内字段及规格：**
        *   `工单类型` (下拉多选框，选项从后台动态获取)
        *   `工单状态` (下拉多选框，固定选项：`审批中`, `已完结`, `已驳回`)
        *   `申请人` (支持输入搜索和多选的下拉框)
        *   `运维负责人` (支持输入搜索和多选的下拉框)
        *   `抄送人` (支持输入搜索和多选的下拉框)
        *   `总耗时` (下拉单选框，选项：`全部`, `超过1天`, `超过3天`, `超过1周`)
        *   `当前节点停留时长` (下拉单选框，选项：`全部`, `超过12小时`, `超过24小时`, `超过3天`)
    *   **弹窗交互：**
        *   弹窗内不设条件预览区。提供【确定】和【取消】按钮。
        *   点击【确定】时，系统判断弹窗内的条件是否真的发生了变化。如果没变，则什么都不做；如果变了，才将主页面的生效标签（蓝色）变灰，并应用新条件到准备区（灰色）。
*   **C. 操作按钮区**
    *   提供【搜索】按钮和【重置】按钮（位于搜索按钮右侧）。
    *   **【重置】按钮：** 点击后 **立即生效**，清空所有筛选控件的设置和“筛选条件展示区”的标签，**同时将排序规则也重置回默认的“申请时间 降序”**，并立即执行一次无条件查询，将列表恢复到初始加载状态。
*   **D. 筛选条件展示区**
    *   **位置：** 位于列表的左上方，与上方的默认筛选区在视觉上形成对齐。
    *   用于可视化地展示灰色（准备中）和蓝色（已生效）的条件标签。页面首次加载时，此区域完全空白。
*   **E. SLA说明图标**
    *   **位置：** 位于列表的右上方。
    *   提供一个“i”(information)图标，当用户鼠标悬浮在其上时，通过一个提示框(Tooltip)清晰地展示SLA高亮规则。

#### **2.1.5. 工单列表展示**
*   **A. 列表字段及顺序**
    *   `单号`
    *   `工单标题`
    *   `工单类型`
    *   `总节点数`
    *   `当前节点`
    *   `申请人`
    *   `运维负责人`
    *   `抄送人` **(新增)**
    *   `申请时间`
    *   `最新更新时间`
    *   `总耗时`
    *   `当前节点停留时长`
    *   `详情` (操作按钮)
*   **B. 支持排序的列**
    *   `申请时间` (默认排序列)
    *   `最新更新时间`
    *   `总耗时`
    *   `当前节点停留时长`
*   **C. 后端排序规则**
    *   为确保业务逻辑的正确性，所有排序请求，后端应采用复合排序逻辑：首先按 **`工单状态`** 排序（顺序为：审批中、已驳回、已完结），在此基础上，再应用用户选择的第二重排序列（如 `申请时间`）。前端只需高亮显示用户选择的排序列即可。
*   **D. 字段特定展示规则**
    *   **工单状态：** 以 `[状态]` 标签的形式，展示在`单号`单元格数据内容的**左上侧**。
    *   **抄送人：** 当抄送人数量大于3人时，采用**截断显示**（例如 `张三, 李四, 王五...`）。鼠标悬浮在该单元格上时，通过提示框(Tooltip)显示完整的抄送人名单。
    *   **空数据处理：** 当`抄送人`、`总耗时`等字段无数据时，单元格内统一显示中横线 **`-`**，以明确表示“无数据”而非“加载失败”。
*   **E. SLA阈值高亮提醒**
    *   **适用范围：** 此功能 **仅对“审批中”状态的工单生效**。
    *   **高亮范围：** **整行高亮**。高亮颜色应柔和不突兀（例如淡黄、淡粉），以避免过度干扰用户视觉。
    *   **高亮规则 (采用“就高原则”，红色优先)：**
        *   **警告 (淡黄色)：** 当 `当前节点停留时长` > 24小时，**或** `总耗时` > 3天。
        *   **严重 (淡红色)：** 当 `当前节点停留时长` > 48小时，**或** `总耗时` > 5天。

#### **2.1.6. 系统反馈**
*   **A. 加载中状态**
    *   在任何触发后台查询的操作后（如搜索、重置、排序），等待数据返回期间，列表区域应显示**骨架加载动画 (Skeleton Screen)**，以提供平滑的加载体验。
*   **B. 无结果反馈**
    *   当查询结果为空时，列表区域不应显示空白表格，而应提供一个明确、友好的居中提示，文字为：“未找到符合条件的工单，请尝试调整您的筛选条件。”

## **3. 后续迭代规划**
*   **管理操作增强：** 增加“转办”、“指派”、“催办”、“代为处理”等更高级的管理操作权限。
*   **关注与订阅功能：** 实现“我关注的”功能，允许用户手动标记重要工单，并能接收相关更新的系统通知。
*   **数据洞察与报表：** 基于工单数据，提供可视化的统计报表功能，如图表化的部门/类型工单量、平均处理时长分析等。
*   **权限精细化：** 在满足业务需求的前提下，探讨支持字段级别的查看权限控制，以满足部分敏感工单（如薪资、人事）的保密需求。
*   **数据关联：** 探讨与外部系统（如飞书、钉钉）进行用户及部门组织架构信息同步的方案，以支持按“申请人所属部门”进行筛选和查看。