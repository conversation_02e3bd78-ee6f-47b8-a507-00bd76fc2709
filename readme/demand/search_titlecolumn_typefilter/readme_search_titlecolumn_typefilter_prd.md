# WFO 系统审批工单页面新增功能 PRD

---

## 1. 工单标题列

* **新增列名：** 工单标题
* **数据来源：** 后端返回 `title` 字段。
* **显示逻辑：**
    * 若 `title` 字段非空（非空字符串），则显示其内容。
    * 若 `title` 字段为空字符串，则该列显示为空。
* **样式：**
    * 设置**最大宽度**（具体数值待定，例如 `200px`）。
    * 内容强制**单行显示**。
    * 超出部分显示**省略号 `...`**。
    * 鼠标悬停时显示**完整内容（Tooltip）**。

---

## 2. 搜索功能

* **触发方式：** 所有搜索条件（单号、标题、申请日期时间范围）均通过**点击“搜索”按钮**触发。
* **搜索条件及规则：**
    * **单号：** 支持**模糊搜索**。
    * **标题：** 支持**模糊搜索**。
    * **申请日期时间范围：**
        * **粒度：** 精确到**时分秒**。
        * **控件：** **两个日期选择器**（开始日期、结束日期），通过弹窗选择。
        * **数据传递：** 前端将未填写的日期字段设置为其 ProtoBuf 类型（`google.protobuf.Timestamp`）的**零值**（空值）传递给后端。
* **空值/空格处理：**
    * **前端处理：** 若单号或标题输入框中仅包含空格，前端在传递给后端时，会将对应字段值**置为空字符串**（`""`）。
    * **后端处理：** 后端将空字符串视为“未提供该搜索条件”。
* **校验与提示：**
    * **日期校验：** 前端**实时校验**结束日期是否大于等于开始日期。
    * **校验提示：** 若校验不通过，显示**小字提示**（例如“结束日期不能早于开始日期”）。
    * **自动处理：** 校验失败后，前端自动将无效的结束日期字段**置为未输入状态**。若用户未手动修改，点击“搜索”时该字段**作为空值传递给后端**。
* **多条件关系：** 所有搜索条件之间为**“与”关系**。
* **默认展示：** 无任何搜索条件时，页面**默认展示所有工单**。
* **权限：** 前端无需进行权限控制，默认展示后端返回的当前用户可查看工单。

---

## 3. 工单类型筛选功能

* **触发方式：** “工单类型”列旁增加**筛选按钮**（类似于 Excel 筛选）。
* **筛选界面：** 点击按钮后出现**小弹窗**，列出所有工单类型。
* **默认状态：** 筛选功能默认处于**全选状态**。
* **多选支持：** 用户可**多选**工单类型。
* **数据来源：** 工单类型列表是**固定枚举值，但未来可能增加**；后端返回 `OrderType` (英文 ID) 和 `OrderTypeName` (中文 ID)。
* **触发逻辑：** 用户勾选/取消勾选工单类型后，**立即触发数据刷新**。
* **弹窗关闭：** 弹窗可通过**点击弹窗外部区域或点击筛选按钮本身**关闭。
* **清空功能：** 弹窗内提供**“全选”和“全不选”按钮**（“全不选”兼具清除筛选功能）。
* **数据处理：** 工单类型筛选条件将作为**搜索条件传递给后端处理**。
* **前端优化：** 前端将使用**防抖**机制优化频繁操作时的请求。

---

## 4. 视觉与交互

* **搜索条件视觉区分：** 结合**边框、背景、字体、占位符**等多种视觉元素区分已填写和未填写的搜索条件。
* **搜索条件无效提示：**
    * **提示文本：** 在页面顶部显示非模态提示“部分搜索条件无效，请检查并重新输入。”
    * **局部反馈：** 无效输入框下方显示小字提示（如“请输入有效内容”），且输入框边框短暂变为柔和提示色。
    * **提示消失：** 提示将**自动消失**（3-5秒后），并提供**手动关闭按钮**（“X”图标）。
* **加载提示：** 数据加载时，前端使用**骨架屏**作为加载提示。