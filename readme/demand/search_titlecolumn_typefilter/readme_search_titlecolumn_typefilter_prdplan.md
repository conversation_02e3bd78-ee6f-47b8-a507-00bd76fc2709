# WFO 系统审批工单页面新增功能需求规划

---

## 迭代规划概述

本需求规划旨在将 WFO 系统审批工单页面的新增功能拆分为多个可独立实现和验证的迭代版本。每个迭代版本都将提供一个独立可用的功能子集，并逐步完善用户体验和功能完整性。

---

## 迭代一：MVP 版 - 核心功能实现

**目标：** 实现审批工单页面的基本搜索和展示能力，确保用户可以进行最基础的工单查找。

**功能列表：**

1.  **工单标题列显示：**
    * 在工单列表展示区域新增名为 "**工单标题**" 的列。
    * 该列数据来源为后端返回的 `title` 字段。
    * **显示逻辑：** 当 `title` 字段非空字符串时，显示其内容；当 `title` 字段为空字符串时，该列显示为空。
    * **样式：**
        * 设置最大宽度为 `200px`。
        * 内容强制单行显示。
        * 超出部分显示省略号 `...`。
        * 鼠标悬停时显示完整内容（Tooltip）。

2.  **基本搜索功能（单号与标题）：**
    * 提供 "**单号**" 和 "**标题**" 两个文本输入框作为搜索条件。
    * 触发搜索的按钮名为 "**搜索**"。
    * **搜索规则：**
        * **单号：** 支持模糊搜索。
        * **标题：** 支持模糊搜索。
    * **空值/空格处理：**
        * **前端处理：** 当单号或标题输入框中仅包含空格时，前端在传递给后端时，会将对应字段值置为空字符串（`""`）。
        * 后端将空字符串视为“未提供该搜索条件”。
    * **多条件关系：** 所有搜索条件之间为“**与**”关系。
    * **默认展示：** 在没有任何搜索条件输入时，页面默认展示所有工单。
    * **权限：** 前端无需进行权限控制，默认展示后端返回的当前用户可查看的工单。

3.  **加载提示（骨架屏）：**
    * 在数据加载时，前端使用**骨架屏**作为加载提示，覆盖整个工单列表区域。

---

## 迭代二：日期范围搜索与基本校验

**目标：** 扩展搜索功能，支持按申请日期时间范围搜索，并加入基本的日期校验。

**功能列表：**

1.  **申请日期时间范围搜索：**
    * 在搜索区域新增 "**申请日期时间范围**" 搜索条件。
    * **控件：** 提供两个日期时间选择器，分别用于选择 **开始日期** 和 **结束日期**。选择器需支持精确到时分秒。
    * **数据传递：** 前端将未填写的日期字段设置为其 ProtoBuf 类型（`google.protobuf.Timestamp`）的零值（空值）传递给后端。
    * **触发方式：** 申请日期时间范围的搜索条件也通过点击 "**搜索**" 按钮触发。

2.  **日期范围实时校验：**
    * **校验规则：** 前端实时校验结束日期是否大于或等于开始日期。
    * **校验提示：** 若校验不通过，在日期选择器下方显示小字提示，例如“结束日期不能早于开始日期”。
    * **自动处理：** 若校验失败，前端自动将无效的结束日期字段置为未输入状态。如果用户未手动修改，点击“搜索”时该字段作为空值传递给后端。

3.  **搜索条件视觉区分（已填写/未填写）：**
    * 对单号、标题、开始日期、结束日期四个搜索条件的输入框，通过**边框、背景、字体、占位符**等视觉元素，清晰区分其**已填写**和**未填写**状态。

---

## 迭代三：工单类型筛选与用户体验优化

**目标：** 引入工单类型筛选功能，并进一步完善搜索条件相关的用户提示和交互。

**功能列表：**

1.  **工单类型筛选功能：**
    * 在“**工单类型**”列旁边增加一个**筛选按钮**（类似于 Excel 筛选图标）。
    * **筛选界面：** 点击筛选按钮后，弹出一个小弹窗，列出所有工单类型。
    * **默认状态：** 筛选功能默认处于**全选状态**。
    * **多选支持：** 用户可以多选工单类型。
    * **数据来源：** 工单类型列表为固定枚举值，后端返回 `OrderType` (英文 ID) 和 `OrderTypeName` (中文 ID)。
    * **触发逻辑：** 用户勾选/取消勾选工单类型后，立即触发数据刷新。
    * **弹窗关闭：** 弹窗可以通过点击弹窗外部区域或点击筛选按钮本身关闭。
    * **清空功能：** 弹窗内提供 "**全选**" 和 "**全不选**" 按钮（“全不选”按钮兼具清除筛选功能）。
    * **数据处理：** 工单类型筛选条件将作为搜索条件传递给后端处理。
    * **前端优化：** 前端对工单类型筛选操作使用**防抖**机制，优化频繁操作时的请求。

2.  **搜索条件无效提示（非模态）：**
    * 当存在无效搜索条件时（例如日期校验失败），在页面顶部显示一条**非模态提示**：“部分搜索条件无效，请检查并重新输入。”
    * 该提示将在 3-5 秒后自动消失。
    * 提供一个 "**X**" 图标，允许用户手动关闭该提示。

3.  **搜索条件局部反馈：**
    * 当某个搜索输入框（如单号、标题、日期）的输入内容无效时（例如日期校验失败），在该输入框下方显示小字提示，例如“请输入有效内容”。
    * 同时，该输入框的边框短暂变为柔和提示色，以吸引用户注意。