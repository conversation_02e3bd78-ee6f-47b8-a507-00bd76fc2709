# WFO 审批工单页面迭代开发计划 2.0

---

## 需求概述

根据PRD需求文档，本次迭代需要为WFO系统审批工单页面实现以下核心功能：

1. **工单标题列新增** - 新增标题显示列，支持省略号截断和Tooltip展示
2. **增强搜索功能** - 支持单号、标题模糊搜索，以及申请日期时间范围搜索
3. **工单类型筛选** - 在工单类型列添加Excel风格的筛选功能
4. **视觉交互优化** - 骨架屏加载、表单校验提示、视觉状态区分

---

## 目录结构设计

基于现有代码结构，本次迭代涉及的目录结构如下：

```
src/
├── components/                    # 新增：可复用组件目录
│   ├── SearchForm/               # 新增：搜索表单组件
│   │   ├── index.js             # 搜索表单主组件
│   │   └── styles.js            # SearchForm样式定义
│   ├── OrderTable/              # 新增：工单表格组件
│   │   ├── index.js             # 表格主组件（从AuditOrder分离）
│   │   └── styles.js            # OrderTable样式定义
│   ├── OrderPagination/         # 新增：分页组件
│   │   ├── index.js             # 分页主组件
│   │   └── styles.js            # 分页样式定义
│   └── common/                  # 新增：通用组件
│       └── LoadingSkeleton/     # 新增：骨架屏组件
│           ├── index.js         # 骨架屏主组件
│           └── styles.js        # 骨架屏样式
├── pages/
│   └── auditOrder/              # 现有：审批工单页面
│       ├── index.js             # 修改：主页面组件（重构为容器组件）
│       └── styles.js            # 修改：页面级样式
├── utils/                       # 扩展：工具函数
│   ├── date.js                  # 新增：日期处理工具
│   └── string.js                # 新增：字符串处理工具
├── request/
│   └── api.js                   # 修改：扩展搜索相关API接口
└── ...现有其他文件
```

---

## 受影响的现有模块

### 1. 核心受影响模块
- **src/pages/auditOrder/index.js** - 主页面组件需重构为容器组件，负责状态管理和数据流转
- **src/request/api.js** - 需扩展`requestMyAuditOrder`接口支持新的搜索参数
- **src/pages/auditOrder/styles.js** - 需适配新的布局和组件样式

### 2. 复用策略
- **复用现有BaseLayout布局框架** - 保持页面整体布局一致性
- **复用现有API service架构** - 基于service.post封装扩展新接口
- **复用Ant Design组件体系** - Input、Button、DatePicker、Table、Pagination等
- **复用styled-components样式方案** - 保持样式架构一致性
- **复用React Class Component模式** - 与现有代码风格保持一致

---

## 渐进式小步迭代开发步骤

### 步骤1：创建工具函数模块
**目标：** 建立基础工具函数，为后续功能提供支撑

**开发内容：**
- 创建 `src/utils/date.js` - 日期处理工具函数
  - `toISOStringOrNull(date)` - 日期转ISO字符串或null
  - `isValidDateRange(startDate, endDate)` - 日期范围校验
- 创建 `src/utils/string.js` - 字符串处理工具函数
  - `trimToEmpty(str)` - 字符串去空格转空字符串
  - `isEmptyOrWhitespace(str)` - 判断字符串是否为空或空格

**验证标准：**
- 工具函数创建完成，导入无错误
- 应用能正常启动和运行

---

### 步骤2：创建骨架屏组件
**目标：** 实现数据加载时的骨架屏效果

**开发内容：**
- 创建 `src/components/common/LoadingSkeleton/index.js`
  - 基于Ant Design Skeleton组件封装
  - 支持表格行数可配置（默认5行）
  - 支持显示/隐藏控制
- 创建 `src/components/common/LoadingSkeleton/styles.js`
  - 定义骨架屏的styled-components样式

**验证标准：**
- 骨架屏组件可独立导入使用
- 应用正常启动，骨架屏渲染正常

---

### 步骤3：扩展API接口支持搜索参数
**目标：** 为requestMyAuditOrder接口增加搜索参数支持

**开发内容：**
- 修改 `src/request/api.js` 中的 `requestMyAuditOrder` 函数
  - 支持orderId（单号）参数
  - 支持title（标题）参数  
  - 支持startDate（开始日期）参数
  - 支持endDate（结束日期）参数
  - 保持向后兼容，参数为可选

**伪代码实现：**
```javascript
export const requestMyAuditOrder = (params = {}) => {
  const {
    orderId = '',
    title = '',
    startDate = null,
    endDate = null,
    page = 1,
    pageSize = 10,
    ...otherParams
  } = params;
  
  return service.post('/api/auditOrder/list', {
    orderId: trimToEmpty(orderId),
    title: trimToEmpty(title),
    startDate: toISOStringOrNull(startDate),
    endDate: toISOStringOrNull(endDate),
    page,
    pageSize,
    ...otherParams
  });
};
```

**验证标准：**
- API接口修改完成，现有功能不受影响
- 应用正常启动，工单列表正常展示

---

### 步骤4：创建搜索表单组件
**目标：** 实现独立的搜索表单组件，支持单号、标题、日期范围搜索

**开发内容：**
- 创建 `src/components/SearchForm/index.js`
  - 基于Ant Design Form、Input、DatePicker组件
  - 包含单号输入框、标题输入框、日期范围选择器
  - 实现日期范围实时校验逻辑
  - 支持onSearch回调传递搜索参数
- 创建 `src/components/SearchForm/styles.js`
  - 定义表单布局和样式
  - 实现已填写/未填写状态的视觉区分
  - 定义校验提示样式

**组件接口设计：**
```javascript
// props接口
{
  onSearch: (searchParams) => void,  // 搜索回调
  loading: boolean                   // 加载状态
}

// searchParams结构
{
  orderId: string,
  title: string,
  startDate: Date | null,
  endDate: Date | null
}
```

**验证标准：**
- 搜索表单组件可独立渲染
- 日期校验逻辑工作正常
- 表单视觉状态区分明显

---

### 步骤5：创建工单表格组件
**目标：** 将表格逻辑从主页面分离，新增标题列

**开发内容：**
- 创建 `src/components/OrderTable/index.js`
  - 从 `AuditOrder` 组件分离表格相关代码
  - 新增"工单标题"列，支持省略号截断和Tooltip
  - 保持现有列结构和数据格式
  - 支持onViewDetail回调
- 创建 `src/components/OrderTable/styles.js`
  - 定义表格样式，特别是标题列样式
  - 实现最大宽度限制和省略号效果

**标题列实现：**
```javascript
{
  title: '工单标题',
  dataIndex: 'title',
  key: 'title',
  width: 200,
  render: (text) => (
    <Tooltip title={text || ''} placement="topLeft">
      <TitleCell>{text || ''}</TitleCell>
    </Tooltip>
  )
}
```

**验证标准：**
- 表格组件独立渲染正常
- 标题列显示正确，省略号和Tooltip功能正常
- 表格数据展示与原有功能一致

---

### 步骤6：创建分页组件
**目标：** 将分页逻辑组件化，支持分页参数传递

**开发内容：**
- 创建 `src/components/OrderPagination/index.js`
  - 基于Ant Design Pagination组件
  - 支持total、current、pageSize、onChange属性
  - 包含页码跳转和每页条数选择功能
- 创建 `src/components/OrderPagination/styles.js`
  - 定义分页组件样式，保持与设计一致

**组件接口设计：**
```javascript
// props接口
{
  total: number,
  current: number,
  pageSize: number,
  onChange: (page, pageSize) => void,
  showSizeChanger: boolean,
  showQuickJumper: boolean
}
```

**验证标准：**
- 分页组件可独立使用
- 分页功能完整，支持页码跳转
- 样式与现有设计保持一致

---

### 步骤7：重构主页面为容器组件
**目标：** 重构AuditOrder主页面，集成所有子组件，实现完整功能

**开发内容：**
- 修改 `src/pages/auditOrder/index.js`
  - 引入所有新创建的子组件
  - 实现统一的状态管理（搜索参数、加载状态、分页状态）
  - 实现搜索逻辑，调用扩展后的API接口
  - 集成骨架屏加载效果
  - 保持原有的详情查看功能
- 更新 `src/pages/auditOrder/styles.js`
  - 适配新的布局结构
  - 确保各子组件样式协调

**主要状态结构：**
```javascript
state = {
  // 数据状态
  orderList: [],
  total: 0,
  
  // 搜索状态
  searchParams: {
    orderId: '',
    title: '',
    startDate: null,
    endDate: null
  },
  
  // 分页状态
  pagination: {
    current: 1,
    pageSize: 10
  },
  
  // UI状态
  loading: false,
  
  // 原有状态（详情等）
  selectedOrderId: null,
  selectedOrderType: null
}
```

**验证标准：**
- 页面完整集成所有子组件
- 搜索功能完整可用，包括单号、标题、日期范围搜索
- 工单标题列正确显示，支持省略号和Tooltip
- 分页功能正常工作
- 骨架屏在数据加载时正确显示
- 原有的详情查看功能保持正常
- 应用整体运行稳定

---

### 步骤8：工单类型筛选功能集成
**目标：** 在工单类型列添加筛选功能，支持多选筛选

**开发内容：**
- 修改 `src/components/OrderTable/index.js`
  - 为工单类型列添加Ant Design Table内置筛选功能
  - 通过filters属性配置筛选选项
  - 实现onFilter筛选逻辑
  - 支持filterMultiple多选筛选
- 扩展主页面状态管理
  - 添加工单类型筛选状态
  - 获取工单类型选项数据
  - 将筛选条件传递给API接口

**筛选列配置：**
```javascript
{
  title: '工单类型',
  dataIndex: 'orderTypeName',
  key: 'orderType',
  filters: orderTypeFilters, // 从API获取或使用默认值
  onFilter: (value, record) => record.orderType === value,
  filterMultiple: true
}
```

**验证标准：**
- 工单类型列显示筛选按钮
- 筛选弹窗功能正常，支持多选
- 筛选后表格数据正确过滤
- 筛选状态与其他搜索条件正确配合
- 应用整体功能完整可用

---

## 开发注意事项

### 1. 代码质量要求
- 每个组件文件控制在500行以内
- 保持与现有代码风格一致（React Class Component + styled-components）
- 复用现有的Ant Design组件和API架构
- 确保每个步骤完成后应用可正常运行

### 2. 兼容性保障
- 保持向后兼容，不破坏现有功能
- API接口扩展采用可选参数方式
- 样式修改不影响其他页面

### 3. 验证标准
- 每步完成后应用能成功启动
- 新功能能够正常展示和交互
- 现有功能保持稳定运行
- 代码无语法错误和运行时错误

---

## 总结

本开发计划采用8步渐进式迭代方式，从基础工具函数开始，逐步构建搜索表单、工单表格、分页组件等，最终集成为完整的功能页面。每个步骤都确保应用的可运行性和功能的可验证性，同时最大化复用现有代码架构，保持系统的一致性和稳定性。
