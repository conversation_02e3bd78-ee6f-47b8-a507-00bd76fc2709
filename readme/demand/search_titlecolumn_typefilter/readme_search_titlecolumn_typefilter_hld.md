# WFO 系统审批工单页面概要设计

## 架构概览
本系统审批工单页面采用**客户端-服务端**架构，前端负责用户界面、交互逻辑以及数据展示，后端负责数据存储、业务逻辑处理和数据接口提供。整体设计遵循KISS和YAGNI原则，力求简洁并易于未来迭代。

系统主要由以下层级和组件构成：

+ **前端层 (Client Layer)**:
    - **页面组件 (Page Component)**: `AuditOrder` 负责协调所有子组件，管理整体页面状态和数据流。
    - **业务组件 (Business Components)**: 如 `SearchForm` (搜索表单)、`OrderTable` (工单列表表格，包含工单标题列和筛选功能)、`OrderPagination` (分页组件)。
    - **UI/基础组件 (UI/Base Components)**: 依赖 Ant Design 提供的基础 UI 组件（`Input`, `DatePicker.RangePicker`, `Table`, `Select`, `Message`, `Skeleton` 等）。
    - **状态管理 (State Management)**: `React Query` 用于服务器数据管理，`Zustand` 用于客户端 UI 状态管理。
    - **数据请求 (Data Fetching)**: `Axios` 负责与后端 API 进行数据交互。
+ **后端层 (Backend Layer)**:
    - **API 服务 (API Service)**: 提供查询工单列表、工单详情等接口。
    - **业务逻辑 (Business Logic)**: 处理工单的搜索、筛选、排序等业务规则。
    - **数据存储 (Data Storage)**: 负责工单数据的持久化存储。

```mermaid
sequenceDiagram
    participant Browser as 用户浏览器
    participant AuditOrderComponent as AuditOrder组件(React)
    participant SearchForm as 搜索表单(React)
    participant OrderTable as 工单表格(React)
    participant BackendAPI as 后端API服务
    participant Database as 数据库

    Browser->>AuditOrderComponent: 访问审批工单页面
    AuditOrderComponent->>BackendAPI: 初始化请求获取工单列表 (requestMyAuditPageOrder)
    BackendAPI->>Database: 查询所有工单
    Database-->>BackendAPI: 返回工单数据
    BackendAPI-->>AuditOrderComponent: 返回工单数据
    AuditOrderComponent->>OrderTable: 渲染工单数据
    AuditOrderComponent->>SearchForm: 渲染搜索表单
    AuditOrderComponent->>Browser: 页面加载完成并显示
    User->>SearchForm: 输入搜索条件/点击筛选
    SearchForm->>AuditOrderComponent: 触发搜索/筛选事件
    AuditOrderComponent->>BackendAPI: 携带搜索/筛选条件请求工单数据
    BackendAPI->>Database: 根据条件查询工单
    Database-->>BackendAPI: 返回过滤后的工单数据
    BackendAPI-->>AuditOrderComponent: 返回过滤后的工单数据
    AuditOrderComponent->>OrderTable: 更新并渲染过滤后的工单数据
    AuditOrderComponent->>Browser: 显示更新后的页面
```

---

## 组件拆分(Components)
+ **AuditOrder Component (src/pages/auditOrder/index.js)**
    - **职责**: 作为页面的根组件，负责整合所有子组件，管理页面级别的状态（如分页信息、搜索/筛选条件），协调数据获取与渲染。
    - **核心功能**:
        * 初始化数据加载 (`componentDidMount` / `useEffect`)。
        * 处理分页逻辑 (`changePage`, `changeShowSize`)。
        * 接收并处理搜索/筛选条件变化，触发数据重新加载。
        * 管理服务器数据状态（通过 `React Query` 封装数据请求）。
+ **SearchForm Component**
    - **职责**: 提供工单单号、工单标题、申请日期时间范围的输入和选择功能，并管理这些搜索条件。
    - **核心功能**:
        * 渲染 `Input` (单号、标题) 和 `DatePicker.RangePicker` (申请日期时间范围)。
        * 处理输入框的空值/空格处理逻辑。
        * 实现日期范围的实时校验和提示。
        * 提供“搜索”按钮，点击后将所有条件集合并通过事件通知 `AuditOrder` 组件。
+ **OrderTable Component**
    - **职责**: 展示工单列表数据，包含工单标题列的特定显示逻辑和工单类型筛选功能。
    - **核心功能**:
        * 渲染 Ant Design `Table` 组件。
        * 实现工单标题列的单行显示、省略号和 Tooltip 效果。
        * 集成工单类型筛选功能（利用 Ant Design Table 的 `filter` 属性，或自定义筛选弹窗）。
        * 展示骨架屏加载状态。
+ **OrderPagination Component**
    - **职责**: 提供分页控件，允许用户切换页码和调整每页显示数量。
    - **核心功能**:
        * 渲染 Ant Design `Pagination` 组件。
        * 接收 `total`, `current`, `pageSize` 等分页数据。
        * 触发 `changePage` 和 `changeShowSize` 事件通知 `AuditOrder` 组件。
+ **OrderDetailDrawer Component**
    - **职责**: 用于显示单个工单的详细信息。
    - **核心功能**:
        * 接收 `orderID` 和 `orderType` 作为属性。
        * 根据传入的工单ID请求并展示工单详情。

---

## 目录结构树(Directory Tree)
```plain
src/
├── api/                  # API 服务定义
│   └── order.js          # 工单相关的 API 请求
├── components/           # 可复用的通用 UI 或业务组件
│   ├── SearchForm/
│   │   └── index.js
│   ├── OrderTable/
│   │   └── index.js
│   ├── OrderPagination/
│   │   └── index.js
│   └── OrderDetailDrawer/
│       └── index.js
├── hooks/                # 自定义 React Hooks
│   └── useDebounce.js    # 防抖 Hook (如果使用自定义实现而非 lodash)
├── pages/
│   └── auditOrder/       # 审批工单页面
│       └── index.js      # AuditOrder 组件主文件
├── stores/               # Zustand 状态管理 store
│   └── uiStore.js        # 例如，用于管理一些全局 UI 状态
├── utils/                # 工具函数
│   ├── date.js           # 日期处理工具
│   └── string.js         # 字符串处理工具 (如去除空格)
└── App.js                # 应用入口
└── index.js              # 应用初始化
```

---

## 数据流(Data Flow)
**关键业务场景：用户搜索“单号”和“标题”，并筛选“工单类型”**

1. **用户输入与交互**:
    - 用户在**SearchForm**组件的“单号”和“标题”输入框中键入内容。
    - 用户通过**OrderTable**组件旁边的筛选按钮打开工单类型筛选弹窗，并选择一个或多个工单类型。
2. **前端数据处理**:
    - **SearchForm**：当用户在“单号”或“标题”输入框中键入时，内部状态更新。点击“搜索”按钮时，SearchForm 收集单号、标题、日期范围（如果已选择）。在传递给 `AuditOrder` 组件前，对单号和标题进行**空值/空格处理**（去除首尾空格，若仅含空格则置为 `""`），未填写的日期字段则置为 `google.protobuf.Timestamp` 的**零值**。
    - **OrderTable**：当用户勾选/取消勾选工单类型时，立即（经过**防抖**处理后）触发数据刷新事件，将选中的 `OrderType` 列表传递给 `AuditOrder` 组件。
3. **AuditOrder 组件触发数据请求**:
    - `AuditOrder` 组件接收到 `SearchForm` 提交的搜索条件或 `OrderTable` 提交的筛选条件。
    - `AuditOrder` 组件将这些条件与当前分页信息（页码、页面大小）合并，形成完整的请求参数对象。
    - 通过 `React Query` 的 `useQuery` 或 `useInfiniteQuery` hook (如果需要无限滚动) 触发数据请求，调用 `api/order.js` 中定义的 `requestMyAuditOrder` API。
4. **后端 API 服务处理**:
    - 后端 `requestMyAuditOrder` API 接收前端传递的搜索条件（单号、标题、申请日期时间范围、工单类型列表）和分页参数。
    - 后端根据这些条件执行数据库查询，单号和标题进行**模糊搜索**，日期范围进行精确匹配，工单类型进行多选匹配，并应用分页逻辑。
    - 后端返回符合条件的工单列表数据，包含 `title` 字段。
5. **前端数据渲染**:
    - `React Query` 接收到后端返回的数据，并更新其缓存。
    - `AuditOrder` 组件通过 `React Query` 获取到最新的工单数据。
    - `AuditOrder` 将数据传递给 `OrderTable` 组件进行渲染。
    - `OrderTable` 组件根据 `title` 字段的显示逻辑（非空则显示，空则为空，最大宽度、单行、省略号、Tooltip）渲染“工单标题”列。
    - 数据加载期间，**OrderTable** 显示**骨架屏**。

```mermaid
sequenceDiagram
    participant User as 用户
    participant SearchForm as SearchForm组件
    participant OrderTable as OrderTable组件
    participant AuditOrder as AuditOrder组件
    participant ReactQuery as React Query
    participant Axios as Axios
    participant BackendAPI as 后端API服务
    participant Database as 数据库

    User->>SearchForm: 输入单号/标题 & 选择日期范围
    User->>OrderTable: 选择工单类型 (经过防抖)
    SearchForm->>AuditOrder: 提交搜索条件 (单号, 标题, 日期范围, 空值/空格/零值处理)
    OrderTable->>AuditOrder: 提交筛选条件 (工单类型列表)
    AuditOrder->>ReactQuery: 触发数据查询 (useQuery.refetch)
    ReactQuery->>Axios: 发送 GET /api/orders (带搜索/筛选/分页参数)
    Axios->>BackendAPI: HTTP 请求
    BackendAPI->>Database: 根据条件查询工单 (模糊搜, 日期范围, 多选类型)
    Database-->>BackendAPI: 返回匹配的工单数据
    BackendAPI-->>Axios: HTTP 响应 (JSON)
    Axios-->>ReactQuery: 返回数据
    ReactQuery-->>AuditOrder: 提供更新后的数据
    AuditOrder->>OrderTable: 传入更新后的工单数据
    OrderTable->>User: 渲染工单列表 (包含标题列显示逻辑 & 骨架屏)
    User->>User: (若有无效搜索条件) 页面顶部非模态提示 & 输入框局部反馈
```

---

## 数据模型设计(Data Model Design)
以下为核心业务实体 `Order` 的初步数据 Schema。考虑到工单管理系统的通用性，我们假定存在 `User` 和 `OrderType` 实体。

```mermaid
erDiagram
    USER {
        string id PK
        string name
        string email
        string department
    }

    ORDER_TYPE {
        string id PK "OrderType (英文ID)"
        string name "OrderTypeName (中文名称)"
        string description
    }

    ORDER {
        string id PK
        string title "工单标题"
        string description "工单描述"
        string typeId FK "工单类型ID"
        string creatorId FK "创建人ID"
        string auditorId FK "当前审批人ID (nullable)"
        string status "工单状态 (Pending, Approved, Rejected)"
        timestamp createdAt "创建时间"
        timestamp updatedAt "更新时间"
        timestamp completedAt "完成时间 (nullable)"
        string attachments "附件列表 (JSON Array, nullable)"
    }

    USER ||--o{ ORDER : creates
    USER ||--o{ ORDER : audits
    ORDER_TYPE ||--o{ ORDER : has
```

**Schema 解释:**

+ `USER`** (用户)**: 记录系统用户基本信息。
+ `ORDER_TYPE`** (工单类型)**: 定义工单的分类，`id` 为英文标识，`name` 为中文名称。
+ `ORDER`** (工单)**:
    - `id`: 工单唯一标识符。
    - `title`: **新增字段**，用于存储工单标题。
    - `description`: 工单详细描述。
    - `typeId`: 外键，关联 `ORDER_TYPE` 表，表示工单的类型。
    - `creatorId`: 外键，关联 `USER` 表，表示工单的创建者。
    - `auditorId`: 外键，关联 `USER` 表，表示当前负责审批的工程师/管理员，可为空（例如，初始待分配状态）。
    - `status`: 工单当前所处的状态（例如：待处理、审批中、已批准、已驳回等）。
    - `createdAt`, `updatedAt`, `completedAt`: 时间戳字段。
    - `attachments`: 存储附件路径或元数据的 JSON 数组，可为空。

---

## API 接口定义
以下是审批工单页面所需的核心 API 端点：

1. **获取审批工单列表**
    - **URL**: `/api/v1/audits/orders`
    - **方法**: `GET`
    - **说明**: 获取当前用户可审批的工单列表，支持搜索、筛选和分页。
    - **请求参数 (Query Parameters)**:
        * `pageNum`: Integer, 当前页码 (默认值：1)
        * `pageSize`: Integer, 每页数量 (默认值：10)
        * `orderId`: String, 工单单号 (模糊搜索，空字符串表示不作为搜索条件)
        * `title`: String, 工单标题 (模糊搜索，空字符串表示不作为搜索条件)
        * `appliedStartDate`: Timestamp, 申请日期时间范围开始 (`google.protobuf.Timestamp` 零值表示不作为搜索条件)
        * `appliedEndDate`: Timestamp, 申请日期时间范围结束 ( `google.protobuf.Timestamp` 零值表示不作为搜索条件)
        * `orderTypes`: Array of String, 工单类型ID列表 (例如: `["LEAVE", "OVERTIME"]`, 空数组表示全选或不筛选)
    - **响应示例 (JSON)**:

```json
{
    "code": 0,
    "message": "Success",
    "data": {
        "total": 120,
        "list": [
            {
                "id": "ORD-20240707-001",
                "title": "请假申请 - 张三",
                "orderType": "LEAVE",
                "orderTypeName": "请假工单",
                "creatorName": "张三",
                "status": "PENDING",
                "createdAt": "2024-07-07T10:00:00Z"
            },
            {
                "id": "ORD-20240707-002",
                "title": "加班申请 - 李四",
                "orderType": "OVERTIME",
                "orderTypeName": "加班工单",
                "creatorName": "李四",
                "status": "APPROVED",
                "createdAt": "2024-07-06T15:30:00Z"
            }
            // ... 更多工单
        ]
    }
}
```

2. **获取工单详情**
    - **URL**: `/api/v1/orders/{orderId}`
    - **方法**: `GET`
    - **说明**: 根据工单ID获取指定工单的详细信息。
    - **请求参数 (Path Parameter)**:
        * `orderId`: String, 必需，工单ID
    - **响应示例 (JSON)**:

```json
{
    "code": 0,
    "message": "Success",
    "data": {
        "id": "ORD-20240707-001",
        "title": "请假申请 - 张三",
        "description": "因个人事务，申请7月10日至7月12日休假。",
        "orderType": "LEAVE",
        "orderTypeName": "请假工单",
        "creatorId": "user123",
        "creatorName": "张三",
        "auditorId": "user456",
        "auditorName": "王五",
        "status": "PENDING",
        "createdAt": "2024-07-07T10:00:00Z",
        "updatedAt": "2024-07-07T10:05:00Z",
        "attachments": ["http://example.com/attachment1.pdf"]
    }
}
```

---

## 迭代演进依据
这份设计方案提供了良好的迭代演进能力，主要体现在以下几个方面：

1. **组件化与职责分离**:
    - 整个页面被拆分为独立的 React 组件 (`SearchForm`, `OrderTable`, `AuditOrder` 等)，每个组件负责自身特定的功能。这意味着未来如果需要修改某个功能（例如，调整搜索框的布局或增加新的筛选条件），可以直接针对相应组件进行修改，而不会影响到其他部分。
    - **例如**: 如果未来需要增加“优先级”筛选，只需要在 `SearchForm` 中添加对应的 UI 控件和逻辑，并通过 `AuditOrder` 将新条件传递给后端，无需改动 `OrderTable` 的核心展示逻辑。
2. **清晰的状态管理策略**:
    - 采用 `React Query` 管理服务器数据，`Zustand` 管理客户端 UI 状态，职责明确。
    - `React Query` 自动处理缓存、数据刷新、加载状态，使得前端代码无需关心复杂的异步数据流。这意味着当新的搜索/筛选条件加入时，只需更新 `useQuery` 的 `queryKey` 或 `variables`，`React Query` 会自动处理后续的数据获取和状态更新。
    - **例如**: 未来如果需要支持更复杂的排序功能，只需在 `OrderTable` 中添加排序UI，并调整 `React Query` 的 `queryKey` 即可，底层的数据获取逻辑仍由 `React Query` 高效管理。
3. **遵循 Ant Design 规范与能力**:
    - Ant Design 提供了强大且可扩展的组件，如 `Table`、`Form`、`DatePicker` 等。这些组件内置了许多高级功能和扩展点，能够轻松应对未来可能的需求变化。
    - **例如**: `Table` 组件的列配置非常灵活，如果未来需要新增或调整列的显示（如添加图标、复杂渲染），可以直接修改列定义，无需重构整个表格组件。
4. **接口驱动设计**:
    - 前端和后端通过明确定义的 API 接口进行交互。这种分离使得前端和后端可以并行开发，互不影响。
    - **例如**: 后端可以独立于前端演进其业务逻辑和数据模型，只要保持 API 接口的兼容性，前端就不需要做大的改动。当有新功能需求时，只需要在现有API基础上扩展参数或新增API。
5. **空值/零值和防抖机制**:
    - 对搜索条件空值、空格、ProtoBuf 零值的处理，以及防抖机制的引入，都体现了对系统健壮性和性能的考量，避免了不必要的请求和后端压力，为后续迭代提供了良好的基础。
6. **务实与简洁**:
    - 当前设计未引入额外的微前端、消息队列等复杂技术，而是聚焦于满足现有需求的最简单、最直接的方案。这降低了初期开发和维护的复杂性。
    - **例如**: 如果未来业务规模急剧扩大，需要支持微前端架构，可以在现有组件化的基础上，将不同的业务模块逐渐拆分为独立的微前端应用，当前的组件化设计为这种演进提供了可能。

