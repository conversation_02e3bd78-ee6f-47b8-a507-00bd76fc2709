# WFO工单审批页面功能测试方案

## 📋 测试概览

本文档基于以下需求文档制定测试方案：
- `readme_search_titlecolumn_typefilter_prd.md` - 产品需求文档
- `readme_search_titlecolumn_typefilter_hld.md` - 高层设计文档

---

## 🎯 功能测试范围

### 1. 工单标题列显示功能
### 2. 搜索功能（单号、标题、日期范围）
### 3. 工单类型筛选功能
### 4. 动态筛选选项更新功能
### 5. 数据校验与错误提示
### 6. 分页与UI交互

---

## 🧪 测试用例详细设计

## 一、工单标题列显示测试

### 1.1 基础显示测试
**测试目标**: 验证工单标题列的显示逻辑

**后端测试数据**:
```json
{
  "orders": [
    {
      "id": "ORD-001",
      "title": "正常长度标题",
      "order_type": "common",
      "order_type_name": "通用工单"
    },
    {
      "id": "ORD-002", 
      "title": "这是一个非常非常非常非常非常非常长的工单标题用来测试省略号和tooltip显示效果",
      "order_type": "common",
      "order_type_name": "通用工单"
    },
    {
      "id": "ORD-003",
      "title": "",
      "order_type": "ks_server_apply",
      "order_type_name": "金山云服务器申请"
    },
    {
      "id": "ORD-004",
      "title": "包含特殊字符的标题: <>\"'&\n换行符测试",
      "order_type": "common",
      "order_type_name": "通用工单"
    },
    {
      "id": "ORD-005",
      "title": "测试-工 单",
      "order_type": "common",
      "order_type_name": "通用工单"
    }
  ]
}
```

**验证点**:
- ✅ 正常标题显示完整内容
- ✅ 超长标题显示省略号，鼠标悬停显示完整内容
- ✅ 空标题显示为空（不显示任何内容）
- ✅ 特殊字符文本显示
- ✅ 标题列宽度限制生效

### 1.2 响应式测试
**测试目标**: 验证不同屏幕尺寸下的标题列显示

**验证点**:
- ✅ 1920px宽度：标题列正常显示
- ✅ 1366px宽度：标题列自适应调整

---

## 二、搜索功能测试

### 2.1 单号搜索测试
**测试目标**: 验证工单单号模糊搜索功能

**前端发送数据样例**:
```javascript
// 正常搜索
{
  pageNum: 1,
  pageSize: 10,
  orderId: "ORD-001",
  title: "",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: []
}

// 模糊搜索
{
  pageNum: 1,
  pageSize: 10,
  orderId: "001",
  title: "",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: []
}

// 空格处理（前端显示 请输入有效单号）
{
  pageNum: 1,
  pageSize: 10,
  orderId: "    ",
  title: "",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: []
}
```

**后端验证逻辑**:
- ✅ 接收空字符串时，不作为搜索条件
- ✅ 接收非空字符串时，执行LIKE模糊匹配
- ✅ SQL注入防护测试：输入`'; DROP TABLE orders; --`

### 2.2 标题搜索测试
**测试目标**: 验证工单标题模糊搜索功能

**前端发送数据样例**:
```javascript
// 中文搜索
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "申请",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: []
}

// 部分匹配
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "test",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: []
}

// 特殊字符搜索
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "测试-工 单",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: []
}

{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "\n",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: []
}
```

**后端验证逻辑**:
- ✅ 中文字符正确匹配
- ✅ 部分字符匹配返回相关结果
- ✅ 特殊字符正确查询
- ✅ 大小写敏感

### 2.3 日期范围搜索测试
**测试目标**: 验证申请日期时间范围精确搜索

**前端发送数据样例**:
```javascript
// 完整日期范围
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "",
  appliedStartDate: "2024-07-01T00:00:00Z",
  appliedEndDate: "2024-07-31T23:59:59Z",
  orderTypes: []
}

// 只有开始日期
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "",
  appliedStartDate: "2024-07-15T00:00:00Z",
  appliedEndDate: null,
  orderTypes: []
}

// 只有结束日期
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "",
  appliedStartDate: null,
  appliedEndDate: "2024-07-15T23:59:59Z",
  orderTypes: []
}
```

**后端验证逻辑**:
- ✅ 完整范围：created_at BETWEEN startDate AND endDate
- ✅ 只有开始：created_at >= startDate
- ✅ 只有结束：created_at <= endDate
- ✅ null值被忽略，不作为搜索条件

### 2.4 组合搜索测试
**测试目标**: 验证多个搜索条件的AND关系

**前端发送数据样例**:
```javascript
{
  pageNum: 1,
  pageSize: 10,
  orderId: "ORD",
  title: "申请",
  appliedStartDate: "2024-07-01T00:00:00Z",
  appliedEndDate: "2024-07-31T23:59:59Z",
  orderTypes: ["common", "ks_server_apply"]
}
```

**后端验证逻辑**:
- ✅ 所有非空条件同时生效
- ✅ 返回同时满足所有条件的工单
- ✅ 分页逻辑正确应用

---

## 三、工单类型筛选测试

### 3.1 单选筛选测试
**前端发送数据样例**:
```javascript
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: ["common"]
}
```

### 3.2 多选筛选测试
**前端发送数据样例**:
```javascript
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: ["common", "ks_server_apply", "sql_audit_execute"]
}
```

### 3.3 全选测试
**前端发送数据样例**:
```javascript
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: [] // 空数组表示全选
}
```

**后端验证逻辑**:
- ✅ 空数组时不应用工单类型筛选
- ✅ 单个类型时：WHERE order_type = 'common'
- ✅ 多个类型时：WHERE order_type IN ('common', 'ks_server_apply')

---

## 四、动态筛选选项测试

### 4.1 order_types字段返回测试
**测试目标**: 验证后端新增的order_types字段

**期望后端响应**:
```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "total": 50,
    "orders": [...],
    "order_types": [
      "common",
      "ks_server_apply", 
      "sql_audit_execute",
      "txy_redis_apply",
      "new_model_node"
    ]
  }
}
```

**验证点**:
- ✅ order_types字段存在且为字符串数组
- ✅ 包含所有用户拥有的工单的工单类型
- ✅ 当出现新的工单类型时，字段内容动态更新

---

## 五、数据校验与错误处理测试

### 5.1 前端校验测试

**5.1.1 空格处理测试**
**测试数据**:
- 输入：`"   "`（纯空格）
- 期望：前端处理为`""`并显示提示"请输入有效单号"

**5.1.2 日期范围校验测试**
**测试数据**:
- 开始日期：2024-07-15
- 结束日期：2024-07-10（早于开始日期）
- 期望：显示错误提示，自动清除结束日期

### 5.2 后端错误处理测试

**5.2.1 参数异常测试**
```javascript
// 无效页码
{
  pageNum: -1,
  pageSize: 10,
  orderId: "",
  title: "",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: []
}

// 过大页面大小
{
  pageNum: 1,
  pageSize: 10000,
  orderId: "",
  title: "",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: []
}

// 无效工单类型
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: ["invalid_type", "another_invalid"]
}
```

**期望后端处理**:
- ✅ 无效页码默认为1
- ✅ 过大页面大小限制为最大值（如100）
- ✅ 无效工单类型被忽略或返回错误信息

---

## 六、性能与UX测试

### 6.1 加载状态测试
**验证点**:
- ✅ 数据加载时显示骨架屏
- ✅ 骨架屏样式与实际内容布局一致
- ✅ 加载完成后骨架屏正确消失

### 6.2 大数据量测试
**测试数据**:
- 总工单数：10,000条
- 单页显示：100条
- 验证分页性能和渲染性能

---

## 七、兼容性测试

### 7.1 浏览器兼容性
**测试浏览器**:
- ✅ Chrome 90+
- ✅ Firefox 85+
- ✅ Safari 14+
- ✅ Edge 90+

### 7.2 设备兼容性
**测试设备**:
- ✅ 桌面端（1920x1080）
- ✅ 笔记本（1366x768）
- ✅ 平板端（768x1024）
- ✅ 手机端（375x667）

---

## 八、API接口测试数据汇总

### 8.1 典型请求参数组合

```javascript
// 1. 初始加载
{
  pageNum: 1,
  pageSize: 10,
  orderId: "",
  title: "",
  appliedStartDate: null,
  appliedEndDate: null,
  orderTypes: []
}

// 2. 复杂搜索
{
  pageNum: 2,
  pageSize: 20,
  orderId: "ORD-2024",
  title: "服务器申请",
  appliedStartDate: "2024-07-01T00:00:00Z",
  appliedEndDate: "2024-07-31T23:59:59Z",
  orderTypes: ["ks_server_apply", "tx_server_apply"]
}

// 3. 边界情况
{
  pageNum: 1,
  pageSize: 1,
  orderId: "!@#$%^&*()",
  title: "测试'\"<>&",
  appliedStartDate: "1970-01-01T00:00:00Z",
  appliedEndDate: "2099-12-31T23:59:59Z",
  orderTypes: ["nonexistent_type"]
}
```

### 8.2 期望响应数据格式

```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "total": 1250,
    "orders": [
      {
        "id": "ORD-20240707-001",
        "title": "金山云服务器申请 - 开发环境扩容",
        "order_type": "ks_server_apply",
        "order_type_name": "金山云服务器申请",
        "creator_name": "张三",
        "status": "PENDING",
        "created_at": "2024-07-07T10:00:00Z"
      }
    ],
    "order_types": [
      "common",
      "ks_server_apply", 
      "tx_server_apply",
      "sql_audit_execute",
      "txy_redis_apply"
    ]
  }
}
```

---

## 九、回归测试清单

### 9.1 核心功能回归
- ✅ 工单列表正常加载
- ✅ 分页功能正常
- ✅ 表格排序功能正常
- ✅ 工单详情查看功能正常

### 9.2 新功能回归  
- ✅ 标题列显示正确
- ✅ 搜索功能工作正常
- ✅ 工单类型筛选功能正常
- ✅ 动态筛选选项更新正常
- ✅ 错误提示显示正确

### 9.3 集成测试
- ✅ 搜索 + 筛选组合使用
- ✅ 搜索 + 分页组合使用
- ✅ 筛选 + 分页组合使用
- ✅ 多条件组合搜索

---

## 🚀 测试执行建议

### 1. 测试优先级
1. **P0**：基础工单列表加载、标题列显示、搜索功能
2. **P1**：工单类型筛选、动态选项更新、错误提示
3. **P2**：性能优化、兼容性、边界情况

### 2. 测试环境要求
- 后端API已实现新的order_types字段
- 测试数据包含多种工单类型
- 测试数据包含各种长度的标题字段

### 3. 测试工具推荐
- **API测试**：Postman/Insomnia
- **前端测试**：Jest + React Testing Library
- **E2E测试**：Cypress/Playwright
- **性能测试**：Chrome DevTools

### 4. 测试交付物
- ✅ 测试用例执行报告
- ✅ 缺陷清单和修复验证
- ✅ 性能测试报告
- ✅ 兼容性测试报告

---

## 📝 附录：测试数据样本

### A.1 完整的Mock工单数据
```json
{
  "orders": [
    {
      "id": "ORD-20240701-001",
      "title": "金山云服务器申请 - 测试环境扩容",
      "order_type": "ks_server_apply",
      "order_type_name": "金山云服务器申请",
      "creator_name": "张三",
      "status": "PENDING", 
      "created_at": "2024-07-01T09:00:00Z"
    },
    {
      "id": "ORD-20240701-002", 
      "title": "",
      "order_type": "common",
      "order_type_name": "通用工单",
      "creator_name": "李四",
      "status": "APPROVED",
      "created_at": "2024-07-01T10:30:00Z"
    },
    {
      "id": "ORD-20240701-003",
      "title": "这是一个超级超级超级超级超级超级超级超级超级长的工单标题，用来测试省略号显示和Tooltip功能是否正常工作，包含了很多很多的文字内容",
      "order_type": "sql_audit_execute", 
      "order_type_name": "sql审计执行工单",
      "creator_name": "王五",
      "status": "REJECTED",
      "created_at": "2024-07-01T14:15:00Z"
    }
  ],
  "order_types": [
    "common",
    "ks_server_apply",
    "sql_audit_execute"
  ]
}
```

此测试方案覆盖了WFO工单审批页面的所有新增功能，确保前后端数据传递的正确性和用户体验的完整性。
