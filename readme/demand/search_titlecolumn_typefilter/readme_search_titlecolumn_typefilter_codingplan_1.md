# WFO 审批工单页面新增功能开发计划迭代一

## 目标功能概述
基于需求文档和详细设计文档，本次迭代需要实现：
1. **工单列表新增"工单标题"列** - 支持省略号显示和 Tooltip 悬停
2. **新增搜索功能** - 单号模糊搜索、标题模糟搜索，通过"搜索"按钮触发
3. **新增工单类型筛选功能** - 类似 Excel 筛选，支持多选，立即触发数据刷新
4. **骨架屏加载提示** - 数据加载时显示骨架屏而非传统 loading
5. **完整的组件化架构** - 按照详细设计的模块拆分进行实现

## 现有代码结构分析

### 受影响的现有模块
根据调用链文档和详细设计分析，主要涉及以下模块：
```
src/
├── pages/auditOrder/          # 现有审批工单页面（主要修改区域）
├── request/                   # 现有 API 服务层（需要扩展搜索接口）
│   ├── api.js                # 需要新增搜索和工单类型接口
│   └── index.js              # 复用现有的 service 封装
├── components/                # 需要新增多个组件
├── utils/                     # 工具函数（字符串处理等）
└── router/                    # 路由配置（基本不变）
```

### 复用策略
- 复用现有的 BaseLayout 布局结构和路由体系
- 复用现有的 `src/request/index.js` 中的 service 封装
- 复用现有的 Ant Design 组件样式体系
- 复用现有的 React Class Component 模式（保持一致性）
- 复用现有的 styled-components 样式处理方式

## 渐进式小步迭代开发计划

### 步骤 1: 基础工具函数和 API 接口准备
**目标**: 创建支撑后续功能的基础设施
**可验证性**: 程序正常启动，无新增功能界面变化

**开发内容**:
1. 创建 `src/utils/string.js` - 字符串处理工具（按详细设计）
   ```javascript
   // 实现 trimAndHandleEmpty 函数
   export function trimAndHandleEmpty(str) {
     if (typeof str !== 'string' || str === null || str === undefined) {
       return '';
     }
     const trimmedStr = str.trim();
     return trimmedStr === '' ? '' : trimmedStr;
   }
   ```

2. 扩展 `src/request/api.js` - 新增工单相关接口（复用现有service结构）
   ```javascript
   // 新增搜索工单的 API 方法，参数预留完整结构
   export const requestMyAuditOrder = (args) => {
     // args: { orderId, title, orderTypes: [], appliedStartDate, appliedEndDate }
     return service.post('/auditOrder/my', args);
   }
   
   // 新增获取工单类型列表的 API 方法
   export const requestOrderTypes = () => {
     return service.post('/order/types', {});
   }
   ```

**验证标准**: 
- 应用正常启动
- 新增的工具函数可以被正确导入并调用
- API 接口已定义，符合现有的 service.post 调用模式

---

### 步骤 2: OrderTable 组件创建和标题列实现
**目标**: 创建 OrderTable 组件，实现工单标题列显示
**可验证性**: 表格组件正常显示，包含标题列的省略号和 Tooltip

**开发内容**:
1. 创建 `src/components/OrderTable/index.js` - 工单表格组件
   ```javascript
   import React, { Component } from 'react';
   import { Table, Tooltip } from 'antd';
   import { AuditedTable } from './styles';
   
   class OrderTable extends Component {
     getColumns() {
       return [
         // 现有列...
         {
           title: '工单标题',
           dataIndex: 'title',
           key: 'title',
           width: 200,
           ellipsis: { showTitle: false },
           render: (title) => (
             <Tooltip placement="topLeft" title={title}>
               {title || ''}
             </Tooltip>
           ),
         },
         // 其他列...
       ];
     }
   }
   ```

2. 创建 `src/components/OrderTable/styles.js` - 表格样式
   ```javascript
   import styled from 'styled-components';
   
   export const AuditedTable = styled.div`
     margin-top: 2%;
     background-color: #fff;
     border-radius: 8px;
     padding: 20px;
   `;
   ```

**验证标准**:
- OrderTable 组件可以单独渲染
- 工单标题列正确显示
- 标题超长时显示省略号，悬停显示完整内容

---

### 步骤 3: SearchForm 组件开发
**目标**: 创建搜索表单组件，支持单号和标题搜索
**可验证性**: 搜索表单正常显示，输入和按钮交互正常

**开发内容**:
1. 创建 `src/components/SearchForm/index.js` - 按详细设计实现
   ```javascript
   import React, { Component } from 'react';
   import { Input, Button, Form } from 'antd';
   import { SearchFormContainer, StyledFormItem } from './styles';
   import { trimAndHandleEmpty } from '../../utils/string';
   
   class SearchForm extends Component {
     constructor(props) {
       super(props);
       this.state = {
         orderId: props.initialSearchParams?.orderId || '',
         title: props.initialSearchParams?.title || '',
       };
     }
     
     handleInputChange = (e) => {
       const { name, value } = e.target;
       this.setState({ [name]: value });
     };
     
     handleSearchClick = () => {
       const { onSearch } = this.props;
       const searchParams = {
         orderId: trimAndHandleEmpty(this.state.orderId),
         title: trimAndHandleEmpty(this.state.title),
       };
       onSearch && onSearch(searchParams);
     };
     
     // 完整的 render 方法实现...
   }
   ```

2. 创建 `src/components/SearchForm/styles.js` - 样式定义
   ```javascript
   import styled from 'styled-components';
   import { Form } from 'antd';
   
   export const SearchFormContainer = styled.div`
     padding: 20px;
     background-color: #fff;
     border-radius: 8px;
     margin-bottom: 20px;
   `;
   
   export const StyledFormItem = styled(Form.Item)`
     margin-bottom: 16px;
   `;
   ```

**验证标准**:
- SearchForm 组件可以独立渲染
- 单号和标题输入框可以正常输入修改
- 搜索按钮可以点击，触发 onSearch 回调
- 空格处理逻辑正确工作

---

### 步骤 4: 搜索功能集成到主页面
**目标**: 将搜索表单集成到审批工单页面，实现搜索功能
**可验证性**: 可以进行单号和标题的模糊搜索

**开发内容**:
1. 修改现有的审批工单页面组件
2. 集成 SearchForm 组件:
   ```javascript
   // 在页面组件中
   handleSearch = (searchParams) => {
     this.setState({ loading: true });
     
     // 调用搜索 API
     searchOrders(searchParams)
       .then(response => {
         this.setState({ 
           orderList: response.data || [],
           loading: false 
         });
       })
       .catch(error => {
         message.error('搜索失败');
         this.setState({ loading: false });
       });
   };
   
   render() {
     return (
       <div>
         <SearchForm onSearch={this.handleSearch} />
         {/* 现有的表格组件 */}
       </div>
     );
   }
   ```

**验证标准**:
- 页面显示搜索表单
- 输入搜索条件后点击搜索，表格数据会更新
- 空值和空格处理正确
- 搜索过程中显示加载状态

---

### 步骤 5: 工单类型筛选组件开发
**目标**: 创建工单类型筛选弹窗组件
**可验证性**: 筛选按钮和弹窗正常显示，多选功能正常

**开发内容**:
1. 创建 `src/components/OrderTypeFilter/index.js`
   ```javascript
   import React, { Component } from 'react';
   import { Popover, Checkbox, Button } from 'antd';
   import { FilterOutlined } from '@ant-design/icons';
   
   class OrderTypeFilter extends Component {
     state = {
       visible: false,
       selectedTypes: [], // 选中的工单类型
       allTypes: [], // 所有工单类型
     };
     
     componentDidMount() {
       // 获取工单类型列表
       this.fetchOrderTypes();
     }
     
     fetchOrderTypes = () => {
       getOrderTypes().then(response => {
         const types = response.data || [];
         this.setState({ 
           allTypes: types,
           selectedTypes: types.map(t => t.OrderType) // 默认全选
         });
       });
     };
     
     handleTypeChange = (checkedValues) => {
       this.setState({ selectedTypes: checkedValues });
       // 立即触发筛选
       const { onFilter } = this.props;
       onFilter && onFilter(checkedValues);
     };
   }
   ```

**验证标准**:
- 筛选按钮可以点击
- 弹窗正常显示工单类型列表
- 多选功能正常工作
- 默认处于全选状态

---

### 步骤 6: 工单类型筛选功能集成
**目标**: 将筛选功能集成到表格列头，实现完整的筛选逻辑
**可验证性**: 筛选功能完全可用，选择不同类型立即更新表格

**开发内容**:
1. 修改工单表格的"工单类型"列配置
   ```javascript
   {
     title: (
       <span>
         工单类型
         <OrderTypeFilter 
           onFilter={this.handleTypeFilter}
           style={{ marginLeft: 8 }}
         />
       </span>
     ),
     dataIndex: 'orderType',
     key: 'orderType',
     // ... 其他配置
   }
   ```

2. 实现筛选逻辑:
   ```javascript
   handleTypeFilter = (selectedTypes) => {
     this.setState({ selectedOrderTypes: selectedTypes });
     // 重新加载数据，传递筛选条件
     this.loadOrderList({ orderTypes: selectedTypes });
   };
   ```

**验证标准**:
- 工单类型列显示筛选按钮
- 点击筛选按钮显示类型选择弹窗
- 选择不同类型后，表格立即更新显示对应工单
- 筛选与搜索功能可以同时使用

---

### 步骤 7: 功能整合和优化
**目标**: 整合所有功能，优化用户体验
**可验证性**: 所有功能协同工作，用户体验流畅

**开发内容**:
1. 优化搜索和筛选的交互逻辑
   - 确保搜索条件和筛选条件能够正确组合
   - 优化加载状态显示

2. 添加错误处理和用户提示
   ```javascript
   // 统一的错误处理
   handleError = (error, action) => {
     console.error(`${action}失败:`, error);
     message.error(`${action}失败，请重试`);
   };
   ```

3. 性能优化
   - 防抖处理（为后续迭代准备）
   - 状态管理优化

4. 样式调整和响应式优化

**验证标准**:
- 所有功能正常协同工作
- 错误情况有合适的用户提示
- 界面美观，符合现有系统风格
- 在不同屏幕尺寸下显示正常

---

## 技术要点说明

### 状态管理策略
- 采用组件内部 state 管理，避免过度复杂化
- 搜索条件和筛选条件通过 props 在组件间传递
- 为后续迭代预留 Zustand 状态管理接入点

### 样式处理
- 复用现有的 Ant Design 主题配置
- 使用 styled-components 进行样式扩展
- 确保新组件与现有系统风格一致

### API 集成
- 遵循现有的 API 调用模式
- 统一的错误处理机制
- 支持后端 ProtoBuf 数据格式

### 扩展性考虑
- 组件设计支持后续功能扩展
- API 接口设计考虑未来的参数扩展
- 为日期时间范围搜索预留接口结构

---

## 后续迭代预览

### 迭代二计划（本次不实现）
- 申请日期时间范围搜索
- 日期校验和自动处理逻辑
- 防抖搜索优化

### 迭代三计划（本次不实现）
- 工单详情抽屉
- 分页功能完整实现
- 高级搜索功能

---

## 风险点和注意事项

1. **性能考虑**: 大量工单数据的处理和渲染性能
2. **浏览器兼容性**: 确保新功能在目标浏览器中正常工作

---

**开发完成标准**: 
每个步骤完成后，整个应用程序必须能够成功启动并保持可运行状态，新增功能能够部分或完整地展示效果。监督者可以立即验证每个步骤的实现效果。