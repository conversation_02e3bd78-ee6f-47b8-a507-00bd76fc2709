# WFO 系统审批工单页面 - 前端技术选型方案

## 1. 前端开发框架：React (搭配 Hooks API)

* **核心优势：**
    * **组件化开发：** 将页面拆分为独立的、可复用的组件，如工单表格、搜索表单、日期选择器等，极大提升代码的可维护性和开发效率。
    * **声明式 UI：** 直观地描述 UI 应该呈现的状态，React 会高效地更新 DOM，简化了复杂交互的实现。
    * **高性能渲染：** 通过虚拟 DOM 机制，最大限度地减少真实 DOM 操作，确保页面在高并发数据更新时的流畅性。
    * **庞大生态与社区：** 拥有全球最活跃的开发者社区和最丰富的第三方库支持，遇到任何问题都能迅速找到解决方案和资源。
* **适用性：** 应对审批工单页面中复杂的表格展示、动态搜索、多条件筛选以及实时交互的需求，React 都能提供强大的支持和优异的性能。

---

## 2. UI 组件库：Ant Design

* **核心优势：**
    * **企业级设计语言：** 提供一套专业、统一的 UI 设计规范和高质量的组件，界面风格稳重、功能全面，非常符合后台管理系统的需求。
    * **强大的 Table 组件：**
        * 完美支持工单标题列的**最大宽度、单行显示、超出省略（`ellipsis`）和鼠标悬停显示完整内容（`tooltip`）**。
        * 内置丰富的表格功能，包括排序、分页、固定列、行选择等，并提供灵活的**列筛选**功能，可轻松实现工单类型多选筛选。
    * **完善的表单体系：**
        * `Input` 组件用于单号和标题的输入。
        * `DatePicker.RangePicker` 完全满足**精确到时分秒**的日期时间范围选择需求。
        * `Form` 组件提供强大的表单管理和校验能力，可实现**结束日期大于开始日期的实时校验**，并显示**小字提示**。
    * **丰富的交互提示：**
        * `message` 组件用于页面顶部的**非模态提示**（如“部分搜索条件无效”），支持自动消失和手动关闭。
        * `Form.Item` 结合校验规则，提供输入框下方的局部反馈和边框提示色。
    * **骨架屏支持：** 提供了 `Skeleton` 组件，用于数据加载时的专业**骨架屏**占位提示，提升用户体验。
* **适用性：** Ant Design 的全面性和功能强大，将极大加速页面开发进程，并确保最终界面专业、易用。

---

## 3. 状态管理：React Query + Zustand

* **服务器数据状态管理：React Query**
    * **核心优势：** 专门用于处理异步服务器数据状态的库，它能自动管理数据的**获取、缓存、更新、过期、重试**等复杂逻辑。
    * **效益：** 极大简化了数据请求代码，减少了手动管理 `loading`、`error`、`data` 状态的繁琐工作。当搜索、筛选条件变化时，能高效触发数据刷新并优化用户体验。
* **客户端 UI 状态管理：Zustand**
    * **核心优势：** 一个非常轻量、高性能的状态管理库，基于 Hooks API，学习曲线平缓。
    * **效益：** 用于管理纯前端的 UI 状态，如搜索输入框的值、筛选弹窗的打开状态、本地校验错误信息等，与 React Query 各司其职，职责清晰。
* **适用性：** 这种组合将使数据流管理变得清晰高效，提升应用的性能和可维护性。

---

## 4. 数据请求库：Axios

* **核心优势：**
    * **基于 Promise：** 异步请求处理方便，符合现代 JavaScript 编程范式。
    * **请求/响应拦截器：** 方便统一处理认证 Token、错误码等公共逻辑。
    * **取消请求：** 支持请求取消功能，对于频繁触发的搜索和筛选操作，能有效避免不必要的网络请求和竞态条件。
    * **广泛使用：** 业界标准，文档丰富，社区活跃。
* **适用性：** 作为与后端交互的核心工具，Axios 将确保数据请求的健壮性和可控性。

---

## 5. 路由管理：React Router

* **核心优势：**
    * **声明式路由：** 通过组件化的方式定义路由，清晰直观。
    * **功能丰富：** 支持嵌套路由、动态路由、路由守卫等，能灵活管理页面之间的跳转和参数传递。
* **适用性：** 作为 React 应用的“标准”路由解决方案，它将确保页面导航的稳定性和可扩展性。

---

## 6. 其他关键技术点

* **防抖（Debounce）：** 对于工单类型筛选（立即触发数据刷新）和搜索框输入这类可能导致频繁请求的操作，将通过自定义 Hook 或 `lodash.debounce` 等工具实现防抖，减少不必要的网络请求，优化后端压力和用户体验。
* **空值/空格处理：** 前端在将搜索条件（单号、标题）传递给后端前，会进行统一处理：去除首尾空格，若仅包含空格则将对应字段值置为空字符串（`""`），后端将空字符串视为“未提供该搜索条件”。
* **日期字段 ProtoBuf 零值处理：** 日期选择器未填写时，前端将对应日期字段转换为 `google.protobuf.Timestamp` 的零值（空值）传递给后端，与后端协议保持一致。

---