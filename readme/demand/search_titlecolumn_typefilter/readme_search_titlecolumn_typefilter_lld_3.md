# WFO 系统审批工单页面 - 迭代三详细设计

本文件汇总了 WFO 系统审批工单页面在**迭代三**中的详细设计内容，旨在指导开发人员进行编码实现。本设计基于迭代一和迭代二的成果，并充分考虑了整个项目的迭代演进和扩展性。

---

## 项目结构与总体设计

### 目录结构树 (Directory Tree)

```plain
src/
├── api/                  # API 服务定义
│   └── order.js          # 工单相关的 API 请求
├── components/           # 可复用的通用 UI 或业务组件
│   ├── SearchForm/
│   │   └── index.js      # 搜索表单组件
│   │   └── styles.js     # SearchForm 样式定义
│   ├── OrderTable/
│   │   └── index.js      # 工单列表表格组件
│   │   └── styles.js     # OrderTable 样式定义
│   ├── OrderPagination/
│   │   └── index.js      # 分页组件
│   │   └── styles.js     # OrderPagination 样式定义
│   ├── OrderDetailDrawer/
│   │   └── index.js      # 工单详情抽屉 (迭代一占位，未来使用)
│   ├── OrderTypeFilter/  # 新增：工单类型筛选弹窗组件 (迭代三实现)
│   │   └── index.js
│   │   └── styles.js
│   └── common/           # 通用UI组件或工具 (例如Loading/EmptyState)
│       ├── LoadingSkeleton/ # 骨架屏组件
│       │   └── index.js
│       │   └── styles.js
│       └── GlobalMessage/ # 新增：全局非模态提示 (迭代三实现)
│           └── index.js
│           └── styles.js
├── hooks/                # 自定义 React Hooks
│   └── useDebounce.js    # 防抖 Hook (迭代三使用)
├── pages/
│   └── auditOrder/       # 审批工单页面
│       └── index.js      # AuditOrder 组件主文件
│       └── styles.js     # AuditOrder 页面级样式
├── stores/               # Zustand 状态管理 store
│   └── uiStore.js        # 例如，用于管理一些全局 UI 状态 (迭代三使用)
├── utils/                # 工具函数
│   ├── date.js           # 日期处理工具
│   └── string.js         # 字符串处理工具 (如去除空格)
└── App.js                # 应用入口
└── index.js              # 应用初始化
```

---

### 整体逻辑和交互时序图

**核心工作流程（迭代三）：** 在迭代二的基础上，增加工单类型筛选、全局非模态提示和搜索条件局部反馈。用户可以通过点击“工单类型”列的筛选按钮，在弹窗中选择多个工单类型进行筛选，筛选结果将立即（防抖后）刷新。当搜索条件无效时，页面顶部会显示提示，同时相关输入框会获得视觉反馈。

```mermaid
sequenceDiagram
    participant User as 用户
    participant AuditOrderPage as AuditOrder页面组件 (pages/auditOrder/index.js)
    participant SearchForm as 搜索表单组件 (components/SearchForm/index.js)
    participant OrderTable as 工单表格组件 (components/OrderTable/index.js)
    participant OrderTypeFilter as 工单类型筛选弹窗 (components/OrderTypeFilter/index.js)
    participant GlobalMessage as 全局提示组件 (components/common/GlobalMessage/index.js)
    participant useDebounce as useDebounce Hook (hooks/useDebounce.js)
    participant uiStore as uiStore (stores/uiStore.js)
    participant BackendAPI as 后端API服务 (api/order.js)

    User->>AuditOrderPage: 访问审批工单页面 (同迭代二，初始加载数据)

    User->>OrderTable: 点击“工单类型”列的筛选按钮
    OrderTable->>OrderTypeFilter: 弹出筛选弹窗 (传递当前选中类型和所有类型列表)
    User->>OrderTypeFilter: 勾选/取消勾选工单类型
    OrderTypeFilter->>useDebounce: 将新的选中类型列表传递给防抖Hook
    useDebounce-->>OrderTypeFilter: 防抖时间结束后，返回新的选中类型列表
    OrderTypeFilter->>OrderTable: 触发筛选事件 (onFilterChange, 传递选中的工单类型ID数组)
    OrderTable->>AuditOrderPage: 触发工单类型筛选事件 (传递选中的工单类型ID数组)
    AuditOrderPage->>BackendAPI: 发送带工单类型筛选条件和现有搜索/分页条件的请求 (requestMyAuditOrder)
    BackendAPI-->>AuditOrderPage: 返回过滤后的工单数据
    AuditOrderPage->>OrderTable: 更新并渲染过滤后的工单数据

    User->>SearchForm: 输入搜索条件 (单号/标题/日期范围)
    alt 日期范围校验失败
        SearchForm->>SearchForm: 实时校验，显示小字提示
        SearchForm->>AuditOrderPage: (通过 onSearch 告知) 存在无效搜索条件 (hasValidationErrors: true)
        AuditOrderPage->>uiStore: 调用 setGlobalMessage 显示全局非模态提示
        uiStore-->>GlobalMessage: 更新全局提示状态
        GlobalMessage->>User: 显示页面顶部提示：“部分搜索条件无效...”
        SearchForm->>User: 对应输入框边框短暂变色 (局部反馈)
    end
    User->>SearchForm: 点击“搜索”按钮
    SearchForm->>AuditOrderPage: 触发搜索事件 (传递单号、标题、日期范围等参数，以及 hasValidationErrors)
    AuditOrderPage->>BackendAPI: 发送带搜索条件和当前工单类型筛选/分页条件的请求 (requestMyAuditOrder)
    BackendAPI-->>AuditOrderPage: 返回过滤后的工单数据
    AuditOrderPage->>OrderTable: 更新并渲染过滤后的工单数据
```

---

## 数据实体结构深化

本项目的数据实体结构与迭代二保持一致，主要与后端 API 交互。前端将使用 `uiStore` 来管理全局的 UI 状态，例如全局提示信息。

### 核心数据模型 (前端视角)

```mermaid
erDiagram
    USER {
        string id PK
        string name
        string email
        string department
    }

    ORDER_TYPE {
        string id PK "OrderType (英文ID)"
        string name "OrderTypeName (中文名称)"
        string description
    }

    ORDER {
        string id PK
        string title "工单标题"
        string description "工单描述"
        string typeId FK "工单类型ID"
        string creatorId FK "创建人ID"
        string creatorName "创建人姓名"
        string auditorId FK "当前审批人ID (nullable)"
        string auditorName "当前审批人姓名 (nullable)"
        string status "工单状态 (Pending, Approved, Rejected)"
        timestamp createdAt "创建时间"
        timestamp updatedAt "更新时间"
        timestamp completedAt "完成时间 (nullable)"
        string attachments "附件列表 (JSON Array, nullable)"
    }

    USER ||--o{ ORDER : creates
    USER ||--o{ ORDER : audits
    ORDER_TYPE ||--o{ ORDER : has
```

**前端 `Order` 接口定义 (TypeScript 伪代码)**

```typescript
// 定义后端返回的工单类型枚举，硬编码在前端
export enum OrderTypeEnum {
  LEAVE = 'LEAVE',
  OVERTIME = 'OVERTIME',
  // ... 其他工单类型，确保与后端枚举一致
}

export interface OrderTypeInfo {
  id: OrderTypeEnum; // 英文 ID
  name: string;      // 中文名称
}

export interface Order {
  id: string;
  title: string;
  description: string;
  orderType: OrderTypeEnum; // 英文ID
  orderTypeName: string; // 中文名称
  creatorId: string;
  creatorName: string;
  auditorId?: string; // 可选
  auditorName?: string; // 可选
  status: string; // 根据后端实际状态枚举定义
  createdAt: string; // ISO 8601 格式时间字符串
  updatedAt: string;
  completedAt?: string; // 可选
  attachments?: string[]; // 可选
}

export interface GetOrdersResponse {
  code: number;
  message: string;
  data: {
    total: number;
    list: Order[];
  };
}
```

---

## 配置项

本项目在迭代三中仍然不涉及复杂的运行配置项。所有常量（如硬编码的工单类型列表）将直接定义在相关文件中。未来如果需要更多配置，可以考虑引入 `dotenv` 或专门的 `config.js` 文件。

---

## 模块化文件详解 (File-by-File Breakdown)

### `src/pages/auditOrder/index.js`

a. **文件用途说明**
   该文件是审批工单页面的根组件 `AuditOrderPage`。在迭代三中，它将增加对 `OrderTypeFilter` 筛选条件的管理，并利用 `uiStore` 和 `GlobalMessage` 组件来显示全局非模态提示。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       AuditOrderPage --|> React.Component
       AuditOrderPage "1" --o "1" SearchForm : contains
       AuditOrderPage "1" --o "1" OrderTable : contains
       AuditOrderPage "1" --o "1" OrderPagination : contains
       AuditOrderPage "1" --o "1" LoadingSkeleton : manages
       AuditOrderPage "1" --o "1" GlobalMessage : manages visibility
       AuditOrderPage -- "1" uiStore : uses
       AuditOrderPage : +state
       AuditOrderPage : +componentDidMount()
       AuditOrderPage : +handleSearch(params, hasValidationErrors)
       AuditOrderPage : +handlePageChange(pageNum, pageSize)
       AuditOrderPage : +handleOrderTypeFilterChange(types)
       AuditOrderPage : +render()
   ```

c. **函数/方法详解**

#### `constructor(props)`
- 用途: 初始化组件状态。
- 输入参数: `props` (React 组件属性)
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/pages/auditOrder/index.js
  import React, { Component } from 'react';
  import { message } from 'antd';
  import { AuditOrderContainer } from './styles';
  import SearchForm from '../../components/SearchForm';
  import OrderTable from '../../components/OrderTable';
  import OrderPagination from '../../components/OrderPagination';
  import LoadingSkeleton from '../../components/common/LoadingSkeleton';
  import GlobalMessage from '../../components/common/GlobalMessage'; // 引入全局提示组件
  import { requestMyAuditOrder } from '../../api/order';
  import useUiStore from '../../stores/uiStore'; // 引入 Zustand store

  class AuditOrderPage extends Component {
    constructor(props) {
      super(props);
      this.state = {
        searchParams: {
          orderId: '',
          title: '',
          appliedStartDate: null,
          appliedEndDate: null,
          // 迭代三：新增工单类型筛选参数
          orderTypes: [], // 存储选中的工单类型ID数组
        },
        orderList: [],
        totalOrders: 0,
        loading: false,
        pagination: {
          pageNum: 1,
          pageSize: 10,
        },
        hasInvalidSearchConditions: false, // 标记是否存在无效的搜索条件
      };
      this.fetchOrders = this.fetchOrders.bind(this);
      this.handleSearch = this.handleSearch.bind(this);
      this.handlePageChange = this.handlePageChange.bind(this);
      this.handleOrderTypeFilterChange = this.handleOrderTypeFilterChange.bind(this); // 绑定工单类型筛选事件
    }
  ```

#### `componentDidMount()`
- 用途: 组件挂载后，立即发起首次工单列表数据请求。
- 输入参数: 无
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/pages/auditOrder/index.js (续)
    componentDidMount() {
      this.fetchOrders(this.state.searchParams, this.state.pagination);
    }
  ```

#### `fetchOrders(params, pagination)`
- 用途: 根据给定的搜索参数和分页信息，向后端请求工单数据，并更新组件状态。
- 输入参数:
    - `params`: `Object`，包含搜索条件的键值对。
    - `pagination`: `Object`，包含分页信息的键值对。
- 输出: `Promise<void>`
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/pages/auditOrder/index.js (续)
  import { toISOStringOrNull } from '../../utils/date';

    async fetchOrders(params, pagination) {
      this.setState({ loading: true });
      try {
        const requestParams = {
          pageNum: pagination.pageNum,
          pageSize: pagination.pageSize,
          orderId: params.orderId,
          title: params.title,
          appliedStartDate: toISOStringOrNull(params.appliedStartDate),
          appliedEndDate: toISOStringOrNull(params.appliedEndDate),
          // 迭代三：传递工单类型筛选参数
          orderTypes: params.orderTypes, // 数组直接传递
        };

        const response = await requestMyAuditOrder(requestParams);
        if (response.code === 0) {
          this.setState({
            orderList: response.data.list,
            totalOrders: response.data.total,
            hasInvalidSearchConditions: false, // 成功获取数据，清除无效条件提示
          });
          // 迭代三：如果之前有全局提示，成功获取数据后清除
          useUiStore.getState().clearGlobalMessage();
        } else {
          message.error(response.message || '获取工单列表失败');
          this.setState({ orderList: [], totalOrders: 0 });
        }
      } catch (error) {
        console.error('Failed to fetch orders:', error);
        message.error('网络错误，请稍后重试');
        this.setState({ orderList: [], totalOrders: 0 });
      } finally {
        this.setState({ loading: false });
      }
    }
  ```

#### `handleSearch(newSearchParams, hasValidationErrors)`
- 用途: 处理 `SearchForm` 组件提交的搜索事件，更新搜索参数并重新发起数据请求。同时根据 `hasValidationErrors` 控制全局提示。
- 输入参数:
    - `newSearchParams`: `Object`，来自 `SearchForm` 的最新搜索条件。
    - `hasValidationErrors`: `Boolean`, `SearchForm` 告知是否存在日期校验错误。
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/pages/auditOrder/index.js (续)
    handleSearch(newSearchParams, hasValidationErrors) {
      this.setState(
        (prevState) => ({
          searchParams: {
            ...prevState.searchParams, // 保留现有工单类型筛选条件
            orderId: newSearchParams.orderId,
            title: newSearchParams.title,
            appliedStartDate: newSearchParams.appliedStartDate,
            appliedEndDate: newSearchParams.appliedEndDate,
          },
          pagination: {
            ...prevState.pagination,
            pageNum: 1, // 搜索时重置页码到第一页
          },
          hasInvalidSearchConditions: hasValidationErrors,
        }),
        () => {
          // 状态更新完成后，发起数据请求
          this.fetchOrders(this.state.searchParams, this.state.pagination);
          // 迭代三：如果存在校验错误，显示非模态提示
          if (this.state.hasInvalidSearchConditions) {
            useUiStore.getState().setGlobalMessage({
              message: '部分搜索条件无效，请检查并重新输入。',
              type: 'warning',
              autoClose: true,
              duration: 3000,
            });
          } else {
            useUiStore.getState().clearGlobalMessage(); // 没有错误时清除提示
          }
        }
      );
    }
  ```

#### `handlePageChange(pageNum, pageSize)`
- 用途: 处理 `OrderPagination` 组件触发的分页事件，更新分页信息并重新发起数据请求。
- 输入参数:
    - `pageNum`: `Number`, 当前页码。
    - `pageSize`: `Number`, 每页显示数量。
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/pages/auditOrder/index.js (续)
    handlePageChange(pageNum, pageSize) {
      this.setState(
        (prevState) => ({
          pagination: {
            pageNum: pageNum,
            pageSize: pageSize,
          },
        }),
        () => {
          this.fetchOrders(this.state.searchParams, this.state.pagination);
        }
      );
    }
  ```

#### `handleOrderTypeFilterChange(types)`
- 用途: 处理 `OrderTable` 传递的工单类型筛选事件，更新 `searchParams` 中的 `orderTypes` 并重新发起数据请求。
- 输入参数:
    - `types`: `Array<string>`, 选中的工单类型ID数组。
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/pages/auditOrder/index.js (续)
    handleOrderTypeFilterChange(types) {
      this.setState(
        (prevState) => ({
          searchParams: {
            ...prevState.searchParams,
            orderTypes: types, // 更新工单类型筛选数组
          },
          pagination: {
            ...prevState.pagination,
            pageNum: 1, // 筛选时重置页码到第一页
          },
        }),
        () => {
          // 状态更新完成后，发起数据请求
          this.fetchOrders(this.state.searchParams, this.state.pagination);
        }
      );
    }
  ```

#### `render()`
- 用途: 渲染页面 UI，包括搜索表单、工单列表（带骨架屏）、分页器和全局非模态提示。
- 输入参数: 无
- 输出: React 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/pages/auditOrder/index.js (续)
    render() {
      const { orderList, totalOrders, loading, pagination, searchParams, hasInvalidSearchConditions } = this.state;
      const globalMessageState = useUiStore.getState().globalMessage; // 从 Zustand store 获取全局提示状态

      return (
        <AuditOrderContainer>
          {/* 迭代三：全局非模态提示 */}
          {globalMessageState && (
            <GlobalMessage
              message={globalMessageState.message}
              type={globalMessageState.type}
              autoClose={globalMessageState.autoClose}
              duration={globalMessageState.duration}
            />
          )}

          {/* 搜索表单 */}
          <SearchForm
            onSearch={this.handleSearch}
            initialSearchParams={searchParams}
            // 迭代三：传递是否有日期校验错误，用于SearchForm内部控制局部反馈
            hasValidationErrorInParent={hasInvalidSearchConditions}
          />

          {/* 工单列表区域，根据加载状态显示骨架屏或表格 */}
          {loading ? (
            <LoadingSkeleton />
          ) : (
            <OrderTable
              dataSource={orderList}
              // 迭代三：传递工单类型筛选处理函数和当前选中的工单类型
              onOrderTypeFilterChange={this.handleOrderTypeFilterChange}
              selectedOrderTypes={searchParams.orderTypes}
            />
          )}

          {/* 分页组件 */}
          <OrderPagination
            total={totalOrders}
            current={pagination.pageNum}
            pageSize={pagination.pageSize}
            onChange={this.handlePageChange}
          />
        </AuditOrderContainer>
      );
    }
  }

  export default AuditOrderPage;
  ```

### `src/pages/auditOrder/styles.js`

a. **文件用途说明**
   该文件定义 `AuditOrderPage` 组件的页面级 `styled-components` 样式。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       StyledComponent --|> React.Component
       StyledComponent : +AuditOrderContainer
   ```

c. **函数/方法详解**

#### `AuditOrderContainer` (styled component)
- 用途: 定义页面整体的容器样式。
- 输入参数: 无
- 输出: Styled-component Div 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/pages/auditOrder/styles.js
  import styled from 'styled-components';

  export const AuditOrderContainer = styled.div`
    padding: 20px;
    background-color: #f0f2f5;
    min-height: calc(100vh - 64px);
    position: relative; /* 确保子组件（如GlobalMessage）可以正确定位 */
  `;
  ```

---

### `src/components/SearchForm/index.js`

a. **文件用途说明**
   该文件定义 `SearchForm` 组件。在迭代三中，它将增加对局部反馈（输入框边框变色和小字提示）的实现，并根据父组件传入的校验状态决定是否显示局部反馈。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       SearchForm --|> React.Component
       SearchForm : +state
       SearchForm : +componentDidUpdate(prevProps)
       SearchForm : +handleInputChange(e)
       SearchForm : +handleDateRangeChange(dates)
       SearchForm : +handleSearchClick()
       SearchForm : +render()
   ```

c. **函数/方法详解**

#### `SearchForm` 组件
- 用途: 搜索表单组件，提供单号、标题和申请日期时间范围的输入。
- 输入参数:
    - `onSearch`: `Function`, 父组件传入的搜索回调函数。
    - `initialSearchParams`: `Object`, 包含父组件的初始搜索条件。
    - `hasValidationErrorInParent`: `Boolean`, (迭代三) 父组件告知是否存在日期校验错误，用于控制局部反馈的显示。
- 输出: React 元素 (搜索表单 UI)

#### `constructor(props)`
- 用途: 初始化组件状态。
- 输入参数: `props`
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/SearchForm/index.js
  import React, { Component } from 'react';
  import { Input, Button, Form, DatePicker } from 'antd';
  import { SearchFormContainer, StyledFormItem, DateRangeErrorTip } from './styles';
  import { trimAndHandleEmpty } from '../../utils/string';
  import dayjs from 'dayjs';

  const { RangePicker } = DatePicker;

  class SearchForm extends Component {
    constructor(props) {
      super(props);
      this.state = {
        orderId: props.initialSearchParams?.orderId || '',
        title: props.initialSearchParams?.title || '',
        startDate: props.initialSearchParams?.appliedStartDate ? dayjs(props.initialSearchParams.appliedStartDate) : null,
        endDate: props.initialSearchParams?.appliedEndDate ? dayjs(props.initialSearchParams.appliedEndDate) : null,
        dateRangeError: '', // 日期范围校验错误信息
        // 迭代三：局部反馈状态，用于控制边框变色
        showOrderIdErrorFeedback: false,
        showTitleErrorFeedback: false,
        showDateRangeErrorFeedback: false,
      };
      this.handleInputChange = this.handleInputChange.bind(this);
      this.handleDateRangeChange = this.handleDateRangeChange.bind(this);
      this.handleSearchClick = this.handleSearchClick.bind(this);
    }
  ```

#### `componentDidUpdate(prevProps)`
- 用途: 监听 `initialSearchParams` 变化，更新组件内部的输入框值。在迭代三中，还会监听 `hasValidationErrorInParent` 属性，用于重置局部反馈状态。
- 输入参数: `prevProps`
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/SearchForm/index.js (续)
    componentDidUpdate(prevProps) {
      const { initialSearchParams, hasValidationErrorInParent } = this.props;

      // 同步父组件传来的搜索参数
      if (initialSearchParams.orderId !== prevProps.initialSearchParams.orderId ||
          initialSearchParams.title !== prevProps.initialSearchParams.title ||
          initialSearchParams.appliedStartDate !== prevProps.initialSearchParams.appliedStartDate ||
          initialSearchParams.appliedEndDate !== prevProps.initialSearchParams.appliedEndDate) {
        this.setState({
          orderId: initialSearchParams.orderId || '',
          title: initialSearchParams.title || '',
          startDate: initialSearchParams.appliedStartDate ? dayjs(initialSearchParams.appliedStartDate) : null,
          endDate: initialSearchParams.appliedEndDate ? dayjs(initialSearchParams.appliedEndDate) : null,
          dateRangeError: '', // props更新时清空错误
          // 重置局部反馈状态
          showOrderIdErrorFeedback: false,
          showTitleErrorFeedback: false,
          showDateRangeErrorFeedback: false,
        });
      }

      // 迭代三：当父组件的错误状态变化时，清除局部反馈（例如，全局提示消失后）
      if (hasValidationErrorInParent !== prevProps.hasValidationErrorInParent && !hasValidationErrorInParent) {
        this.setState({
          showOrderIdErrorFeedback: false,
          showTitleErrorFeedback: false,
          showDateRangeErrorFeedback: false,
        });
      }
    }
  ```

#### `handleInputChange(e)`
- 用途: 处理输入框内容的改变，更新组件内部状态。
- 输入参数: `e` (事件对象)
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/SearchForm/index.js (续)
    handleInputChange(e) {
      const { name, value } = e.target;
      this.setState({ [name]: value, [`show${name.charAt(0).toUpperCase() + name.slice(1)}ErrorFeedback`]: false }); // 清除对应局部错误反馈
    }
  ```

#### `handleDateRangeChange(dates)`
- 用途: 处理日期范围选择器内容的改变，更新组件内部状态并进行实时校验。
- 输入参数:
    - `dates`: `Array<dayjs | null>`, 包含开始日期和结束日期的 dayjs 对象数组或 null。
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/SearchForm/index.js (续)
    handleDateRangeChange(dates) {
      const [newStartDate, newEndDate] = dates || [null, null];
      let error = '';
      let showFeedback = false;

      if (newStartDate && newEndDate && newEndDate.isBefore(newStartDate)) {
        error = '结束日期不能早于开始日期';
        // 不自动置空，让用户看到错误，但点击搜索时仍传递有效值（或空值）
        // 迭代三：当校验失败时，触发局部反馈
        showFeedback = true;
      }

      this.setState({
        startDate: newStartDate,
        endDate: newEndDate,
        dateRangeError: error,
        showDateRangeErrorFeedback: showFeedback, // 控制局部反馈
      });
    }
  ```

#### `handleSearchClick()`
- 用途: 处理“搜索”按钮点击事件，对输入值进行预处理（空值/空格/零值），然后调用父组件的 `onSearch` 回调，并告知父组件是否存在校验错误。
- 输入参数: 无
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/SearchForm/index.js (续)
    handleSearchClick() {
      const { orderId, title, startDate, endDate, dateRangeError } = this.state;

      const processedOrderId = trimAndHandleEmpty(orderId);
      const processedTitle = trimAndHandleEmpty(title);

      // 检查日期范围是否有效
      let currentHasValidationErrors = !!dateRangeError;

      // 迭代三：如果其他文本框有“无效”输入（例如，未来如果定义了其他校验规则）
      // 这里简化，只考虑日期范围的校验错误
      // if (processedOrderId === 'invalid_pattern') { /* ... */ }

      // 触发局部反馈的逻辑在点击搜索时执行
      this.setState({
        showDateRangeErrorFeedback: currentHasValidationErrors,
        // 其他文本框的局部反馈，如果需要
        // showOrderIdErrorFeedback: processedOrderId === '' && orderId.length > 0, // 仅空格时
        // showTitleErrorFeedback: processedTitle === '' && title.length > 0,
      });


      this.props.onSearch({
        orderId: processedOrderId,
        title: processedTitle,
        appliedStartDate: startDate ? startDate.toDate() : null,
        appliedEndDate: endDate ? endDate.toDate() : null,
      }, currentHasValidationErrors); // 将校验错误状态传递给父组件
    }
  ```

#### `render()`
- 用途: 渲染搜索表单的 UI 结构，包括日期选择器和视觉区分。在迭代三中，将根据局部反馈状态添加样式。
- 输入参数: 无
- 输出: React 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/SearchForm/index.js (续)
    render() {
      const { orderId, title, startDate, endDate, dateRangeError,
              showOrderIdErrorFeedback, showTitleErrorFeedback, showDateRangeErrorFeedback } = this.state;
      return (
        <SearchFormContainer>
          <Form layout="inline">
            <StyledFormItem
              label="单号"
              className={orderId ? 'has-value' : 'no-value'}
              // 迭代三：局部反馈，控制错误状态
              validateStatus={showOrderIdErrorFeedback ? 'error' : ''}
              help={showOrderIdErrorFeedback ? '请输入有效单号' : ''} // 示例提示
            >
              <Input
                name="orderId"
                placeholder="请输入单号"
                value={orderId}
                onChange={this.handleInputChange}
                allowClear
              />
            </StyledFormItem>
            <StyledFormItem
              label="标题"
              className={title ? 'has-value' : 'no-value'}
              // 迭代三：局部反馈，控制错误状态
              validateStatus={showTitleErrorFeedback ? 'error' : ''}
              help={showTitleErrorFeedback ? '请输入有效标题' : ''} // 示例提示
            >
              <Input
                name="title"
                placeholder="请输入标题"
                value={title}
                onChange={this.handleInputChange}
                allowClear
              />
            </StyledFormItem>

            {/* 迭代二：申请日期时间范围搜索 */}
            <StyledFormItem
              label="申请日期时间范围"
              className={(startDate || endDate) ? 'has-value' : 'no-value'}
              // 迭代三：局部反馈，当有日期校验错误时
              validateStatus={showDateRangeErrorFeedback ? 'error' : ''}
              help={dateRangeError} // 直接使用dateRangeError作为提示
            >
              <RangePicker
                showTime
                value={[startDate, endDate]}
                onChange={this.handleDateRangeChange}
                // Ant Design input状态，直接由 validateStatus 控制
              />
              {/* 不再单独显示 DateRangeErrorTip，由 Form.Item 的 help 属性控制 */}
            </StyledFormItem>

            <Form.Item>
              <Button type="primary" onClick={this.handleSearchClick}>
                搜索
              </Button>
            </Form.Item>
          </Form>
        </SearchFormContainer>
      );
    }
  }

  export default SearchForm;
  ```

### `src/components/SearchForm/styles.js`

a. **文件用途说明**
   该文件定义 `SearchForm` 组件的 `styled-components` 样式。在迭代三中，将增加 `Form.Item` 级别的局部反馈样式。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       StyledComponent --|> React.Component
       StyledComponent : +SearchFormContainer
       StyledComponent : +StyledFormItem
       StyledComponent : +DateRangeErrorTip
   ```

c. **函数/方法详解**

#### `SearchFormContainer` (styled component)
- 用途: 搜索表单的容器样式。
- 输入参数: 无
- 输出: Styled-component Div 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/SearchForm/styles.js
  import styled from 'styled-components';
  import { Form } from 'antd';

  export const SearchFormContainer = styled.div`
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 20px;

    .ant-form-inline .ant-form-item {
      margin-bottom: 16px;
    }
  `;
  ```

#### `StyledFormItem` (styled component)
- 用途: 用于自定义 `Form.Item` 的样式，实现已填写/未填写状态的视觉区分，并为局部反馈预留样式。
- 输入参数: 无
- 输出: Styled-component Form.Item 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/SearchForm/styles.js (续)
  export const StyledFormItem = styled(Form.Item)`
    &.has-value .ant-input-affix-wrapper,
    &.has-value .ant-picker {
      border-color: #1890ff;
    }
    &.no-value .ant-input-affix-wrapper,
    &.no-value .ant-picker {
      border-color: #d9d9d9;
    }

    // 迭代三：局部反馈，错误边框色和提示文字样式
    &.ant-form-item-has-error .ant-input-affix-wrapper,
    &.ant-form-item-has-error .ant-picker {
      border-color: #ffad86; /* 柔和提示色 */
      box-shadow: 0 0 0 2px rgba(255, 173, 134, 0.2); /* 柔和阴影 */
    }
    .ant-form-item-explain {
      font-size: 12px;
      color: #ffad86; /* 柔和提示色 */
      position: absolute;
      width: 150%; /* 允许提示文字更长 */
      left: 0;
      top: 100%; /* 定位在输入框下方 */
    }
  `;
  ```

#### `DateRangeErrorTip` (styled component)
- 用途: 仅作为日期范围校验错误提示的小字样式，在迭代三中，其内容将被 `StyledFormItem` 的 `help` 属性替代，但样式定义仍保留。
- 输入参数: 无
- 输出: Styled-component Div 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/SearchForm/styles.js (续)
  export const DateRangeErrorTip = styled.div`
    color: #ff4d4f; /* 仍然定义，但实际显示由 Ant Design 的 Form.Item help 属性控制其样式 */
    font-size: 12px;
    margin-top: 4px;
    /* position: absolute; */ /* 移除，由Form.Item的定位统一管理 */
  `;
  ```

---

### `src/components/OrderTable/index.js`

a. **文件用途说明**
   该文件定义 `OrderTable` 组件。在迭代三中，它将实现“工单类型”列的筛选功能，通过 Ant Design Table 的 `filterDropdown` 属性集成 `OrderTypeFilter` 组件，并处理筛选逻辑。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       OrderTable --|> React.Component
       OrderTable "1" --o "1" OrderTypeFilter : integrates
       OrderTable "1" --o "1" useDebounce : uses
       OrderTable : +constructor(props)
       OrderTable : +getColumns()
       OrderTable : +render()
   ```

c. **函数/方法详解**

#### `OrderTable` 组件
- 用途: 展示工单列表数据的表格组件，并提供工单类型筛选功能。
- 输入参数:
    - `dataSource`: `Array<Order>`, 要展示的工单数据数组。
    - `onOrderTypeFilterChange`: `Function`, (迭代三) 工单类型筛选改变时的回调函数，接收选中的类型数组。
    - `selectedOrderTypes`: `Array<string>`, (迭代三) 当前父组件中选中的工单类型数组，用于同步筛选弹窗状态。
- 输出: React 元素 (工单列表表格 UI)

#### `constructor(props)`
- 用途: 初始化组件。
- 输入参数: `props`
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/OrderTable/index.js
  import React, { Component } from 'react';
  import { Table, Tooltip, Button, Space } from 'antd';
  import { FilterOutlined } from '@ant-design/icons'; // 引入筛选图标
  import { AuditedTable } from './styles';
  import OrderTypeFilter from '../OrderTypeFilter'; // 引入工单类型筛选弹窗
  import useDebounce from '../../hooks/useDebounce'; // 引入防抖Hook (函数式组件用法，这里类组件内部模拟或由父组件传入)

  // 迭代三：硬编码的工单类型列表，实际应从API获取或配置文件读取
  const ALL_ORDER_TYPES = [
    { id: 'LEAVE', name: '请假工单' },
    { id: 'OVERTIME', name: '加班工单' },
    { id: 'TRAVEL', name: '出差工单' },
    { id: 'REIMBURSEMENT', name: '报销工单' },
    // ... 其他类型
  ];

  class OrderTable extends Component {
    // Note: useDebounce 是一个 Hook，不能直接在类组件中作为实例方法使用。
    // 在类组件中，我们通常需要自己实现防抖逻辑，或者通过高阶组件/render props 引入 Hook。
    // 为了简化，这里假定防抖逻辑由 OrderTypeFilter 内部或直接在调用 onOrderTypeFilterChange 前处理。
    // 更推荐的方式是在父组件 AuditOrderPage 中使用 useDebounce。
    // 为了贴合 LLD，这里直接在 renderProps 中使用，实际生产中会优化。

    constructor(props) {
      super(props);
      // 无需内部状态，所有筛选状态通过props传入
    }
  ```

#### `getColumns()`
- 用途: 定义 Ant Design `Table` 组件所需的列配置数组，实现“工单标题”列的特殊渲染逻辑，并为“工单类型”列增加筛选功能。
- 输入参数: 无
- 输出: `Array<Object>` (Ant Design Table Columns 配置)
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/OrderTable/index.js (续)
    getColumns() {
      const { onOrderTypeFilterChange, selectedOrderTypes } = this.props;

      return [
        {
          title: '单号',
          dataIndex: 'id',
          key: 'id',
          align: 'center',
          fixed: 'left',
          width: 150,
        },
        {
          title: '工单标题',
          dataIndex: 'title',
          key: 'title',
          align: 'center',
          width: 200,
          render: (text) => {
            const displayContent = text && text.trim() !== '' ? text : '';
            return (
              <Tooltip title={displayContent}>
                <div style={{
                  maxWidth: '100%',
                  overflow: 'hidden',
                  whiteWhiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                }}>
                  {displayContent}
                </div>
              </Tooltip>
            );
          },
        },
        {
          title: '工单类型',
          dataIndex: 'orderTypeName',
          key: 'orderTypeName',
          align: 'center',
          width: 120,
          // 迭代三：工单类型筛选功能实现
          filterIcon: (filtered) => (
            <FilterOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
          ),
          filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <OrderTypeFilter
              orderTypes={ALL_ORDER_TYPES}
              selectedTypes={selectedKeys} // Ant Design Table 传入的 selectedKeys 已经是数组
              // 这里的 onChange 会被 OrderTypeFilter 组件内部的防抖处理
              onSelect={(newSelected) => {
                // 更新 Ant Design 内部的 selectedKeys
                setSelectedKeys(newSelected);
                // 调用父组件的筛选回调，并传入防抖处理后的值
                onOrderTypeFilterChange(newSelected); // 此处直接触发父组件回调，防抖逻辑在OrderTypeFilter内部或父组件中
                confirm(); // 确认筛选，关闭弹窗
              }}
              onClear={() => {
                clearFilters(); // 清空 Ant Design 内部的 selectedKeys
                onOrderTypeFilterChange([]); // 清空父组件的筛选状态
                confirm(); // 确认并关闭
              }}
              // 传递 Ant Design 的 close 函数，让 OrderTypeFilter 可以手动关闭弹窗
              onClose={close}
            />
          ),
          // Ant Design Table 自身的 filter 逻辑，我们这里通过后端筛选，所以 onFilter 可以设为 null
          // 或者简单返回 true，表示前端不进行过滤
          onFilter: (value, record) => {
            // 如果后端处理筛选，这里可以简单返回 true
            return true; // 实际筛选由后端完成
          },
          // defaultFilteredValue: selectedOrderTypes, // 用于初始化筛选状态
          // 确保筛选图标状态与父组件同步
          filteredValue: selectedOrderTypes.length ? selectedOrderTypes : null, // Ant Design 的 filter 状态
        },
        {
          title: '创建人',
          dataIndex: 'creatorName',
          key: 'creatorName',
          align: 'center',
          width: 100,
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          align: 'center',
          width: 100,
        },
        {
          title: '申请日期',
          dataIndex: 'createdAt',
          key: 'createdAt',
          align: 'center',
          width: 180,
          render: (text) => {
            return text ? new Date(text).toLocaleString() : '';
          }
        },
        {
          title: '操作',
          key: 'actions',
          align: 'center',
          fixed: 'right',
          width: 100,
          render: (_, record) => (
            <Button type="link" onClick={() => console.log('查看详情:', record.id)}>
              详情
            </Button>
          ),
        },
      ];
    }
  ```

#### `render()`
- 用途: 渲染 Ant Design `Table` 组件。
- 输入参数: 无
- 输出: React 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/OrderTable/index.js (续)
    render() {
      const { dataSource } = this.props;
      return (
        <AuditedTable>
          <Table
            dataSource={dataSource}
            columns={this.getColumns()}
            rowKey="id"
            size="middle"
            pagination={false}
            scroll={{ x: 'max-content' }}
          />
        </AuditedTable>
      );
    }
  }

  export default OrderTable;
  ```

### `src/components/OrderTable/styles.js`

a. **文件用途说明**
   该文件定义 `OrderTable` 组件的 `styled-components` 样式。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       StyledComponent --|> React.Component
       StyledComponent : +AuditedTable
   ```

c. **函数/方法详解**

#### `AuditedTable` (styled component)
- 用途: 为表格容器提供外边距。
- 输入参数: 无
- 输出: Styled-component Div 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/OrderTable/styles.js
  import styled from 'styled-components';

  export const AuditedTable = styled.div`
    margin-top: 2%;
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
  `;
  ```

---

### `src/components/OrderPagination/index.js` (与迭代二相同)

a. **文件用途说明**
   该文件定义 `OrderPagination` 组件，负责分页功能。在迭代三中其逻辑不变。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       OrderPagination --|> React.Component
       OrderPagination : +handlePageChange(page, pageSize)
       OrderPagination : +render()
   ```

c. **函数/方法详解**

#### `OrderPagination` 组件
- 用途: 分页组件，提供页码切换和每页显示数量调整功能。
- 输入参数:
    - `total`: `Number`, 总数据条数。
    - `current`: `Number`, 当前页码。
    - `pageSize`: `Number`, 每页显示数量。
    - `onChange`: `Function`, 页码或每页数量改变时的回调函数，接收 `(page, pageSize)` 参数。
- 输出: React 元素 (分页 UI)

#### `constructor(props)`
- 用途: 初始化组件。
- 输入参数: `props`
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/OrderPagination/index.js
  import React, { Component } from 'react';
  import { Pagination } from 'antd';
  import { StyledPagination } from './styles';

  class OrderPagination extends Component {
    constructor(props) {
      super(props);
    }
  ```

#### `handlePageChange(page, pageSize)`
- 用途: 处理 Ant Design `Pagination` 组件的页码或每页数量改变事件，并向上级组件传递新的分页信息。
- 输入参数:
    - `page`: `Number`, 改变后的页码。
    - `pageSize`: `Number`, 改变后的每页显示数量。
- 输出: 无
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/OrderPagination/index.js (续)
    handlePageChange(page, pageSize) {
      if (this.props.onChange) {
        this.props.onChange(page, pageSize);
      }
    }
  ```

#### `render()`
- 用途: 渲染 Ant Design `Pagination` 组件。
- 输入参数: 无
- 输出: React 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/OrderPagination/index.js (续)
    render() {
      const { total, current, pageSize } = this.props;
      return (
        <StyledPagination
          size="small"
          showQuickJumper
          showSizeChanger
          total={total}
          current={current}
          pageSize={pageSize}
          onChange={this.handlePageChange}
          onShowSizeChange={this.handlePageChange}
          pageSizeOptions={['10', '20', '50', '100']}
        />
      );
    }
  }

  export default OrderPagination;
  ```

### `src/components/OrderPagination/styles.js` (与迭代二相同)

a. **文件用途说明**
   该文件定义 `OrderPagination` 组件的 `styled-components` 样式。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       StyledComponent --|> React.Component
       StyledComponent : +StyledPagination
   ```

c. **函数/方法详解**

#### `StyledPagination` (styled component)
- 用途: 为分页组件提供样式，使其靠右显示。
- 输入参数: 无
- 输出: Styled-component Pagination 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/OrderPagination/styles.js
  import styled from 'styled-components';

  export const StyledPagination = styled(Pagination)`
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
    padding: 0 20px;
  `;
  ```

---

### `src/components/common/LoadingSkeleton/index.js` (与迭代二相同)

a. **文件用途说明**
   该文件定义一个通用的骨架屏组件。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       LoadingSkeleton --|> React.Component
       LoadingSkeleton : +render()
   ```

c. **函数/方法详解**

#### `LoadingSkeleton` 组件
- 用途: 显示骨架屏加载提示。
- 输入参数: 无
- 输出: React 元素 (骨架屏 UI)

#### `render()`
- 用途: 渲染 Ant Design `Skeleton` 组件。
- 输入参数: 无
- 输出: React 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/common/LoadingSkeleton/index.js
  import React, { Component } from 'react';
  import { Skeleton } from 'antd';
  import { SkeletonContainer } from './styles';

  class LoadingSkeleton extends Component {
    render() {
      return (
        <SkeletonContainer>
          <Skeleton active paragraph={{ rows: 8 }} title={false} />
        </SkeletonContainer>
      );
    }
  }

  export default LoadingSkeleton;
  ```

### `src/components/common/LoadingSkeleton/styles.js` (与迭代二相同)

a. **文件用途说明**
   该文件定义 `LoadingSkeleton` 组件的 `styled-components` 样式。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       StyledComponent --|> React.Component
       StyledComponent : +SkeletonContainer
   ```

c. **函数/方法详解**

#### `SkeletonContainer` (styled component)
- 用途: 为骨架屏提供容器样式。
- 输入参数: 无
- 输出: Styled-component Div 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/common/LoadingSkeleton/styles.js
  import styled from 'styled-components';

  export const SkeletonContainer = styled.div`
    margin-top: 2%;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  `;
  ```

---

### `src/api/order.js`

a. **文件用途说明**
   该文件封装了与工单相关的后端 API 请求。在迭代三中，`requestMyAuditOrder` 将新增对 `orderTypes` 数组参数的传递。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       Axios --|> RequestHandler : uses
       RequestHandler : +requestMyAuditOrder(params)
       RequestHandler : +requestOrderDetail(orderId)
   ```

c. **函数/方法详解**

#### `requestMyAuditOrder(params)`
- 用途: 向后端请求当前用户可审批的工单列表。
- 输入参数:
    - `params`: `Object`，包含搜索、筛选和分页条件的键值对。
- 输出: `Promise<GetOrdersResponse>`
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/api/order.js
  import axios from 'axios';

  const BASE_URL = '/api/v1';

  export async function requestMyAuditOrder(params) {
    try {
      const response = await axios.get(`${BASE_URL}/audits/orders`, {
        params: {
          pageNum: params.pageNum,
          pageSize: params.pageSize,
          orderId: params.orderId,
          title: params.title,
          appliedStartDate: params.appliedStartDate,
          appliedEndDate: params.appliedEndDate,
          // 迭代三：传递工单类型筛选参数
          orderTypes: params.orderTypes, // 数组会作为多个同名参数或逗号分隔字符串 (取决于后端实现)
        },
        paramsSerializer: params => {
          // 自定义参数序列化，处理数组参数，例如转换为逗号分隔字符串
          // 或者让 axios 默认处理，如果后端支持 repeated query parameters
          // 这里假设后端能处理数组作为查询参数 (例如 ?orderTypes=LEAVE&orderTypes=OVERTIME)
          const searchParams = new URLSearchParams();
          for (const key in params) {
            const value = params[key];
            if (Array.isArray(value)) {
              value.forEach(item => searchParams.append(key, item));
            } else if (value !== null && value !== undefined && value !== '') { // 过滤空值
              searchParams.append(key, value);
            }
          }
          return searchParams.toString();
        }
      });
      return response.data;
    } catch (error) {
      console.error('API request for my audit orders failed:', error);
      return {
        code: error.response?.status || -1,
        message: error.response?.data?.message || '网络或服务器错误',
        data: {
          total: 0,
          list: [],
        },
      };
    }
  }

  // 迭代一/二占位：
  export async function requestOrderDetail(orderId) {
      console.log(`Fetching detail for order: ${orderId}`);
      // 模拟一个固定返回值的空实现
      return new Promise(resolve => {
          setTimeout(() => {
              resolve({
                  code: 0,
                  message: "Success",
                  data: {
                      id: orderId,
                      title: "模拟工单详情标题",
                      description: "这是一个模拟的工单详情描述，等待实际数据接入。",
                      orderType: "LEAVE",
                      orderTypeName: "请假工单",
                      creatorId: "user999",
                      creatorName: "模拟创建人",
                      auditorId: "user001",
                      auditorName: "模拟审批人",
                      status: "PENDING",
                      createdAt: "2024-01-01T08:00:00Z",
                      updatedAt: "2024-01-01T08:05:00Z",
                      attachments: ["http://example.com/mock_attachment.pdf"]
                  }
              });
          }, 500); // 模拟网络延迟
      });
  }
  ```

---

### `src/utils/string.js` (与迭代二相同)

a. **文件用途说明**
   该文件包含字符串处理的工具函数。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       StringUtil : +trimAndHandleEmpty(str)
   ```

c. **函数/方法详解**

#### `trimAndHandleEmpty(str)`
- 用途: 对字符串进行去空格处理，如果去除空格后为空字符串，则返回空字符串 `""`。
- 输入参数: `str`: `String`, 需要处理的字符串。
- 输出: `String`, 处理后的字符串。
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/utils/string.js
  export function trimAndHandleEmpty(str) {
    if (typeof str !== 'string' || str === null || str === undefined) {
      return '';
    }
    const trimmedStr = str.trim();
    return trimmedStr === '' ? '' : trimmedStr;
  }
  ```

---

### `src/utils/date.js` (与迭代二相同)

a. **文件用途说明**
   该文件包含日期处理的工具函数。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       DateUtil : +toISOStringOrNull(dateInput)
   ```

c. **函数/方法详解**

#### `toISOStringOrNull(dateInput)`
- 用途: 将 `Date` 对象或类似日期值转换为 ISO 8601 格式的字符串。如果输入无效或为 `null`/`undefined`, 则返回 `null`。
- 输入参数: `dateInput`: `Date | number | string | null | undefined`, 需要转换的日期输入。
- 输出: `string | null`, ISO 8601 格式的字符串（例如 "2024-07-07T10:30:00.000Z"）或 `null`。
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/utils/date.js
  export function toISOStringOrNull(dateInput) {
    if (!dateInput) {
      return null;
    }
    try {
      const date = new Date(dateInput);
      if (isNaN(date.getTime())) {
        return null;
      }
      return date.toISOString();
    } catch (e) {
      console.error('Error converting date to ISO string:', dateInput, e);
      return null;
    }
  }
  ```

---

### `src/components/OrderDetailDrawer/index.js` (与迭代二相同，仅占位)

a. **文件用途说明**
   该文件定义工单详情抽屉组件，迭代三中仍然是占位。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       OrderDetailDrawer --|> React.Component
       OrderDetailDrawer : +render()
   ```

c. **函数/方法详解**

#### `OrderDetailDrawer` 组件
- 用途: 工单详情抽屉组件，仅作基础渲染。
- 输入参数: `visible`, `onClose`, `orderId`。
- 输出: React 元素 (抽屉 UI)
- 伪代码: (内容与迭代二相同)
  ```javascript
  // src/components/OrderDetailDrawer/index.js
  import React from 'react';
  import { Drawer, Button } from 'antd';
  import { requestOrderDetail } from '../../api/order'; // 假设这里会导入详情API

  const OrderDetailDrawer = ({ visible, onClose, orderId }) => {
    // 迭代三：仍然是占位，未来在这里会根据 orderId 请求详情并渲染
    // useEffect(() => {
    //   if (visible && orderId) {
    //     requestOrderDetail(orderId).then(data => {
    //       // 设置详情数据
    //     });
    //   }
    // }, [visible, orderId]);

    return (
      <Drawer
        title={`工单详情: ${orderId || 'N/A'}`}
        placement="right"
        closable={true}
        onClose={onClose}
        open={visible}
        width={500}
      >
        <p>这里是工单详情内容 (迭代三占位)</p>
        <p>工单ID: {orderId}</p>
        <Button onClick={onClose}>关闭</Button>
      </Drawer>
    );
  };

  export default OrderDetailDrawer;
  ```

---

### `src/components/OrderTypeFilter/index.js` (迭代三实现)

a. **文件用途说明**
   该文件定义工单类型筛选弹窗组件。在迭代三中，它将实现勾选/取消勾选、全选/全不选功能，并使用 `useDebounce` Hook 来优化筛选操作的请求频率。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       OrderTypeFilter --|> React.FC
       OrderTypeFilter ..> useDebounce : uses
       OrderTypeFilter : +useState(selected)
       OrderTypeFilter : +useEffect(debounce)
       OrderTypeFilter : +handleCheckboxChange(id, checked)
       OrderTypeFilter : +handleSelectAll(checked)
       OrderTypeFilter : +render()
   ```

c. **函数/方法详解**

#### `OrderTypeFilter` 组件
- 用途: 工单类型筛选弹窗组件，提供多选功能，并对选择操作进行防抖。
- 输入参数:
    - `orderTypes`: `Array<{id: string, name: string}>`, 所有可用的工单类型列表。
    - `selectedTypes`: `Array<string>`, 当前外部已选中的工单类型ID数组，用于初始化。
    - `onSelect`: `Function`, 选中类型改变时的回调函数，接收 `Array<string>` (debounced)。
    - `onClear`: `Function`, 清空筛选时的回调函数。
    - `onClose`: `Function`, (Ant Design Table 传入的) 关闭筛选弹窗的回调。
- 输出: React 元素 (筛选弹窗 UI)

#### `OrderTypeFilter` (函数式组件实现)
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/OrderTypeFilter/index.js
  import React, { useState, useEffect } from 'react';
  import { Checkbox, Button, Space } from 'antd';
  import { FilterDropdownContainer } from './styles';
  import useDebounce from '../../hooks/useDebounce'; // 引入防抖 Hook

  const OrderTypeFilter = ({ orderTypes = [], selectedTypes = [], onSelect, onClear, onClose }) => {
    // 内部状态，用于管理用户在弹窗中的临时选择
    const [internalSelected, setInternalSelected] = useState(selectedTypes);
    // 使用防抖 Hook 包装 onSelect 回调
    const debouncedOnSelect = useDebounce(onSelect, 300); // 300ms 防抖

    // 当外部 selectedTypes 变化时，同步到内部状态（例如，父组件重置筛选时）
    useEffect(() => {
      setInternalSelected(selectedTypes);
    }, [selectedTypes]);

    const handleCheckboxChange = (id, checked) => {
      const newSelected = checked
        ? [...internalSelected, id]
        : internalSelected.filter(typeId => typeId !== id);
      setInternalSelected(newSelected);
      // 触发防抖后的回调
      debouncedOnSelect(newSelected);
    };

    const handleSelectAll = (checked) => {
      const allTypeIds = orderTypes.map(type => type.id);
      const newSelected = checked ? allTypeIds : [];
      setInternalSelected(newSelected);
      // 触发防抖后的回调
      debouncedOnSelect(newSelected);
    };

    const handleClearFilter = () => {
      setInternalSelected([]);
      onClear(); // 立即触发清空，因为清空是明确意图，不需防抖
      onClose(); // 关闭弹窗
    };

    return (
      <FilterDropdownContainer>
        <div style={{ padding: '8px 0' }}>
          {orderTypes.map(type => (
            <Checkbox
              key={type.id}
              checked={internalSelected.includes(type.id)}
              onChange={(e) => handleCheckboxChange(type.id, e.target.checked)}
              style={{ display: 'block', padding: '4px 8px' }}
            >
              {type.name}
            </Checkbox>
          ))}
        </div>
        <Space style={{ padding: '8px', borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between' }}>
          <Button size="small" type="link" onClick={() => handleSelectAll(true)}>全选</Button>
          <Button size="small" type="link" onClick={() => handleSelectAll(false)}>全不选</Button>
          <Button size="small" type="link" onClick={handleClearFilter}>清除</Button> {/* 新增清除按钮 */}
          {/* <Button size="small" type="primary" onClick={onClose}>确定</Button> /* 确定按钮不再必要，因为是实时筛选 */}
        </Space>
      </FilterDropdownContainer>
    );
  };

  export default OrderTypeFilter;
  ```

### `src/components/OrderTypeFilter/styles.js` (迭代三实现)

a. **文件用途说明**
   该文件定义 `OrderTypeFilter` 组件的 `styled-components` 样式。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       StyledComponent --|> React.Component
       StyledComponent : +FilterDropdownContainer
   ```

c. **函数/方法详解**

#### `FilterDropdownContainer` (styled component)
- 用途: 为筛选弹窗提供样式。
- 输入参数: 无
- 输出: Styled-component Div 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/OrderTypeFilter/styles.js
  import styled from 'styled-components';

  export const FilterDropdownContainer = styled.div`
    padding: 5px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    min-width: 150px; /* 确保弹窗有最小宽度 */
  `;
  ```

---

### `src/components/common/GlobalMessage/index.js` (迭代三实现)

a. **文件用途说明**
   该文件定义全局非模态提示组件。在迭代三中，它将实现根据 `message` 和 `type` 显示不同内容的提示，支持自动消失和手动关闭。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       GlobalMessage --|> React.FC
       GlobalMessage : +useState(visible)
       GlobalMessage : +useEffect(autoClose)
       GlobalMessage : +render()
   ```

c. **函数/方法详解**

#### `GlobalMessage` 组件
- 用途: 显示页面顶部的非模态提示信息。
- 输入参数:
    - `message`: `String`, 要显示的提示内容。
    - `type`: `String`, 提示类型（'success', 'info', 'warning', 'error'）。
    - `autoClose`: `Boolean`, 是否自动关闭。
    - `duration`: `Number`, 自动关闭的延迟时间（毫秒）。
- 输出: React 元素 (提示 UI)

#### `GlobalMessage` (函数式组件实现)
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/common/GlobalMessage/index.js
  import React, { useEffect, useState } from 'react';
  import { Alert, Space, Button } from 'antd'; // 引入 Button 用于关闭
  import { CloseOutlined } from '@ant-design/icons';
  import { GlobalMessageContainer } from './styles';

  const GlobalMessage = ({ message, type = 'info', autoClose = true, duration = 3000 }) => {
    const [visible, setVisible] = useState(true);

    // 监听 message 变化，当 message 改变时，重新显示提示
    useEffect(() => {
      setVisible(!!message); // 有 message 就显示
      if (message && autoClose) {
        const timer = setTimeout(() => {
          setVisible(false);
        }, duration);
        return () => clearTimeout(timer);
      }
    }, [message, autoClose, duration]); // 依赖 message

    if (!visible || !message) {
      return null;
    }

    return (
      <GlobalMessageContainer>
        <Alert
          message={message}
          type={type}
          showIcon
          action={
            <Space>
              <Button size="small" type="text" icon={<CloseOutlined />} onClick={() => setVisible(false)} />
            </Space>
          }
          closable // 允许点击右上角关闭
          onClose={() => setVisible(false)}
        />
      </GlobalMessageContainer>
    );
  };

  export default GlobalMessage;
  ```

### `src/components/common/GlobalMessage/styles.js` (迭代三实现)

a. **文件用途说明**
   该文件定义 `GlobalMessage` 组件的 `styled-components` 样式。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       StyledComponent --|> React.Component
       StyledComponent : +GlobalMessageContainer
   ```

c. **函数/方法详解**

#### `GlobalMessageContainer` (styled component)
- 用途: 为全局提示消息提供固定定位样式，使其始终显示在页面顶部。
- 输入参数: 无
- 输出: Styled-component Div 元素
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/components/common/GlobalMessage/styles.js
  import styled from 'styled-components';

  export const GlobalMessageContainer = styled.div`
    position: sticky; /* 或者 fixed */
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000; /* 确保在最上层 */
    padding: 0 20px; /* 与页面内容对齐 */
    box-sizing: border-box;
    /* transition: opacity 0.3s ease-in-out; /* 可选：淡入淡出动画 */

    .ant-alert {
      margin-bottom: 10px;
      /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 可选：添加阴影 */
    }
  `;
  ```

---

### `src/hooks/useDebounce.js` (迭代三实现)

a. **文件用途说明**
   该文件定义自定义防抖 Hook，用于延迟执行函数或更新值。在迭代三中，它将用于优化工单类型筛选的请求频率。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       useDebounce --|> React.Hook
       useDebounce : +useState(debouncedValue)
       useDebounce : +useEffect(timer)
   ```

c. **函数/方法详解**

#### `useDebounce(value, delay)` Hook
- 用途: 创建一个防抖的值。当 `value` 改变时，在 `delay` 毫秒后更新 `debouncedValue`。
- 输入参数:
    - `value`: `any`, 需要防抖的值。
    - `delay`: `Number`, 延迟时间（毫秒）。
- 输出: `any`, 防抖后的值。
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/hooks/useDebounce.js
  import { useState, useEffect } from 'react';

  function useDebounce(value, delay) {
    // State to store debounced value
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
      // Set a timeout to update debounced value after the specified delay
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);

      // Cleanup function to clear the timeout if value changes or component unmounts
      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]); // Only re-run if value or delay changes

    return debouncedValue;
  }

  export default useDebounce;
  ```

---

### `src/stores/uiStore.js` (迭代三实现)

a. **文件用途说明**
   该文件定义 Zustand 状态管理 store，用于管理全局 UI 状态，特别是在迭代三中用于全局非模态提示的显示与隐藏。

b. **文件内类图**
   ```mermaid
   classDiagram
       direction LR
       ZustandStore --|> Object
       ZustandStore : +globalMessage
       ZustandStore : +setGlobalMessage(message)
       ZustandStore : +clearGlobalMessage()
   ```

c. **函数/方法详解**

#### `useUiStore` (Zustand store)
- 用途: 提供全局 UI 状态的访问和修改方法。
- 状态:
    - `globalMessage`: `Object | null`, 存储当前全局提示的信息（`{ message: string, type: string, autoClose: boolean, duration: number }`）。
- 方法:
    - `setGlobalMessage(message)`: 设置全局提示信息。
    - `clearGlobalMessage()`: 清除全局提示信息。
- 伪代码和注释说明实现步骤和要点:
  ```javascript
  // src/stores/uiStore.js
  import { create } from 'zustand';

  const useUiStore = create((set) => ({
    // 全局提示状态，默认为空
    globalMessage: null,

    /**
     * 设置全局提示消息。
     * @param {object} messageData - 包含 message (string), type (string), autoClose (boolean), duration (number) 的对象。
     */
    setGlobalMessage: (messageData) => set({ globalMessage: messageData }),

    /**
     * 清除全局提示消息。
     */
    clearGlobalMessage: () => set({ globalMessage: null }),
  }));

  export default useUiStore;
  ```

---

## 迭代演进依据

这份详细设计继续遵循了**简约至上 (KISS Principle)** 和**拒绝过度设计 (YAGNI Principle)** 的原则，同时为未来的迭代提供了坚实的基础：

1.  **细粒度模块化与职责划分：** 迭代三将 `OrderTypeFilter` 和 `GlobalMessage` 作为独立组件实现，各自拥有明确的职责。`useDebounce` 作为独立的 Hook，也体现了可复用性。这种细致的模块化保证了每个功能点的高内聚，低耦合，未来即使需求变化，也只需修改局部，而不会影响整个系统。
    * 例如，如果需要更换工单类型筛选的 UI 库，只需修改 `OrderTypeFilter` 内部，对 `OrderTable` 和 `AuditOrderPage` 的影响很小。
2.  **状态管理分层与统一：** `uiStore` 的引入，清晰地将全局 UI 状态（如非模态提示）从页面组件中剥离出来，实现了**状态的统一管理**。这避免了组件间复杂的 prop drilling，使得全局提示的触发和控制变得简洁明了，易于扩展更多全局 UI 状态。
3.  **渐进式功能增强：** 工单类型筛选通过 `Table` 组件的 `filterDropdown` 扩展点实现，而不是侵入性地修改 `Table` 核心代码。日期范围校验的局部反馈也在现有 `SearchForm` 结构上进行增强，充分利用了 Ant Design 的 `Form.Item` 属性。这种**渐进式增强**的策略，最小化了每次迭代对现有代码的影响。
4.  **性能优化前置：** 在需求阶段就明确了对工单类型筛选操作使用**防抖机制**，并通过 `useDebounce` Hook 的引入实现了这一优化。这体现了**务实**的设计，避免了高频操作可能带来的性能问题，保证了用户体验。
5.  **数据流的稳定性与可扩展性：** 无论是日期范围搜索还是工单类型筛选，最终都汇聚到 `AuditOrderPage`，统一通过 `fetchOrders` 调用后端 `requestMyAuditOrder` API。`requestMyAuditOrder` API 能够接收多样的搜索和筛选参数，并且设计时就考虑了 ProtoBuf 零值的兼容性，保证了前后端数据接口的**稳定性和可扩展性**。未来新增任何搜索或筛选条件，都可以在这个统一的数据流框架下进行。
6.  **用户体验的持续优化：** 迭代三聚焦于通过非模态提示和局部反馈提升用户交互体验。这些功能的加入是用户反馈的直接体现，并被集成到现有组件中，展示了系统**易于迭代演进**以满足不断增长的用户体验需求的能力。