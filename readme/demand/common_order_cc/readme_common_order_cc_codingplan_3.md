# 编码计划迭代三
---

### **代码目录结构**

本次迭代不新增文件，所有工作都在现有文件上进行改造，激活迭代二中预留的逻辑。

```
wfo-frontend/
└── src/
    ├── components/         # (无改动)
    ├── pages/
    │   ├── auditOrder/
    │   │   └── index.js        # [改造] 激活筛选逻辑
    │   ├── doingOrder/
    │   │   └── index.js        # [改造] 激活筛选逻辑
    │   └── doneOrder/
    │       └── index.js        # [改造] 激活筛选逻辑
    └── request/              # (无改动, 已在迭代二准备好)
        └── api.js
```

### **受影响的现有模块说明**

1.  **`src/pages/{auditOrder,doingOrder,doneOrder}/index.js`**:
    *   **适配说明**: 这是本次迭代的核心工作区域。我们将激活迭代二中添加的 `filters` 占位，并实现完整的受控筛选逻辑。
    *   **扩展计划**:
        *   改造表格 `columns` 定义，通过 `filteredValue` 属性将筛选状态与组件 `state` 绑定。
        *   在表格的 `onChange` 事件处理器中，实现从 `filters` 参数更新组件 `state`，并触发API重新请求数据的完整闭环。
        *   （仅验证）在“单号”列的 `render` 方法中，改造跳转逻辑，确保 `role_type` 能被传递到详情页。

### **核心流程图**

下图清晰地展示了从用户操作到UI更新的完整受控筛选流程。

```mermaid
flowchart TD
    A[用户点击筛选菜单并确认] --> B[Table 组件触发 onChange 事件];
    B -- "(pagination, newFilters, sorter)" --> C[页面组件的 onChange 处理器被调用];
    C --> D["从 newFilters 中提取'单号'列的筛选值<br/>(e.g., newFilters.order_id)"];
    D --> E["调用 setState, 更新 state 中的筛选状态<br/>(e.g., this.setState({ searchParams: {...}, pageNum: 1 }))"];
    E -- setState回调或useEffect --> F[调用数据请求方法(e.g., requestMyAuditPageOrder)];
    F --> G["API请求体中包含最新的筛选参数<br/>{ ..., filter_by_role: ['CC_TO_ME'] }"];
    G --> H[API 返回筛选后的数据];
    H --> I[setState 更新 dataSource, 触发UI重新渲染];
    I --> J[表格显示筛选后的结果];
```

---

### **渐进式小步迭代式开发与集成步骤**

以下是为前端迭代三制定的4个小步迭代开发计划，每一步都聚焦于一个具体的页面，确保稳定推进。

---

#### **第一步：在“我的审批”列表页实现完整的受控筛选**

*   **目标**: 以 `auditOrder` 页面为试点，实现从UI操作到API请求的完整筛选功能闭环。
*   **操作文件**:
    *   `src/pages/auditOrder/index.js` (改造)
*   **具体编码说明**:
    1.  **绑定受控值**: 找到 `columns` 定义中的“单号”列，为其添加 `filteredValue` 属性，使其值受 `state` 控制。
        ```javascript
        // 在 "单号" 列的 column 定义中
        filteredValue: this.state.searchParams.filter_by_role || null,
        ```
    2.  **实现 `onChange` 逻辑**:
        *   当前项目 `auditOrder/index.js` 中没有统一的 `Table` `onChange` 处理器，而是分散在 `handlePageChange` 等方法中。我们需要整合这些逻辑。
        *   为 `Table` 组件添加一个统一的 `onChange` 处理器 `handleTableChange`。
            ```jsx
            <Table onChange={this.handleTableChange} ... />
            ```
        *   实现 `handleTableChange` 方法：
            ```javascript
            // file: src/pages/auditOrder/index.js

            handleTableChange = (pagination, filters, sorter) => {
              const { searchParams } = this.state;
              const newSearchParams = {
                ...searchParams,
                // 从 filters 对象中提取 'order_id' 列的筛选值，如果不存在则为空数组
                filter_by_role: filters.order_id || []
              };

              this.setState({
                searchParams: newSearchParams,
                pageNum: pagination.current // 分页也在这里统一处理
              }, () => {
                this.requestMyAuditPageOrder(); // 调用API刷新
              });
            };
            ```
    3.  **移除旧逻辑**: 由于 `handleTableChange` 已处理分页，可以考虑简化或移除 `handlePageChange` 的独立逻辑，防止重复调用。
*   **验证方法**:
    *   进入“我的审批”页面。
    *   点击“单号”列的筛选图标，选择一个或多个身份（如 `[待我审批]`）。
    *   点击“OK”后，观察Network面板，应立即发起一个新的API请求，其Payload中包含 `filter_by_role: ["TO_BE_APPROVED"]`。
    *   表格数据应刷新为筛选后的结果。
    *   再次打开筛选菜单，之前勾选的项应保持选中状态（受控`filteredValue`的效果）。
    *   点击筛选菜单的`[Reset]`按钮，再点`[OK]`，应发起 `filter_by_role: []` 的请求，列表恢复未筛选状态。

---

#### **第二步：将筛选逻辑同步应用于“进行中”和“已完结”列表**

*   **目标**: 将已验证成功的筛选模式，快速、安全地复制到 `doingOrder` 和 `doneOrder` 页面。
*   **操作文件**:
    *   `src/pages/doingOrder/index.js` (改造)
    *   `src/pages/doneOrder/index.js` (改造)
*   **具体编码说明**:
    1.  **对 `doingOrder` 和 `doneOrder` 分别执行**:
        *   **State**: 在组件的 `state` 中添加筛选状态，例如 `this.state = { ..., roleFilters: [] };`。
        *   **绑定受控值**: 在 `columns` 的“单号”列定义中，添加 `filteredValue: this.state.roleFilters || null`。
        *   **实现 `onChange`**: 为 `Table` 组件添加 `onChange` 处理器。该处理器的逻辑与第一步中的 `handleTableChange` 类似：从`filters`参数中获取筛选值，更新 `state`，然后调用API重新加载数据。
        *   **API调用**: 确保在调用 `requestMyDoingOrder` / `requestMyDoneOrder` 时，将 `state` 中的筛选值作为 `filter_by_role` 参数传递。
*   **验证方法**:
    *   分别进入“进行中的工单”和“已完结的工单”页面。
    *   重复第一步的验证流程，确保筛选功能在这些页面上同样表现正常。
    *   确认筛选菜单中的选项符合需求（“我申请的”、“抄送我的”）。

---

#### **第三步：实现详情页跳转时传递`role_type`**

*   **目标**: 改造列表页的跳转逻辑，以便在用户点击进入详情页时，能够将当前工单的 `role_type` 信息携带过去。
*   **操作文件**:
    *   `src/pages/auditOrder/index.js` (改造)
    *   `src/pages/doingOrder/index.js` (改造)
    *   `src/pages/doneOrder/index.js` (改造)
*   **具体编码说明**:
    1.  **对每个列表页统一执行**:
        *   找到“单号”列的 `render` 方法。当前实现可能是通过调用一个`showDetailDrawer(orderId)`方法来打开抽屉。
        *   我们需要修改这个调用，将整条记录 `record` 对象传递过去。
            ```jsx
            // columns 定义中的 "单号" 列
            render: (text, record) => (
              <div>
                <RoleTag roleType={record.role_type} />
                <a onClick={() => this.showDetailDrawer(record)}>{text}</a>
              </div>
            ),
            ```
        *   修改 `showDetailDrawer` 方法（或类似名称的方法），使其接收 `record` 对象，并在打开详情抽屉/页面时，将 `record.role_type` 传递过去。如果详情页是路由页面，使用 `react-router`的 `history.push` 或 `Link` 的 `state` 属性；如果是抽屉组件，则作为 `prop` 传递。
*   **验证方法**:
    *   在任意列表页，找到一个“抄送我的”工单。
    *   在详情页组件代码中，临时添加 `console.log(this.props)` 或 `console.log(props.location.state)`。
    *   点击该工单进入详情页。
    *   在浏览器控制台中，应能看到被打印出的 `role_type: "CC_TO_ME"`。

---

#### **第四步：验证工单详情页的只读权限（非编码任务）**

*   **目标**: 根据需求文档，此步骤是一个**验证性任务**，旨在确认现有权限逻辑对 `CC_TO_ME` 角色能够按预期工作，无需编写新的权限控制代码。
*   **操作文件**:
    *   `src/pages/{工单详情页组件}` (仅查看和验证)
*   **具体验证说明**:
    1.  **找到权限控制点**: 打开工单详情页的组件代码，找到控制“审批”、“转交”、“驳回”等操作按钮显示/隐藏的判断逻辑。这通常是一个 `if` 语句，例如 `if (this.props.user.isOperator && order.canBeOperated)`。
    2.  **模拟场景**: 找到一个“抄送我的”工单并进入其详情页。
    3.  **观察UI**: 仔细检查页面UI。所有操作性按钮（如“审批通过”、“驳回”、“变更审批人”等）都**不应该**显示给抄送人。页面应呈现为纯信息展示状态。
    4.  **确认结果**: 如果所有操作按钮都已正确隐藏，则证明现有权限逻辑对“抄送人”角色是有效的，验证通过。如果发现有按钮未隐藏，则记录下来，这可能是一个需要修复的缺陷，但它超出了本次迭代三的原定范围。

完成以上所有步骤，您就成功地为工单系统赋予了完整的身份标签展示和筛选功能，并确保了抄送人的权限隔离，整个抄送功能前端部分至此已全部高质量交付。