# 前端详细设计：工单抄送功能 - 迭代三

本次迭代的目标是实现列表页的身份筛选功能，并确保抄送人在详情页拥有正确的只读权限。我们将激活在迭代二中预留的占位结构，并用具体的业务逻辑填充它们。

## 项目结构与总体设计

### 目录结构

本次迭代不新增文件，仅对现有文件进行改造。目录结构保持不变。

```
wfo-frontend/
└── src/
    ├── components/
    │   ├── CcInput/            # (无改动)
    │   └── RoleTag/            # (无改动)
    ├── pages/
    │   ├── auditOrder/
    │   │   └── index.js        # [改造] 本次迭代核心改造文件
    │   ├── commonOrder/
    │   │   └── index.js        # (无改动)
    │   ├── doingOrder/
    │   │   └── index.js        # [改造] 本次迭代核心改造文件
    │   └── doneOrder/
    │       └── index.js        # [改造] 本次迭代核心改造文件
    └── request/
        └── api.js              # [改造] 激活参数传递逻辑
```

### 整体逻辑和交互时序图

此图详细描述了用户使用筛选功能的核心工作流程，展示了前端组件状态与API调用之间的联动关系。

```mermaid
sequenceDiagram
    participant User as 用户
    participant P_OrderList as pages/{doing,done,audit}Order/index.js
    participant R_Api as request/api.js
    participant Backend as 后端API

    User->>P_OrderList: 点击"单号"列表头的筛选图标
    P_OrderList->>User: Ant Design Table 弹出筛选菜单
    User->>P_OrderList: 勾选筛选条件 (e.g., [抄送我的]), 点击 [OK]
    
    Note right of P_OrderList: AntD Table的onChange事件被触发，<br/>携带了最新的筛选值: ['CC_TO_ME']
    
    P_OrderList->>P_OrderList: 调用 onChange 处理函数, 更新组件 state.filter_by_role = ['CC_TO_ME']
    activate P_OrderList
    
    P_OrderList->>P_OrderList: 触发副作用(useEffect/componentDidUpdate), 调用API重新获取数据
    P_OrderList->>R_Api: getMyOrders({..., filter_by_role: ['CC_TO_ME']})
    
    activate R_Api
    R_Api->>Backend: POST /GetMy...Order (body包含filter_by_role)
    activate Backend
    Backend->>Backend: 执行 SQL IN (...) 查询
    Backend-->>R_Api: 返回已筛选的工单列表
    deactivate Backend
    R_Api-->>P_OrderList: 返回列表数据
    deactivate R_Api
    
    P_OrderList->>P_OrderList: 更新 state.tableData, 触发UI重新渲染
    P_OrderList-->>User: 显示已根据"抄送我的"筛选后的列表
    deactivate P_OrderList
```

## 数据实体结构深化

本迭代的核心是为列表页组件引入并管理筛选状态。

```mermaid
erDiagram
    LIST_PAGE_COMPONENT_STATE {
        array tableData "工单列表数据"
        object pagination "分页信息"
        array filter_by_role "当前激活的角色筛选值, e.g., ['CC_TO_ME']"
    }

    ANTD_COLUMN_DEFINITION {
        string title
        string dataIndex
        function render
        array filters "提供给筛选菜单的选项"
        array filteredValue "受控于 LIST_PAGE_COMPONENT_STATE"
    }
    
    LIST_PAGE_COMPONENT_STATE ||--|{ ANTD_COLUMN_DEFINITION : "controls"
```

## 模块化文件详解 (File-by-File Breakdown)

---

### `src/request/api.js`

a. **文件用途说明**
此文件负责所有API调用。本次迭代将确保列表请求函数能正确地将`filter_by_role`参数传递给后端。

c. **函数/方法详解**

#### **`getMyDoingOrder`, `getMyDoneOrder`, `getMyAuditOrder` (激活改造)**
- **用途:** 获取工单列表，并支持按角色筛选。
- **输入参数:** `params` (Object): 包含分页信息和`filter_by_role`数组。
- **输出数据结构:** `Promise<Object>`: resolve时返回后端完整响应。
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start: getMy...Order(params)"] --> B["从 params 中解构出 filter_by_role 和其他参数"];
      B --> C["构造一个包含所有非空参数的请求体 body"];
      C --> D["发起 POST /GetMy...Order 请求, body 为构造的请求体"];
      D --> E{请求成功?};
      E -- Yes --> F["返回 Promise.resolve(response.data)"];
      E -- No --> G["返回 Promise.reject(error)"];
      F & G --> H[End];
  ```
  **实现说明:** 迭代二中此函数已定义，本次确保其实现能完整地将`params`对象作为请求体发送。无需新增代码，只需验证现有实现即可。

---

### `src/pages/{doing,done,audit}Order/index.js` (统一改造方案)

a. **文件用途说明**
列表页面的核心实现。本次迭代将激活并实现表格的受控筛选功能。

c. **函数/方法详解**

#### **组件State (新增/改造)**
- **用途:** 在组件的 state 中添加一个状态，用于存储和控制当前表格的角色筛选值。
- **实现流程:**
  ```mermaid
  flowchart TD
    subgraph "使用 React Hooks"
        A["const [filters, setFilters] = useState({})"] --> B["filters 初始值: { order_id: null }"];
    end
    subgraph "使用 Class Component"
        C["this.state = { ... }"] --> D["添加 filters: { order_id: null }"];
    end
  ```
  **说明:** `order_id`是我们要筛选的列的`dataIndex`。初始值为`null`表示不筛选。当用户筛选后，其值将变为数组，如`['CC_TO_ME']`。

#### **表格列定义 `columns` (激活改造)**
- **用途:** 填充在迭代二中为"单号"列预留的`filters`和`filteredValue`属性。
- **实现流程 (以 `auditOrder/index.js` 为例):**
  ```mermaid
  flowchart TD
      A[Start: 定义columns数组] --> B["找到'单号'列的配置对象"];
      B --> C["填充 filters 属性"];
      subgraph "filters: [...]"
          direction LR
          C1["text: '[待我审批]', value: 'TO_BE_APPROVED'"]
          C2["text: '[我已审批]', value: 'ALREADY_APPROVED'"]
          C3["text: '[抄送我的]', value: 'CC_TO_ME'"]
      end
      C --> D["填充 filteredValue 属性"];
      subgraph "filteredValue: state.filters.order_id || null"
          D1["使其受组件 state 控制"]
      end
      B --> F["(可选) 改造 render 方法, 支持传递 role_type 到详情页"];
       subgraph "render: (text, record) => { ... }"
         F1["用 Link 组件包裹单号"]
         F2["设置 to={{ pathname: `/path/to/detail/${record.order_id}`, state: { role_type: record.role_type } }}"]
      end
      D & F --> G[End: 返回改造后的columns数组]
  ```
  **说明:**
  - **`doingOrder`/`doneOrder` 的 `filters`**: 应为 `[{ text: '[我申请的]', value: 'APPLICANT' }, { text: '[抄送我的]', value: 'CC_TO_ME' }]`。
  - **`filteredValue`**: 这一步至关重要，它将筛选状态的控制权交给了React组件，实现了“受控筛选”，是`Reset`功能的基础。
  - **`onFilter`**: **我们不使用此属性**，因为它是为前端筛选设计的。后端筛选的逻辑统一在Table的`onChange`处理器中实现。

#### **表格`onChange`处理函数 (激活改造)**
- **用途:** 监听表格的所有变化（分页、排序、筛选），更新组件状态，并触发API调用。
- **输入参数:** `pagination`, `filters`, `sorter`
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start: onChange(pagination, newFilters, sorter)"] --> B["更新分页和排序的状态(沿用现有逻辑)"];
      B --> C["调用 this.setState 或 setFilters, 更新筛选状态"];
      subgraph "更新筛选状态"
        C1["this.setState({ filters: newFilters })"]
      end
      C --> D["在 setState 的回调函数或 useEffect 中调用API获取数据"];
      subgraph "调用API"
        D1["从 state 中读取最新的分页、排序、筛选信息"]
        D2["将 state.filters.order_id 作为 filter_by_role 参数"]
        D3["调用 api.getMy...Order(...)"]
      end
      D --> E[End]
  ```
  **实现说明:** Ant Design的`onChange`会把所有列的筛选状态以`{ dataIndex: [...] }`的格式传入`filters`参数。我们只需将其完整存入组件state，然后在请求API时取出`filters.order_id`作为`filter_by_role`的值即可。如果用户清空筛选，`filters.order_id`会是`null`或空数组，符合API要求。

#### **工单详情页权限处理**
- **用途:** 确保抄送人访问详情页时，没有操作权限。
- **实现要点:**
    1.  **数据传递:** 在列表页的`render`方法中，如上图所示，使用 `react-router` 的 `Link` 组件，并通过`state`属性将`role_type`传递到详情页。
    2.  **权限确认 (非编码任务):** 在详情页组件中，确认现有的用于控制“审批按钮”、“变更审批人按钮”显示的条件（例如 `if (currentUser.isOperator)`），对于从路由状态中接收到的 `role_type` 为 `CC_TO_ME` 的情况，能够正确地将这些按钮隐藏。
    3.  **设计文档声明:** 本设计文档确认，**无需为详情页编写新的权限判断逻辑**，仅需验证现有逻辑对 `CC_TO_ME` 角色生效即可。这是一个测试和验证任务，而非开发任务。

## 迭代演进依据

这份设计方案确保了功能的健壮性和未来的可维护性：

1.  **遵循既定模式:** 采用Ant Design Table的**受控模式**进行筛选，这是官方推荐的最佳实践。它使得组件的状态与UI表现完全同步，逻辑清晰，易于调试，并能轻松实现`Reset`等复杂交互。

2.  **增量式开发:** 设计完美地建立在迭代二的基础上，将预留的`filters`, `filteredValue`和`onChange`逻辑填充完整，没有引入任何破坏性变更。这种增量演进的方式风险最低，效率最高。

3.  **职责单一:** 页面组件 (`...Order/index.js`) 负责管理UI状态和触发事件；API层 (`api.js`) 负责数据获取。这种清晰的职责划分使得代码更易于理解和维护。

4.  **务实的设计:** 对于详情页的权限，设计明确指出这是一个“验证”而非“开发”任务，避免了因误解需求而进行的过度设计（YAGNI原则），节省了开发资源。