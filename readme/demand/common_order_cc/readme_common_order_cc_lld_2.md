# 前端详细设计：工单抄送功能 - 迭代二

本次设计将聚焦于迭代二的目标：在工单列表中整合并展示抄送身份。同时，遵照架构完整性原则，为后续迭代三的功能预留清晰的结构和占位实现。

## 项目结构与总体设计

### 目录结构

目录结构保持完整性，包含所有迭代涉及的文件。本次迭代将重点实现`RoleTag`组件，并改造三个列表页面。

```
wfo-frontend/
└── src/
    ├── components/
    │   ├── CcInput/            # 迭代一已实现
    │   │   ├── index.js
    │   │   └── style.css
    │   └── RoleTag/            # [新增] 本次迭代核心组件
    │       ├── index.js
    │       └── style.css
    ├── pages/
    │   ├── auditOrder/
    │   │   └── index.js        # [改造] 本次迭代改造此文件
    │   ├── commonOrder/
    │   │   └── index.js        # 迭代一已改造
    │   ├── doingOrder/
    │   │   └── index.js        # [改造] 本次迭代改造此文件
    │   └── doneOrder/
    │       └── index.js        # [改造] 本次迭代改造此文件
    └── request/
        └── api.js              # [改造] 迭代三再实现，本次仅定义函数签名
```

### 整体逻辑和交互时序图

此图描述了用户访问工单列表页的核心流程，展示了迭代二中新增的`role_type`字段如何被利用，并体现了为迭代三预留的筛选逻辑。

```mermaid
sequenceDiagram
    participant User as 用户
    participant P_OrderList as pages/{doing,done,audit}Order/index.js
    participant C_RoleTag as components/RoleTag/index.js
    participant R_Api as request/api.js
    participant Backend as 后端API

    User->>P_OrderList: 访问工单列表页
    activate P_OrderList
    
    Note right of P_OrderList: 从state中读取筛选参数。<br/>迭代二时，filter_by_role为空数组。
    P_OrderList->>R_Api: getMyOrders({filter_by_role: []})
    
    activate R_Api
    Note right of R_Api: 迭代三再实现，用固定返回值占位
    R_Api->>Backend: POST /GetMy...Order
    
    activate Backend
    Backend-->>R_Api: 返回带 role_type 的工单列表
    deactivate Backend
    
    R_Api-->>P_OrderList: 返回列表数据
    deactivate R_Api
    
    P_OrderList->>P_OrderList: 遍历列表数据，准备渲染表格
    
    loop 每一条工单记录
        P_OrderList->>C_RoleTag: 渲染 <RoleTag roleType={record.role_type} />
        activate C_RoleTag
        C_RoleTag-->>P_OrderList: 返回带样式的标签JSX
        deactivate C_RoleTag
    end
    
    P_OrderList-->>User: 显示包含身份标签的工单列表
    deactivate P_OrderList
```

## API接口定义

迭代二不涉及新增API，仅需消费后端改造后的列表接口返回的新字段。为迭代三预留的参数改造将在`api.js`中以函数签名的形式体现。

## 数据实体结构深化

本迭代不引入新的复杂数据实体，主要是对现有数据结构的消费和组件Props的定义。

```mermaid
erDiagram
    WORK_ORDER_ITEM {
        string order_id
        string title
        string role_type "APPLICANT, CC_TO_ME, etc."
        string other_fields "..."
    }

    ROLE_TAG_PROPS {
        string roleType "The value from WORK_ORDER_ITEM"
    }

    WORK_ORDER_ITEM ||--o{ ROLE_TAG_PROPS : "provides data for"
```

## 模块化文件详解 (File-by-File Breakdown)

---

### `src/components/RoleTag/index.js`

a. **文件用途说明**
一个纯展示性UI组件，根据传入的`role_type`，渲染出对应文案和颜色的身份标签。

b. **文件内类图 (Mermaid `classDiagram`)**
```mermaid
classDiagram
    direction LR
    class RoleTag {
        <<Functional Component>>
        +roleType: string
    }
```

c. **函数/方法详解**

#### **`RoleTag(props)` (组件主体)**
- **用途:** 渲染一个身份标签。
- **输入参数:** `props` (Object):
    - `roleType` (string): 角色类型枚举值 (`APPLICANT`, `CC_TO_ME`, `TO_BE_APPROVED`, `ALREADY_APPROVED`)。
- **输出数据结构:** JSX Element。
- **实现流程:**
  ```mermaid
  flowchart TD
      A[Start] --> B["定义角色映射常量 ROLE_CONFIG"];
      subgraph ROLE_CONFIG
        direction LR
        C1["APPLICANT: { text: '[我申请的]', color: 'default' }"]
        C2["CC_TO_ME: { text: '[抄送我的]', color: 'default' }"]
        C3["TO_BE_APPROVED: { text: '[待我审批]', color: 'orange' }"]
        C4["ALREADY_APPROVED: { text: '[我已审批]', color: 'default' }"]
      end
      B --> D["根据 props.roleType 从 ROLE_CONFIG 获取配置"];
      D --> E{找到配置?};
      E -- Yes --> F["使用获取的 text 和 color 渲染一个 Ant Design Tag 组件"];
      E -- No --> G["返回 null (不渲染任何内容)"];
      F --> H[End];
      G --> H;
  ```

---
### `src/components/RoleTag/style.css`

a. **文件用途说明**
为`RoleTag`组件提供样式，确保其在表格单元格内能正确布局。

c. **内容详解**
- **用途:** 定义标签样式。
- **实现要点:**
  - 定义一个基础的class，例如`.role-tag`。
  - 设置 `font-size`, `padding`, `border-radius`, `border` 等基础样式。
  - Ant Design的Tag组件自带颜色，无需为`orange`或`default`单独写CSS。此处主要是为了微调边距或字体。

---
### `src/pages/{doing,done,audit}Order/index.js` (统一改造方案)

a. **文件用途说明**
这三个文件分别对应“进行中”、“已完结”、“我的审批”工单列表页面。改造的核心是修改Ant Design Table的列定义，以展示身份标签。

c. **函数/方法详解**

#### **表格列定义 `columns` (改造)**
- **用途:** 修改“单号”列(`dataIndex: 'order_id'`)的定义，使其能够渲染身份标签，并为迭代三预留筛选功能。
- **实现流程 (以 `auditOrder/index.js` 为例):**
  ```mermaid
  flowchart TD
      A[Start: 定义columns数组] --> B["找到'单号'列的配置对象"];
      B --> C["修改该对象的 render 方法"];
      subgraph "render: (text, record) => { ... }"
          direction TB
          C1["创建一个外层容器, e.g., <div style={{display: 'flex', flexDirection: 'column'}}>"]
          C2["在容器内部渲染 <RoleTag roleType={record.role_type} />"]
          C3["在其下方渲染原始的单号 {text}"]
          C4["返回外层容器"]
      end
      C --> D["为该对象添加 filters 和 onFilter 属性"];
      subgraph "迭代三再实现，用固定返回值占位"
        D1["filters: 根据页面类型返回不同的筛选选项数组"]
        D2["e.g., for auditOrder: [{text: '[待我审批]', value: 'TO_BE_APPROVED'}, ...]"]
        D3["onFilter: 返回一个固定返回 true 的函数, e.g., () => true"]
      end
      D --> E[End: 返回改造后的columns数组]
  ```

#### **表格`onChange`处理函数 (改造)**
- **用途:** 监听表格的变化（分页、排序、筛选），为迭代三的后端筛选做准备。
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start: onChange(pagination, filters, sorter)"] --> B["迭代三再实现，用固定返回值占位"];
      B --> C["从 filters 参数中提取 'order_id' 列的筛选值"];
      C --> D["将筛选值更新到组件的 state 中"];
      D --> E["使用新的 state (包含筛选值) 重新调用API获取列表数据"];
      E --> F[End]
  ```
  **迭代二实现说明:** 在本次迭代中，此函数可能已存在用于处理分页。我们只需确保其结构清晰，可以轻松在迭代三中添加筛选逻辑即可。无需做实质性改动。

---
### `src/request/api.js`

a. **文件用途说明**
统一封装所有对后端API的调用。为迭代三改造列表接口，增加筛选参数。

c. **函数/方法详解**

#### **`getMyDoingOrder`, `getMyDoneOrder`, `getMyAuditOrder` (改造)**
- **用途:** 获取各类工单列表。在函数签名和实现上为迭代三的筛选功能做好准备。
- **输入参数:** `params` (Object): 包含分页、排序以及`filter_by_role`等所有可能的查询参数。
- **输出数据结构:** `Promise<Object>`: resolve时返回后端响应。
- **实现流程:**
  ```mermaid
  flowchart TD
      A["Start: getMy...Order(params)"] --> B["迭代三再实现，用固定返回值占位"];
      B --> C["构造请求体, 将 params 对象作为 body"];
      C --> D["发起 POST /GetMy...Order 请求"];
      D --> E["返回 Promise"];
      E --> F[End];
  ```
  **迭代二实现说明:** 虽然我们为`params`参数预留了`filter_by_role`字段，但在迭代二的实际调用中，该字段将是一个空数组或不存在。API调用本身能正常工作，后端会忽略此空参数。这样，接口调用代码无需在迭代三时再次修改。

## 迭代演进依据

这份设计方案确保了系统能够平滑、低成本地向后续迭代演进：

1.  **高内聚组件 (`RoleTag`):** `RoleTag`是一个独立的、无状态的纯展示组件。它不关心业务逻辑，只负责根据输入渲染UI。这使得它极易测试、复用和维护。未来任何页面需要展示身份标签，只需引入并使用即可。

2.  **最小化侵入:** 对现有列表页的改造被严格限制在Ant Design Table的`columns`定义中。这是AntD组件预留的、最标准的扩展方式，没有改变页面原有的状态管理和数据请求流程，将引入新Bug的风险降到了最低。

3.  **清晰的演进路径:** 设计明确区分了迭代二的“实现”和迭代三的“占位”。当迭代三开始时，开发者只需：
    *   在`columns`定义中，将`filters`和`onFilter`的占位实现替换为真实逻辑。
    *   在表格的`onChange`处理函数中，启用从`filters`参数更新state并重新请求数据的逻辑。
    *   在调用API时，传入从state中读取的`filter_by_role`数组。
    所有改造点都已预先定义，无需进行任何破坏性重构。

4.  **关注点分离:** API的改造被封装在`request/api.js`中，UI组件不直接感知请求参数的细节。这使得未来即使API参数再次变化，UI层的代码也可能无需改动，只需调整`api.js`中的封装即可。