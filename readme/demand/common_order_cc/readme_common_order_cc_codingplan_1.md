# 编码计划迭代一

### **代码目录结构**

根据详细设计，我们将采用以下目录结构。`[新增]`表示新创建的文件/目录，`[改造]`表示需要修改的现有文件。

```
wfo-frontend/
└── src/
    ├── components/
    │   └── CcInput/            # [新增] 核心抄送人输入组件
    │       ├── index.js
    │       └── style.css
    ├── pages/
    │   └── commonOrder/
    │       └── index.js        # [改造] 集成CcInput组件，处理抄送人数据
    └── request/
        └── api.js              # [改造] 添加新API函数，改造现有API函数
```

### **受影响的现有模块说明**

本次迭代将主要影响以下两个现有模块：

1.  **`src/request/api.js`**:
    *   **适配说明**: 这是API请求的统一出口。我们将在这里新增一个用于校验抄送人邮箱的函数 `validateUser`，并改造现有的通用工单创建函数 `requestCommonOrder`，使其能够携带 `cc_user_infos` 字段。

2.  **`src/pages/commonOrder/index.js`**:
    *   **适配说明**: 这是通用工单的创建页面，也是本次功能的核心集成点。根据现有代码分析，该组件是名为 `CommonOrderForm` 的React类组件。
    *   **扩展计划**:
        *   我们将在其 `state` 中新增一个状态 `cc_user_infos` 来管理抄送人列表。
        *   将在其 `render` 方法中嵌入新的 `CcInput` 组件。
        *   将在其 `handleSubmit` 方法中，将 `cc_user_infos` 随工单表单一同提交。
        *   将复用其现有的 `state` 管理和数据提交逻辑，以最小化侵入。

### **核心流程图**

下图展示了本次迭代核心功能——“添加抄送人”的内部处理流程。

```mermaid
flowchart TD
    subgraph CcInput Component
        A[用户输入邮箱后按回车/空格] --> B{前端自校验};
        B -- "1. 是当前用户?" --> C[提示「不能抄送给自己」];
        B -- "2. 已在列表?" --> D[提示「该用户已被添加」];
        B -- "校验通过" --> E["调用 api.validateUser(email)"];
        E --> F{API返回成功?};
        F -- Yes --> G["调用 props.onChange() 更新父组件状态"];
        G --> H[清空输入框, 新增Tag];
        F -- No --> I[显示API返回的错误信息];
    end
```

---

### **渐进式小步迭代式开发与集成步骤**

以下是为前端迭代一制定的6个小步迭代开发计划。

---

#### **第一步：环境准备与API层搭建**

*   **目标**: 创建新组件所需的文件结构，并在`api.js`中封装新的 `validateUser` 接口调用函数。这是所有后续工作的基础。
*   **操作文件**:
    *   `src/components/CcInput/index.js` (创建)
    *   `src/components/CcInput/style.css` (创建)
    *   `src/request/api.js` (改造)
*   **具体编码说明**:
    1.  在 `src/components/` 目录下创建 `CcInput` 文件夹。
    2.  在 `CcInput` 文件夹内创建 `index.js` 和 `style.css` 两个空文件。
    3.  打开 `src/request/api.js`，新增一个异步函数 `validateUser`：
        ```javascript
        // file: src/request/api.js

        // (示例代码)
        export async function validateUser(email) {
          try {
            const response = await post('/order/validate-user', { email }); // 假设已有post工具函数
            if (response.resp_common && response.resp_common.ret === 0) {
              return Promise.resolve(response); // 成功时返回整个响应体
            }
            // 业务失败，返回具体的错误信息
            return Promise.reject(response.resp_common ? response.resp_common.msg : '校验失败');
          } catch (error) {
            // 网络或代码异常
            return Promise.reject('校验服务异常，请稍后重试');
          }
        }
        ```
*   **验证方法**:
    *   确认文件和目录已成功创建。
    *   在浏览器的开发者工具Console中，手动引入并调用 `validateUser('<EMAIL>')`，检查Network面板中是否正确发起了 `POST /order/validate-user` 请求，并观察Promise返回是否符合预期。

---

#### **第二步：创建骨架`CcInput`组件并集成到创建工单页**

*   **目标**: 在`commonOrder`页面上渲染出抄送功能的入口和基础输入框，实现UI的初步展示和交互。
*   **操作文件**:
    *   `src/pages/commonOrder/index.js` (改造)
    *   `src/components/CcInput/index.js` (改造)
*   **具体编码说明**:
    1.  **在 `src/pages/commonOrder/index.js` (`CommonOrderForm` 类) 中**:
        *   在 `constructor` 中初始化状态: `this.state = { ..., showCcInput: false };`。
        *   在 `render()` 方法的表单区域合适位置，添加一个链接式按钮：
            ```jsx
            <a onClick={() => this.setState({ showCcInput: !this.state.showCcInput })}>
              {this.state.showCcInput ? '收起' : '[添加抄送]'}
            </a>
            ```
        *   在该链接下方，根据 `this.state.showCcInput` 条件渲染 `<CcInput />` 组件。
            ```jsx
            {this.state.showCcInput && <CcInput />}
            ```
        *   在文件顶部 `import CcInput from '../../components/CcInput';`。
    2.  **在 `src/components/CcInput/index.js` 中**:
        *   创建一个简单的函数式组件，它仅渲染一个 Ant Design 的 `Input` 组件和人数计数器。
            ```jsx
            // file: src/components/CcInput/index.js
            import React from 'react';
            import { Input } from 'antd';
            import './style.css'; // 稍后添加样式

            const CcInput = () => {
              return (
                <div className="cc-input-container">
                  <Input placeholder="请输入抄送人邮箱，按回车或空格确认" />
                  <span className="cc-count">0/50</span>
                </div>
              );
            };
            export default CcInput;
            ```
*   **验证方法**:
    *   启动应用，进入“通用工单创建”页面。
    *   应能看到 `[添加抄送]` 链接。
    *   点击链接，下方应能平滑地展示出抄送人输入框和计数器。再次点击，应能收起。

---

#### **第三步：实现抄送人添加与校验核心逻辑**

*   **目标**: 为 `CcInput` 组件注入生命，实现输入、前端自校验、后端API校验、添加和错误提示的完整流程。
*   **操作文件**:
    *   `src/pages/commonOrder/index.js` (改造)
    *   `src/components/CcInput/index.js` (改造)
*   **具体编码说明**:
    1.  **在 `src/pages/commonOrder/index.js` (`CommonOrderForm` 类) 中**:
        *   在 `state` 中新增 `ccList: []` 用于存储完整的抄送人信息对象。
        *   新增一个处理函数 `handleCcChange = (newList) => { this.setState({ ccList: newList }); };`。
        *   修改 `<CcInput />` 的调用，使其成为受控组件：
            ```jsx
            <CcInput
              value={this.state.ccList}
              onChange={this.handleCcChange}
              currentUserEmail={this.props.currentUser.email} // 假设当前用户信息在props中
            />
            ```
    2.  **在 `src/components/CcInput/index.js` 中**:
        *   将组件重构为使用 `useState` 的函数组件，以管理内部状态：`inputValue`, `errorMsg`。
        *   从 `props` 接收 `value` (已选列表), `onChange` (回调), `currentUserEmail`。
        *   实现 `handleKeyDown` 事件处理器，绑定到 `Input` 的 `onKeyDown`。
        *   在 `handleKeyDown` 中，实现完整的校验逻辑（如流程图所示）：
            a.  检查按键是否为回车或空格。
            b.  前端自校验：与 `currentUserEmail` 比较，与 `props.value` 列表比较。若失败，使用 `setErrorMsg` 设置错误提示。
            c.  调用第一步创建的 `api.validateUser(inputValue)`。
            d.  `Promise.then` (成功): 调用 `props.onChange` 回传包含新用户信息的数组，并清空 `inputValue` 和 `errorMsg`。
            e.  `Promise.catch` (失败): 使用 `setErrorMsg` 显示后端返回的错误。
        *   在JSX中，根据 `errorMsg` 动态渲染错误提示区域。
        *   遍历 `props.value` 数组，渲染出已添加用户的 Ant Design `Tag` 标签。
        *   更新人数计数器 `<span>{props.value.length}/50</span>`。
*   **验证方法**:
    *   输入当前登录用户的邮箱并回车，应提示“不能抄送给自己”。
    *   成功添加一个用户后，再次输入相同邮箱并回车，应提示“该用户已被添加”。
    *   输入一个不存在的邮箱，应提示后端返回的错误信息，如“该邮箱不存在...”。
    *   输入一个有效的、未添加的邮箱，输入框应清空，下方应出现一个包含“姓名 (部门)”的标签，且计数器更新。

---

#### **第四步：实现抄送人移除逻辑**

*   **目标**: 为每个已添加的抄送人标签增加关闭按钮，允许用户将其从列表中移除。
*   **操作文件**:
    *   `src/components/CcInput/index.js` (改造)
*   **具体编码说明**:
    1.  修改 `CcInput` 组件中渲染 `Tag` 的部分，使其 `closable`。
    2.  为 `Tag` 组件添加 `onClose` 事件处理器。
        ```jsx
        // Tag渲染部分
        <Tag
          closable
          key={user.email}
          onClose={() => handleRemove(user.email)}
        >
          {user.display_text}
        </Tag>
        ```
    3.  创建 `handleRemove` 函数。该函数接收要移除的 `email`作为参数。
    4.  在函数内部，通过 `props.value.filter()` 生成一个不包含该用户的新数组。
    5.  调用 `props.onChange(newArray)` 将更新后的数组回传给父组件。
*   **验证方法**:
    *   添加多个抄送人。
    *   点击任意一个标签上的 `x` 关闭按钮。
    *   该标签应立即消失，且人数计数器同步更新。

---

#### **第五步：实现工单提交集成**

*   **目标**: 在用户提交工单时，将抄送人列表数据一同发送给后端。
*   **操作文件**:
    *   `src/pages/commonOrder/index.js` (改造)
    *   `src/request/api.js` (改造)
*   **具体编码说明**:
    1.  **在 `src/pages/commonOrder/index.js` (`CommonOrderForm` 类) 中**:
        *   修改 `handleCcChange` 函数，确保存入 `state` 的是 `[{ cc_open_id, cc_email }]` 格式。
            ```javascript
            handleCcChange = (newListFromCcInput) => {
              const apiCcList = newListFromCcInput.map(user => ({
                cc_open_id: user.open_id,
                cc_email: user.email,
              }));
              this.setState({ cc_user_infos: apiCcList, ccList: newListFromCcInput }); // 可能需要保留完整列表用于显示
            };
            ```
        *   修改 `handleSubmit` 方法。在调用API之前，从 `this.state` 中获取 `cc_user_infos`。
        *   将 `cc_user_infos` 添加到 `requestCommonOrder` 的请求体中。
            ```javascript
            // handleSubmit 内部
            const { title, apply_msg, ..., cc_user_infos } = this.state;
            const params = {
              title,
              apply_msg,
              // ... 其他已有参数
              cc_user_infos, // 新增字段
            };
            requestCommonOrder(params).then(...);
            ```
    2.  **在 `src/request/api.js` 中**:
        *   找到现有的 `requestCommonOrder` 函数（或类似名称的函数），确保它能正确地将包含 `cc_user_infos` 的 `params` 对象作为请求体发送。此步骤可能无需修改，仅需确认。
*   **验证方法**:
    *   添加1-2个抄送人。
    *   填写所有必填的工单信息。
    *   打开浏览器开发者工具，切换到Network面板。
    *   点击“提交”按钮。
    *   在Network面板中找到创建工单的API请求（例如 `/order/common`），查看其Payload(Request Body)。确认其中包含了 `cc_user_infos` 字段，且其值为一个包含正确`cc_open_id`和`cc_email`的数组。

---

#### **第六步：最终清理与优化**

*   **目标**: 添加CSS样式，确保交互动画（如抖动、平滑展开）和UI布局符合设计要求，并进行代码清理。
*   **操作文件**:
    *   `src/components/CcInput/style.css` (改造)
    *   `src/components/CcInput/index.js` (改造)
*   **具体编码说明**:
    1.  **在 `style.css` 中**:
        *   为错误提示信息添加红色字体样式。
        *   为错误提示区域添加 `max-height` 和 `transition` 属性，实现平滑展开/收起效果。
        *   添加输入框抖动动画的 `@keyframes` 和 class。
    2.  **在 `index.js` 中**:
        *   当触发前端自校验错误时，给输入框动态添加抖动动画的CSS class，并在短暂延迟后移除它。
        *   确保错误提示信息在3秒后或用户再次输入时自动消失（可使用 `setTimeout` 和 `useEffect` 清理副作用）。
        *   当抄送人达到50人时，给 `Input` 组件添加 `disabled` 属性。
*   **验证方法**:
    *   重复第三步的错误场景验证，观察是否有符合预期的抖动动画和错误提示的平滑显示/隐藏效果。
    *   快速添加用户直到50人，验证输入框是否被禁用。
    *   整体审阅抄送区域的UI布局是否美观、对齐。

完成以上六个步骤，前端迭代一的核心功能“创建工单时添加抄送人”即可高质量交付。