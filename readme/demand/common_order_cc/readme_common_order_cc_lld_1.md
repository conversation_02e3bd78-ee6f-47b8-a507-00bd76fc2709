# 前端详细设计迭代一

本次前端详细设计将严格遵循您已有的React组件化架构和开发模式。核心思想是：
1.  **封装可复用组件**: 新增的核心功能，如抄送人输入器(`CcInput`)和身份标签(`RoleTag`)，将被封装为独立的、高内聚的组件，以便在未来复用。
2.  **最小化侵入改造**: 对现有页面的改造将尽可能地重用已有逻辑和状态管理模式，只在必要的地方添加新功能，降低回归测试的风险。
3.  **迭代占位**: 目录结构和组件设计将一次性考虑所有迭代的需求。迭代二、三的功能（列表展示与筛选）将在本次设计中定义完整，但具体实现将通过占位或模拟数据的方式在相应迭代中完成。
4.  **清晰的数据流**: 组件之间的数据传递将遵循React的单向数据流原则。复杂的交互逻辑将被封装在独立的组件或Hooks中，保持页面级组件的整洁。

## 目录结构

目录结构将按照概要设计进行，确保新增组件的模块化和既有代码的结构完整性。

```
wfo-frontend/
└── src/
    ├── components/
    │   ├── CcInput/            # [新增] 迭代一：核心抄送人输入组件
    │   │   ├── index.js
    │   │   └── style.css
    │   └── RoleTag/            # [新增] 迭代二：身份标签展示组件
    │       └── index.js
    ├── pages/
    │   ├── auditOrder/
    │   │   ├── index.js        # [改造] 迭代二&三：添加身份标签和筛选功能
    │   │   └── OrderTable.js   # [改造] 迭代二&三：(若该组件存在) 修改表格列定义
    │   ├── commonOrder/
    │   │   └── index.js        # [改造] 迭代一：集成CcInput组件
    │   ├── doingOrder/
    │   │   └── index.js        # [改造] 迭代二&三：添加身份标签和筛选功能
    │   └── doneOrder/
    │       └── index.js        # [改造] 迭代二&三：添加身份标签和筛选功能
    └── request/
        └── api.js              # [改造] 迭代一、二、三：添加和修改API调用函数
```

## 整体逻辑和交互时序图

下图展示了从“添加抄送人”到“提交工单”再到“查看列表”的完整用户旅程和代码交互。

```mermaid
sequenceDiagram
    participant User as 用户
    participant C_CcInput as components/CcInput/index.js
    participant P_CommonOrder as pages/commonOrder/index.js
    participant R_Api as request/api.js
    participant Backend as 后端API
    participant P_OrderList as pages/{doing,done,audit}Order/index.js

    Note over User, Backend: 迭代一: 创建工单与抄送
    User->>C_CcInput: 输入邮箱, 按下回车
    activate C_CcInput
    C_CcInput->>C_CcInput: 前端自校验 (是否自己/重复)
    C_CcInput->>R_Api: validateUser(email)
    R_Api->>Backend: POST /order/validate-user
    Backend-->>R_Api: {name, department, open_id}
    R_Api-->>C_CcInput: 返回用户信息
    C_CcInput->>P_CommonOrder: onChange([{cc_open_id, cc_email}, ...])
    deactivate C_CcInput
    
    User->>P_CommonOrder: 点击 "提交"
    activate P_CommonOrder
    P_CommonOrder->>R_Api: createCommonOrder(formData, cc_user_infos)
    R_Api->>Backend: POST /order/common
    Backend-->>R_Api: {ret: 0, msg: "ok"}
    R_Api-->>P_CommonOrder: 提交成功
    P_CommonOrder-->>User: 提示成功并跳转
    deactivate P_CommonOrder

    Note over User, Backend: 迭代二 & 三: 查看列表与筛选
    User->>P_OrderList: 访问工单列表页
    activate P_OrderList
    P_OrderList->>R_Api: getMyDoingOrder({filter_by_role: []})
    R_Api->>Backend: POST /GetMyDoingOrder
    Backend-->>R_Api: 返回带 role_type 的工单列表
    R_Api-->>P_OrderList: 返回列表数据
    P_OrderList->>P_OrderList: 渲染表格 (使用RoleTag)
    deactivate P_OrderList
```

## API接口定义 (前端视角)

在 `request/api.js` 中将封装以下函数：
-   `validateUser(email)`: 发送 `POST /order/validate-user` 请求。
-   `createCommonOrder(data)`: 发送 `POST /order/common` 请求，`data` 中包含 `cc_user_infos`。
-   `getMyDoingOrder(params)`: 发送 `POST /GetMyDoingOrder` 请求，`params` 中可包含 `filter_by_role`。
-   `getMyDoneOrder(params)`: 发送 `POST /GetMyDoneOrder` 请求，`params` 中可包含 `filter_by_role`。
-   `getMyAuditOrder(params)`: 发送 `POST /GetMyAuditOrder` 请求，`params` 中可包含 `filter_by_role`。

## 组件State与数据模型

-   **`CcInput` 组件内部 State:**
    ```javascript
    // 使用 React Hooks
    const [inputValue, setInputValue] = useState('');
    const [errorMsg, setErrorMsg] = useState(null);
    // value 和 onChange 将由 props 传入，实现受控
    ```

-   **`commonOrder` 页面 State:**
    ```javascript
    // state 中新增
    cc_user_infos: [] // 存储格式: [{ cc_open_id: '...', cc_email: '...' }]
    ```

-   **工单列表页 State (e.g., `auditOrder`):**
    ```javascript
    // state.searchParams 中新增或改造
    searchParams: {
        // ... a bunch of existing search params
        filter_by_role: [] // 存储格式: ['APPLICANT', 'CC_TO_ME']
    }
    ```

## 模块化文件详解 (File-by-File Breakdown)

---
### `src/request/api.js`

a. **文件用途说明**
统一管理所有与后端API的交互，封装请求和响应处理。

c. **函数/方法详解**

#### **`validateUser(email)` (新增)**
- **用途:** 调用后端接口校验邮箱并获取用户信息。
- **输入参数:** `email` (string): 待校验的邮箱。
- **输出数据结构:** `Promise<Object>`: resolve时返回 `{ name, department, open_id }`。
- **实现流程:**
  ```mermaid
  flowchart TD
      A[Start] --> B["发起 POST /order/validate-user 请求, body: {email}"];
      B --> C{检查 resp_common.ret === 0};
      C -- Yes --> D["返回 Promise.resolve(响应体数据)"];
      C -- No --> E["返回 Promise.reject(resp_common.msg)"];
  ```

#### **`createCommonOrder(data)` (改造)**
- **用途:** 提交通用工单，携带抄送人信息。
- **输入参数:** `data` (Object): 包含所有工单字段及 `cc_user_infos` 数组。
- **输出数据结构:** `Promise<Object>`: resolve时返回后端完整响应。
- **实现流程:**
  ```mermaid
  flowchart TD
      A[Start] --> B[发起 POST /order/common 请求, body: data];
      B --> C{检查 resp_common.ret === 0};
      C -- Yes --> D["返回 Promise.resolve(响应体)"];
      C -- No --> E["返回 Promise.reject(resp_common.msg)"];
  ```

#### **`getMyDoingOrder`, `getMyDoneOrder`, `getMyAuditOrder` (改造)**
- **用途:** 获取各类工单列表，支持按角色筛选。
- **输入参数:** `params` (Object): 包含分页信息和`filter_by_role`数组。
- **输出数据结构:** `Promise<Object>`: resolve时返回后端完整响应。
- **实现流程:**
  ```mermaid
  flowchart TD
      A[Start] --> B[发起 POST 请求, body: params];
      B --> C{检查 resp_common.ret === 0};
      C -- Yes --> D["返回 Promise.resolve(响应体)"];
      C -- No --> E["返回 Promise.reject(resp_common.msg)"];
  ```

---
### `src/components/CcInput/index.js` (新增)

a. **文件用途说明**
一个独立的、受控的抄送人输入组件。负责处理用户输入、校验、添加、删除抄送人的所有交互逻辑。

b. **组件图 (`classDiagram` 形式)**
```mermaid
classDiagram
    class CcInput {
        <<Component>>
        +value: CcUserInfo[]
        +onChange: function
        +currentUserEmail: string
        -inputValue: string
        -errorMsg: string | null
        -handleKeyDown(event)
        -handleRemove(email)
    }
    class CcUserInfo {
        <<Interface>>
        +open_id: string
        +email: string
        +display_text: string
    }
    CcInput "1" *-- "0..*" CcUserInfo : "manages"
```

c. **函数/方法详解**

#### **`CcInput(props)` (组件主体)**
- **用途:** 渲染输入框、错误提示、已选用户标签列表，并绑定事件。
- **输入参数(props):**
    - `value` (Array): 已选抄送人列表，格式为`[{ open_id, email, display_text }]`。
    - `onChange` (Function): 当列表变化时调用的回调函数，回传最新的列表。
    - `currentUserEmail` (string): 当前登录用户的邮箱，用于前端自校验。
- **输出数据结构:** JSX Element
- **实现流程:**
  ```mermaid
  flowchart TD
      A[Start] --> B(渲染Antd Input组件);
      B --> C(渲染错误提示区域, 根据errorMsg显示/隐藏);
      C --> D{遍历props.value};
      D -- 每个user --> E(渲染一个带关闭按钮的Antd Tag);
      E --> F(绑定Tag关闭按钮的onClick到handleRemove);
      D -- 遍历结束 --> G(渲染人数计数器);
      G --> H[End];
  ```

#### **`handleKeyDown(event)`**
- **用途:** 监听输入框的回车和空格事件，触发校验和添加流程。
- **输入参数:** `event` (KeyboardEvent)
- **输出数据结构:** 无
- **实现流程:**
  ```mermaid
  flowchart TD
      A[Start] --> B{event.key === 'Enter' or event.key === ' ' ?};
      B -- No --> Z[End];
      B -- Yes --> C["event.preventDefault()"];
      C --> D[获取输入值 email];
      D --> E{email === props.currentUserEmail ?};
      E -- Yes --> F[设置错误提示「不能抄送给自己」] --> Z;
      E -- No --> G{props.value中已存在该email ?};
      G -- Yes --> H[设置错误提示「该用户已被添加」] --> Z;
      G -- No --> I["调用 api.validateUser(email)"];
      I -- 成功 (userInfo) --> J[构造新用户对象];
      J --> K["调用 props.onChange([...props.value, newUser])"];
      K --> L[清空输入框和错误提示] --> Z;
      I -- 失败 (error) --> M[设置错误提示为error] --> Z;
  ```

#### **`handleRemove(emailToRemove)`**
- **用途:** 从已选列表中移除一个抄送人。
- **输入参数:** `emailToRemove` (string)
- **输出数据结构:** 无
- **实现流程:**
  ```mermaid
  flowchart TD
    A[Start] --> B[过滤 props.value 数组];
    B --> C[生成不含 emailToRemove 的新数组 newList];
    C --> D["调用 props.onChange(newList)"];
    D --> E[End];
  ```
---
### `src/components/RoleTag/index.js` (新增)

a. **文件用途说明**
一个纯展示性组件，根据传入的`role_type`显示对应的身份标签文本和颜色。

c. **函数/方法详解**

#### **`RoleTag(props)` (组件主体)**
- **用途:** 根据角色类型渲染一个带样式的标签。
- **输入参数(props):**
    - `roleType` (string): 角色类型枚举值，如`APPLICANT`, `CC_TO_ME`等。
- **输出数据结构:** JSX Element (Antd `Tag` or a styled `div`)
- **实现流程:**
  ```mermaid
  flowchart TD
      A[Start] --> B[定义一个映射对象 ROLE_MAP];
      subgraph ROLE_MAP
        C["APPLICANT: { text: '[我申请的]', color: 'default' }"]
        D["CC_TO_ME: { text: '[抄送我的]', color: 'default' }"]
        E["TO_BE_APPROVED: { text: '[待我审批]', color: 'orange' }"]
        F["ALREADY_APPROVED: { text: '[我已审批]', color: 'default' }"]
      end
      B --> G[从ROLE_MAP中查找props.roleType对应的配置];
      G --> H{找到了吗?};
      H -- Yes --> I[使用找到的text和color渲染标签];
      H -- No --> J[返回null或一个默认标签];
      I --> K[End];
      J --> K;
  ```

---
### `src/pages/commonOrder/index.js` (改造)

a. **文件用途说明**
通用工单创建页面，集成新的抄送人组件。

c. **函数/方法详解**

#### **`constructor` 或 `useState` (改造)**
- **用途:** 在组件状态中初始化一个用于存储抄送人信息的空数组。
- **实现要点:** 添加 `this.state = { ..., cc_user_infos: [] }` 或 `const [ccUserInfos, setCcUserInfos] = useState([])`。

#### **`handleCcChange(ccList)` (新增)**
- **用途:** 作为`CcInput`组件的`onChange`回调，更新父组件的state。
- **输入参数:** `ccList` (Array): `CcInput`组件回传的最新的抄送人列表，格式为 `[{ open_id, email, display_text }]`。
- **输出数据结构:** 无
- **实现流程:**
  ```mermaid
  flowchart TD
    A[Start] --> B["从 ccList 中提取 {cc_open_id, cc_email}"];
    B --> C[生成符合API要求的新数组 apiCcList];
    C --> D["调用 this.setState({ cc_user_infos: apiCcList })"];
    D --> E[End];
  ```
#### **`handleSubmit()` (改造)**
- **用途:** 在提交工单时，将`state`中的抄送人信息附加到请求体中。
- **实现流程:**
  ```mermaid
  sequenceDiagram
      participant H as handleSubmit
      participant S as this.state
      participant A as api.createCommonOrder
      
      H->>H: 执行现有表单校验
      H->>S: 读取 title, apply_msg, etc.
      H->>S: 读取 this.state.cc_user_infos
      H->>A: 调用 api.createCommonOrder({...formData, cc_user_infos})
      A-->>H: 返回Promise
      H->>H: 处理成功/失败逻辑 (跳转/提示)
  ```

#### **`render()` (改造)**
- **用途:** 在表单中渲染`CcInput`组件。
- **实现要点:**
  1. 在JSX中找到合适的位置（如“添加抄送”链接点击后显示的区域）。
  2. 渲染 `<CcInput />` 组件。
  3. 将`cc_user_infos`的展示部分（display_text）传给`CcInput`的`value` prop。
  4. 将`handleCcChange`传给`onChange` prop。
  5. 将当前登录用户的邮箱传给`currentUserEmail` prop。

---
### `src/pages/{doing,done,audit}Order/index.js` (统一改造)

a. **文件用途说明**
对所有工单列表页进行统一改造，以支持身份标签展示和按身份筛选。

c. **函数/方法详解**

#### **表格列定义 (`columns`) (改造)**
- **用途:** 修改“单号”列的定义，以集成身份标签和筛选功能。
- **实现要点:**
    1.  **渲染 (`render`方法):**
        -   在`render`函数中，除了返回原有的工单号，还在其上方或旁边渲染`<RoleTag roleType={record.role_type} />`。建议使用flex布局实现。
    2.  **筛选 (迭代三实现):**
        -   为该列添加 `filters` 和 `onFilter` 属性。
        -   `filters`: 值是一个`{text, value}`对象数组，根据当前页面的类型（`audit` vs `doing`/`done`）动态生成。例如: `[{ text: '[我申请的]', value: 'APPLICANT' }, { text: '[抄送我的]', value: 'CC_TO_ME' }]`。
        -   `onFilter`: 一个函数 `(value, record) => ...`。但由于筛选是在后端完成的，这里我们应该使用表格的受控筛选模式。即在`Table`组件上监听`onChange`事件，从其参数中获取筛选值，存入state，并重新调用API。

#### **列表数据请求方法 (e.g., `requestMyAuditPageOrder`) (改造)**
- **用途:** 在API请求中加入`filter_by_role`参数。
- **实现流程:**
  ```mermaid
  flowchart TD
      A[Start] --> B[从 this.state.searchParams 中读取 filter_by_role];
      B --> C[构造API请求参数, 将 filter_by_role 加入其中];
      C --> D[调用API];
      D --> E[End];
  ```
#### **表格`onChange`处理函数 (新增或改造)**
- **用途:** 处理表格的排序、分页、筛选变化。
- **输入参数:** `pagination`, `filters`, `sorter`
- **实现流程:**
  ```mermaid
  flowchart TD
    A[Start] --> B[从 filters 参数中提取「单号」列的筛选值 roleFilterValues];
    B --> C{"roleFilterValues 是否存在?"};
    C -- Yes --> D[调用 this.setState 更新 searchParams.filter_by_role];
    C -- No --> E["将 searchParams.filter_by_role 设为空数组[]"];
    D & E --> F[调用数据请求方法，如 requestMyAuditPageOrder];
    F --> G[End];
  ```

---
## 迭代演进依据
这份详细设计确保了未来的平滑演进：
1.  **组件化隔离风险**: `CcInput`作为一个独立的、高度内聚的组件，其内部复杂的交互逻辑被完全封装。未来即使需求变更，也仅需修改该组件，不会影响到使用它的任何页面，极大降低了维护成本和回归风险。
2.  **声明式UI**: `RoleTag`组件使得在任何地方添加身份标签都只需一行代码`<RoleTag roleType={...} />`，而无需关心其具体实现，符合React的声明式编程思想。
3.  **API层统一适配**: 所有与后端筛选和新字段相关的逻辑都首先在`api.js`层进行适配。UI层只需调用新的或改造后的函数，无需关心底层参数的具体构造，实现了前后端关注点的分离。
4.  **分步实现路径清晰**: 设计明确了哪些部分属于哪个迭代。迭代一时，列表页无需做任何改动。迭代二时，只需添加`RoleTag`的渲染。迭代三时，再添加表格的筛选属性和处理逻辑。每一步都是在上一部的基础上进行增量开发，路径清晰，易于管理。