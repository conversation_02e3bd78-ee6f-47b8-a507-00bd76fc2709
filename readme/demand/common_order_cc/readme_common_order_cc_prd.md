# 工单系统抄送功能需求规格说明 (最终版)

## 一、 总览

本文档定义了工单系统抄送功能的完整需求与分步实现计划。所有功能点已拆分为独立的、可验证的模块。后续开发以此文档为唯一依据。

---

## 功能一：创建工单时的抄送功能

### 1.1 目标
实现工单抄送功能的核心后端能力和完整的前端交互。用户可以在创建工单时，通过输入邮箱来校验、添加和管理抄送人。

### 1.2 前端需求 (UI & Interaction)

*   **UI布局**
    1.  **触发入口**：在表单区域，添加一个链接式按钮，文案为 `[添加抄送]`。
    2.  **配置区**：默认隐藏，点击按钮后展开。包含以下元素：
        *   **输入框**：`placeholder` 提示文案为 `请输入抄送人邮箱，按回车或空格确认`。
        *   **人数计数器**：位于输入框右侧，用灰色文字实时显示，格式为 `x/50`。
        *   **错误提示区域**：在输入框正下方，应有一个专门用于显示校验提示信息的区域。当无提示时，此区域不占用垂直空间；当显示提示时，此区域应平滑展开以容纳文字。
        *   **已选列表区**：位于错误提示区域下方，用于展示已成功添加的抄送人标签。

*   **核心交互逻辑**
    1.  **状态管理**：组件内部维护一个状态数组，用于存储已添加的抄送人信息。数组元素为对象格式：`{ open_id: '...', email: '...', display_text: '姓名 (部门)' }`。
    2.  **事件触发**：监听输入框的**回车键**或**空格键**，触发校验流程。
    3.  **前端自校验**：在调用后端接口前，按顺序执行以下检查：
        *   **检查是否为当前用户**：若输入邮箱与当前登录用户邮箱相同，触发错误状态。反馈：输入框触发一次短暂的抖动动画，并在下方的错误提示区域显示红色文字：“不能抄送给自己”。
        *   **检查是否已在列表中**：若输入邮箱已存在于已选列表中，触发错误状态。反馈：输入框触发一次短暂的抖动动画，并在下方的错误提示区域显示红色文字：“该用户已被添加”。
    4.  **错误状态处理**：
        *   当触发任一前端自校验错误时，不清空输入框内容，校验流程终止。
        *   错误提示信息显示3秒后，或在用户下一次输入时，自动平滑消失。
    5.  **后端校验调用**：若所有前端自校验通过，调用 `POST /order/validate-user` 接口。
        *   **处理成功 (`resp_common.ret === 0`)**：清空输入框，在“已选列表区”新增一个标签（标签文案为`姓名 (部门)`），并将返回的用户信息存入内部状态数组，更新计数器。若人数达到上限（50人），则禁用输入框。
        *   **处理失败 (`resp_common.ret !== 0`)**：输入框边框变红，并在错误提示区域显示 `resp_common.msg` 中的错误信息，3秒后自动消失。
    6.  **移除与提交**：
        *   每个标签自带的关闭按钮 `x` 可将对应人员从状态数组中移除。
        *   提交工单时，从内部状态数组中提取信息，构造成 `cc_user_infos` 字段要求的格式，随表单一同提交。

### 1.3 后端需求 (Data Model & API)

*   **数据表结构定义 (`tb_order_cc`)**
    *   **操作**：创建新表。
    *   **字段定义**:
        *   `id` (BIGINT, 主键, 自增)
        *   `order_id` (VARCHAR(16), 工单ID)
        *   `cc_open_id` (VARCHAR(64), 被抄送人飞书Open ID)
        *   `cc_email` (VARCHAR(32), 被抄送人邮箱)
        *   `ctime` (DATETIME, 创建时间)
        *   `mtime` (DATETIME, 更新时间)
    *   **索引**：为 `order_id` 和 `cc_open_id` 字段创建联合唯一索引 (UNIQUE KEY)，以优化查询并防止重复记录。

*   **新增API: 邮箱校验接口**
    *   **路径**：`/order/validate-user`
    *   **方法**：`POST`
    *   **请求体**：`{ "common": {...}, "email": "string" }`
    *   **成功响应**：返回 `{ "resp_common": {"ret": 0, ...}, "name": "...", "department": "...", "open_id": "..." }`
    *   **失败响应**：通过 `resp_common` 字段返回错误码和信息，如 `{"ret": 1001, "msg": "该邮箱不存在或非内部员工，请检查"}`。

*   **改造API: 创建工单接口**
    *   **请求体变更**：在现有请求体中增加一个新字段 `cc_user_infos` (类型: `Array<Object>`)。
    *   **`cc_user_infos` 结构**: `[{ "cc_open_id": "string", "cc_email": "string" }, ...]`
    *   **处理逻辑**：
        *   工单主信息创建成功后，遍历 `cc_user_infos` 数组，将记录写入 `tb_order_cc` 表。
        *   抄送关系写入失败不影响工单创建主流程，但需记录详细错误日志。

---

## 功能二：工单列表整合与权限控制

### 2.1 目标
改造所有工单列表，实现抄送工单的整合展示、按身份筛选，并确保抄送人访问详情页时为只读状态。

### 2.2 前端需求 (UI & Interaction)

*   **列表页UI变更：身份标识**
    *   **目标列表**：“进行中”、“已完结”、“我的审批”。
    *   **实现方式**：在“单号”列，根据记录的 `role_type` 字段，在单元格的左上角渲染一个带边框的文本标签。
    *   **标签映射关系**:
        *   `APPLICANT` -> `[我申请的]` (默认颜色)
        *   `CC_TO_ME` -> `[抄送我的]` (默认颜色)
        *   `TO_BE_APPROVED` -> `[待我审批]` (橙色)
        *   `ALREADY_APPROVED` -> `[我已审批]` (默认颜色)

*   **列表页UI变更：筛选功能**
    *   **实现方式**：采用 Ant Design Table 组件自带的列筛选功能。
    *   **触发入口**：在“单号”列的表头标题右侧，增加一个筛选图标。
    *   **交互流程**：
        1.  点击筛选图标，弹出支持多选的筛选菜单。
        2.  **菜单选项定义**:
            *   **“进行中”/“已完结”列表**：包含 `[我申请的]`、`[抄送我的]` 两个可勾选的选项。
            *   **“我的审批”列表**：包含 `[待我审批]`、`[我已审批]`、`[抄送我的]` 三个可勾选的选项。
        3.  菜单下方包含两个按钮：`[Reset]` 和 `[OK]`。
        4.  点击 `[Reset]` 按钮，清除筛选菜单中所有已勾选的选项。
        5.  点击 `[OK]` 按钮，筛选菜单消失，并立即调用列表API刷新数据。API请求中 **总是附带** `filter_by_role` 参数，其值为当前所有已勾选项对应的 `role_type` 字符串数组。若无任何勾选项，则传递一个空数组 `[]`。

*   **工单详情页权限处理**
    *   **实现逻辑**：从列表页进入详情页时，需将该工单的 `role_type` 传递过去。
    *   **权限判定**：此项需求旨在 **验证** 页面现有的权限控制逻辑（如 `当前用户是操作员`）对于 `role_type` 为 `CC_TO_ME` 的用户已能正确实现隐藏“审批按钮”和“审批人变更按钮”等操作入口。无需新增额外的权限控制逻辑。

### 2.3 后端需求 (API & Logic)

*   **改造API: “进行中”、“已完结”、“我的审批”工单列表接口**
    *   **逻辑变更**：查询逻辑需扩展，以包含被抄送给当前用户的工单。
    *   **返回体变更**：在返回的每条工单数据对象中，增加一个新字段 `role_type`。
        *   **字段类型**：`Enum` (String)
        *   **枚举值**：`TO_BE_APPROVED`, `ALREADY_APPROVED`, `APPLICANT`, `CC_TO_ME`。
        *   **赋值规则**：严格按此优先级顺序判断并赋值：`待审批` > `已审批` > `申请人` > `抄送人`。
    *   **参数变更**：接口需支持一个新的查询参数 `filter_by_role`。
        *   **参数类型**：`Array<string>`。
        *   **处理逻辑**：如果 `filter_by_role` 数组非空，则仅返回 `role_type` 存在于该数组中的工单（使用SQL `IN` 操作符）。如果数组为空 `[]` 或未提供该参数，则不应用此项筛选。

---

## 功能三：消息通知

### 3.1 目标
在工单创建成功后，向所有抄送人发送飞书通知，确保信息的及时触达。

### 3.2 后端需求 (Logic)

*   **触发时机**：在工单数据和 `tb_order_cc` 关系数据均已成功写入数据库之后。
*   **核心逻辑**：
    1.  遍历本次工单创建时提交的 `cc_user_infos` 数组。
    2.  对于数组中的每一个 `cc_open_id`，调用飞书消息服务，发送通知卡片。
    3.  **通知内容**：卡片内容与格式必须与发送给该工单申请人的消息卡片 **完全一致**。
    4.  **重要**：通知卡片 **不包含** 任何指向工单详情页的链接。
*   **容错处理**：
    *   单条通知发送失败需记录详细错误日志（包含 `order_id` 和失败的 `cc_open_id`）。
    *   单条通知的失败不得中断对后续用户的通知，也不得影响整个创建工单API的成功返回。

### 3.3 前端需求
无。