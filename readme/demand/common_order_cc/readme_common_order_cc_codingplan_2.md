# 编码计划迭代二

### **代码目录结构**

本次迭代将新增一个可复用组件，并改造所有工单列表页面。

```
wfo-frontend/
└── src/
    ├── components/
    │   ├── CcInput/            # (迭代一已完成)
    │   └── RoleTag/            # [新增] 身份标签展示组件
    │       ├── index.js
    │       └── style.css
    ├── pages/
    │   ├── auditOrder/
    │   │   └── index.js        # [改造] 添加身份标签和筛选器占位
    │   ├── doingOrder/
    │   │   └── index.js        # [改造] 添加身份标签和筛选器占位
    │   └── doneOrder/
    │       └── index.js        # [改造] 添加身份标签和筛选器占位
    └── request/
        └── api.js              # [改造] 适配列表API，支持新参数
```

### **受影响的现有模块说明**

1.  **`src/pages/{auditOrder,doingOrder,doneOrder}/index.js`**:
    *   **适配说明**: 这三个文件是本次改造的核心。根据对现有代码的分析，它们都使用了 Ant Design 的 `Table` 组件来展示列表。我们将统一改造表格的列（`columns`)定义。
    *   **扩展计划**:
        *   在“单号”列的 `render` 方法中，调用新的 `RoleTag` 组件来展示身份。
        *   为该列添加 `filters` 属性，以显示筛选菜单（迭代二只显示，功能在迭代三实现）。
        *   改造或新增表格的 `onChange` 事件处理器，为下一步的受控筛选做准备。

2.  **`src/request/api.js`**:
    *   **适配说明**: 后端列表接口已增加 `filter_by_role` 参数。我们需要确保前端的API调用函数能够传递此参数。
    *   **扩展计划**: 我们将检查并可能微调 `requestMyAuditOrderWithSearch`、`requestMyDoingOrder`、`requestMyDoneOrder` 等函数，使其接受一个包含所有查询条件的 `params` 对象，并将其作为请求体发送。

### **核心流程图**

下图展示了用户访问列表页时，新身份标签的渲染流程。

```mermaid
flowchart TD
    A[用户访问工单列表页] --> B[页面组件调用API获取工单数据];
    B --> C{"API成功返回数据(含role_type字段)"};
    C -- Yes --> D[页面遍历数据, 准备渲染Table];
    D -- "对每条工单(record)" --> E["在'单号'列的render方法中"];
    E --> F["创建 <RoleTag roleType={record.role_type} />"];
    F --> G[将标签和原单号一同渲染出来];
    G --> H[最终在页面显示带身份标签的列表];
    C -- No --> I[显示空或错误状态];
```

---

### **渐进式小步迭代式开发与集成步骤**

以下是为前端迭代二制定的5个小步迭代开发计划。

---

#### **第一步：创建独立的`RoleTag`组件**

*   **目标**: 创建一个独立的、纯展示用的`RoleTag`组件。这是本次所有UI改造的基础。
*   **操作文件**:
    *   `src/components/RoleTag/index.js` (创建)
    *   `src/components/RoleTag/style.css` (创建)
*   **具体编码说明**:
    1.  在 `src/components/` 下创建 `RoleTag` 目录及 `index.js`, `style.css` 文件。
    2.  在 `index.js` 中，创建一个函数式组件 `RoleTag`。
    3.  组件内部定义一个映射对象 `ROLE_CONFIG`，存储 `roleType` 到文本和颜色的映射关系，如详细设计所示。
    4.  组件接收 `roleType` 作为 prop，根据它从 `ROLE_CONFIG` 查找配置，并渲染一个对应颜色和文本的 Ant Design `Tag` 组件。
    5.  如果 `roleType` 无效或未提供，组件应返回 `null`，不渲染任何内容。
    6.  在 `style.css` 中添加一些微调样式，例如 `margin-bottom`，确保标签和单号之间有合适的间距。
*   **验证方法**:
    *   此步骤完成后，应用仍可正常运行。
    *   （可选）可以在任意页面（如`commonOrder`）临时引入并渲染 `<RoleTag roleType="TO_BE_APPROVED" />` 和 `<RoleTag roleType="CC_TO_ME" />`，验证不同类型的标签是否能正确显示预期的文本和颜色。

---

#### **第二步：在“我的审批”列表页集成身份标签**

*   **目标**: 在最复杂的 `auditOrder` 页面上率先集成`RoleTag`，验证其在真实表格环境中的展示效果。
*   **操作文件**:
    *   `src/pages/auditOrder/index.js` (改造)
*   **具体编码说明**:
    1.  在文件顶部 `import RoleTag from '../../components/RoleTag';`。
    2.  找到定义表格列的 `columns` 数组。
    3.  定位到“单号”列（通常是 `dataIndex: 'order_id'` 或 `title: '单号'` 的那一项）。
    4.  修改该列的 `render` 方法。原 `render` 方法可能只返回 `text` 或一个链接。将其修改为返回一个 `div` 或 `React.Fragment`，其中包含 `RoleTag` 和原来的内容。
        ```jsx
        // columns 定义中的 "单号" 列
        {
          title: '单号',
          dataIndex: 'order_id',
          // ... 其他属性
          render: (text, record) => (
            <div>
              <RoleTag roleType={record.role_type} />
              {/* 保留原来的链接或文本 */}
              <a onClick={() => this.showDetailDrawer(record.order_id)}>{text}</a>
            </div>
          ),
        }
        ```
*   **验证方法**:
    *   启动应用，进入“我的审批”页面。
    *   此时，每个工单的单号上方或旁边应该会出现身份标签，例如 `[待我审批]` 或 `[我已审批]`。
    *   页面其他功能（搜索、分页）应不受影响。

---

#### **第三步：在“进行中”和“已完结”列表页同步集成**

*   **目标**: 将已验证的集成模式快速应用到其余两个列表页。
*   **操作文件**:
    *   `src/pages/doingOrder/index.js` (改造)
    *   `src/pages/doneOrder/index.js` (改造)
*   **具体编码说明**:
    1.  对 `doingOrder/index.js` 和 `doneOrder/index.js` 重复**第二步**的全部操作。
    2.  即：引入 `RoleTag` 组件，并修改 `columns` 数组中“单号”列的 `render` 方法。
*   **验证方法**:
    *   分别进入“进行中的工单”和“已完结的工单”页面。
    *   确认每个工单的单号旁边都正确显示了身份标签，如 `[我申请的]` 或 `[抄送我的]`。

---

#### **第四步：为列表页添加“按身份筛选”的UI占位**

*   **目标**: 为迭代三的筛选功能搭建UI框架。在本次迭代中，用户将能看到筛选图标和菜单，但选择筛选条件不会产生实际效果。
*   **操作文件**:
    *   `src/pages/auditOrder/index.js` (改造)
    *   `src/pages/doingOrder/index.js` (改造)
    *   `src/pages/doneOrder/index.js` (改造)
*   **具体编码说明**:
    1.  **对 `auditOrder/index.js`**:
        *   在“单号”列的 `columns` 定义中，添加 `filters` 属性。其值应为：
            ```javascript
            filters: [
              { text: '[待我审批]', value: 'TO_BE_APPROVED' },
              { text: '[我已审批]', value: 'ALREADY_APPROVED' },
              { text: '[抄送我的]', value: 'CC_TO_ME' },
            ],
            ```
    2.  **对 `doingOrder/index.js` 和 `doneOrder/index.js`**:
        *   在“单号”列的 `columns` 定义中，添加 `filters` 属性。其值应为：
            ```javascript
            filters: [
              { text: '[我申请的]', value: 'APPLICANT' },
              { text: '[抄送我的]', value: 'CC_TO_ME' },
            ],
            ```
*   **验证方法**:
    *   分别进入三个列表页。
    *   “单号”列的表头右侧应出现一个筛选（漏斗）图标。
    *   点击图标，应能弹出包含正确选项（支持多选）的筛选菜单。
    *   此时勾选任何选项并点击“OK”，表格数据**不应**发生变化。

---

#### **第五步：改造数据请求流程，为受控筛选做准备**

*   **目标**: 改造列表页的状态管理和API调用逻辑，使其能够处理 `filter_by_role` 参数，为迭代三的实际筛选功能打下坚实基础。
*   **操作文件**:
    *   `src/pages/auditOrder/index.js` (改造)
    *   `src/pages/doingOrder/index.js` (改造)
    *   `src/pages/doneOrder/index.js` (改造)
    *   `src/request/api.js` (可能需要改造)
*   **具体编码说明**:
    1.  **在 `request/api.js` 中**:
        *   检查 `requestMyAuditOrderWithSearch`, `requestMyDoingOrder`, `requestMyDoneOrder` 函数。确保它们能接受一个 `params` 对象，并将此对象完整地作为请求体发送。如果当前是写死的参数，需改造为接收一个统一的 `params` 对象。
    2.  **对每个列表页 (`auditOrder` 等)**:
        *   **State**: 在组件的 `state` 中（对于`auditOrder`，是在 `state.searchParams` 中）添加一个新字段：`filter_by_role: []`。
        *   **API调用**: 在发起数据请求的方法（如 `requestMyAuditPageOrder`）中，确保将 `state` 中的 `filter_by_role` 连同其他参数（分页、搜索条件）一同传递给API调用函数。
        *   **onChange处理**: 找到或创建 `Table` 组件的 `onChange` 属性。该函数会接收 `(pagination, filters, sorter)`。
            ```jsx
            // 以 auditOrder 为例，改造 Table 组件的 onChange
            <Table
              // ...其他props
              onChange={(pagination, filters, sorter) => {
                // 迭代三将在这里写真实逻辑
                // const roleFilters = filters.order_id || [];
                // this.setState({ searchParams: { ...this.state.searchParams, filter_by_role: roleFilters } }, () => {
                //   this.requestMyAuditPageOrder();
                // });

                // 迭代二，我们只处理已有的分页逻辑
                this.handlePageChange(pagination.current);
              }}
            />
            ```
            **关键**：在本次迭代中，我们只预留出逻辑位置，或将 `filters` 参数打印出来观察，但**不**实现 `setState` 和重新请求的逻辑。
*   **验证方法**:
    *   分别进入三个列表页。
    *   打开浏览器Network面板。
    *   进行分页、搜索等操作，观察对应的API请求。在请求的Payload中，应该能看到新增的 `filter_by_role: []` 参数被正确传递给了后端。
    *   点击筛选菜单并选择选项，此时由于`onChange`未实现筛选逻辑，不应发出新的API请求。整个应用功能保持稳定。

完成以上五个步骤，迭代二的功能便已完整且稳定地实现，同时也为迭代三的顺利开发铺平了道路。