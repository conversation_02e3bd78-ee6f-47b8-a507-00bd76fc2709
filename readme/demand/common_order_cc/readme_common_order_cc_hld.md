# 前端概要设计：工单抄送功能 (最终汇总版)

## 架构概览

本次功能迭代遵循现有架构，引入可复用的UI组件并在请求层封装API调用。设计核心是**精确响应后端API的行为模式**：前端在提交工单后，将预期一个**快速的成功响应**（因为后端的核心数据写入是同步的），而无需等待耗时的通知发送（因为后端已将其设计为异步后台任务）。

核心的“创建工单”交互流程如下，体现了快速的API响应：

```mermaid
sequenceDiagram
    participant User as 用户
    participant CommonOrderForm as "工单创建页"
    participant ApiJs as "请求层(api.js)"
    participant BackendAPI as "后端API"

    User->>CommonOrderForm: 点击“提交”
    activate CommonOrderForm
    CommonOrderForm->>ApiJs: createCommonOrder(工单数据, 抄送人列表)
    activate ApiJs
    ApiJs->>BackendAPI: POST /order/common
    
    note right of BackendAPI: 后端同步执行<br/>1. 写入 tb_order<br/>2. 写入 tb_order_cc
    BackendAPI-->>ApiJs: 返回成功响应 (工单创建成功)
    note right of BackendAPI: 后端异步启动<br/>审批流创建和通知发送
    
    ApiJs-->>CommonOrderForm: 返回成功结果
    deactivate ApiJs
    CommonOrderForm->>User: 提示“提交成功”并跳转页面
    deactivate CommonOrderForm
```

## 组件拆分(Components)

组件的职责和划分保持不变，因为它们的设计本身就是内聚和独立的。

*   **`components/CcInput` (新增):** 核心的可复用抄送人输入组件。
*   **`components/RoleTag` (新增):** 纯展示性的身份标签组件。
*   **`pages/commonOrder/index.js` (改造):** 引入并使用 `CcInput` 组件，并在表单提交时发送数据。
*   **`pages/doingOrder/`, `pages/doneOrder/`, `auditOrder/` (改造):** 在各自组件内实现身份标签和筛选功能。
*   **`request/api.js` (改造):** 封装对新增和改造后API的调用。

## 目录结构树(Directory Tree)

前端目录结构保持不变。所有变更都发生在现有文件或新增的组件目录内。

```text
wfo-frontend/
└── src/
    ├── components/
    │   ├── CcInput/            # (新增)
    │   │   ├── index.js
    │   │   └── style.css
    │   └── RoleTag/            # (新增)
    │       └── index.js
    ├── pages/
    │   ├── auditOrder/
    │   │   ├── index.js        # (改造)
    │   │   └── OrderTable.js   # (已有, 改造)
    │   ├── commonOrder/
    │   │   └── index.js        # (改造)
    │   ├── doingOrder/
    │   │   └── index.js        # (改造)
    │   └── doneOrder/
    │       └── index.js        # (改造)
    └── request/
        └── api.js              # (改造)
```

## 数据流(Data Flow)

**场景: 用户在前端界面添加2名抄送人，然后提交整个通用工单。**

此流程图已被重构，以清晰地展示客户端与后端之间的多次独立交互，以及对多名抄送人的处理。

```mermaid
sequenceDiagram
    participant Client as "客户端(Browser)"
    participant Handler as "后端Handler"
    participant Service as "后端Service"

    note over Client: 用户输入第一个抄送人邮箱并确认
    Client->>Handler: POST /order/validate-user (ValidateUserReq)
    activate Handler
    Handler->>Service: ValidateUserAndBuildInfo('<EMAIL>')
    Service-->>Handler: 返回组装好的 User1 完整信息
    Handler-->>Client: HTTP 200 OK (ValidateUserResp)
    deactivate Handler
    
    note over Client: 用户输入第二个抄送人邮箱并确认
    Client->>Handler: POST /order/validate-user (ValidateUserReq)
    activate Handler
    Handler->>Service: ValidateUserAndBuildInfo('<EMAIL>')
    Service-->>Handler: 返回组装好的 User2 完整信息
    Handler-->>Client: HTTP 200 OK (ValidateUserResp)
    deactivate Handler

    note over Client: 用户填写完所有表单信息, 点击“提交”
    Client->>Handler: POST /order/common (CommonOrderReq)
    activate Handler
    note over Handler: 后端开始处理同步创建流程
    Handler->>Service: (同步)调用 NewCreateOrder, CreateCcRecords 等
    Service-->>Handler: 返回 err
    
    alt err 不为 nil
        Handler-->>Client: 返回失败响应 (e.g., HTTP 500)
    else err 为 nil
        Handler-->>Client: HTTP 200 OK (body: 工单创建成功)
    end
    
    note over Handler: 后台异步启动 NewCreateStage
    deactivate Handler
```

## 数据模型设计(Data Model Design)

前端组件的状态数据模型保持不变。

*   **`CcInput` 组件内部 State:**
    ```javascript
    {
      displayList: [{ open_id: '...', email: '...', display_text: '姓名 (部门)' }, ...],
      inputValue: '...',
      error: '不能抄送给自己' | null
    }
    ```
*   **`CommonOrderForm` 页面 State:**
    ```javascript
    {
      title: '...',
      apply_msg: '...',
      cc_user_infos: [{ cc_open_id: '...', cc_email: '...' }, ...]
    }
    ```

## API接口定义

前端将与以下后端API端点交互，其请求/响应体结构遵循后端`Protobuf`定义。

1.  **邮箱校验接口 (新增)**
    *   `POST /order/validate-user`
    *   **说明:** 前端 `CcInput` 组件调用此接口来逐个校验用户。请求体将是一个符合后端 `ValidateUserReq` 结构的JSON对象。

2.  **通用工单创建接口 (改造)**
    *   `POST /order/common`
    *   **说明:** 前端 `CommonOrderForm` 在提交时调用。前端将预期一个快速的成功或失败响应，并根据此响应立即给用户反馈（如跳转页面或显示错误信息）。

3.  **各工单列表接口 (改造)**
    *   `POST /GetMyDoingOrder`
    *   `POST /GetMyDoneOrder`
    *   `POST /GetMyAuditOrder`
    *   **说明:** 前端列表页调用。请求体中将包含 `filter_by_role` 参数，其结构遵循后端 `GetMyOrdersParam` 的定义。

## 迭代演进依据

这份设计方案严格遵循了“简约至上”和“迭代演进”的原则，其优势在于：

1.  **高内聚、低耦合的可复用组件 (`CcInput`):**
    *   **现状:** 抄送功能被封装在一个独立的受控组件中，实现了“一次开发，处处复用”。
    *   **演进:** 未来可轻松集成到任何其他表单中。

2.  **职责分离的API层 (`request/api.js`):**
    *   **现状:** 所有API交互都集中在此，UI组件不关心请求细节。
    *   **演进:** 未来API的任何变更都只需修改此文件，降低了维护成本。

3.  **务实的、最小化侵入的改造:**
    *   **现状:** 我们选择直接在各自列表页组件内部进行改造，这最符合现有代码结构，风险最低，开发速度最快。
    *   **演进:** 保留了未来的优化空间。如果需要，未来可以再为相似度高的`doingOrder`和`doneOrder`页面提取通用逻辑。

4.  **优化的用户体验:**
    *   **现状:** 前端设计完全匹配了后端**同步创建+异步通知**的模式。
    *   **演进:** 这意味着用户在点击提交后几乎可以立即得到“成功”的反馈，无需在界面上长时间等待后台所有任务（如发送通知）完成。这是一种现代Web应用中常见的、用于提升用户感知性能的优秀实践。