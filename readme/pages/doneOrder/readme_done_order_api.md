# /Users/<USER>/soft/develop/GoProject/wfo-frontend/src/pages/doneOrder/index.js

## 类/方法/函数详解

### DoneOrder
#### state
- 用途: 定义和管理 `DoneOrder` 组件的状态。
- 逐一说明状态属性:
  - `dataSource`: 存储已完成工单数据的数组。
  - `pageSize`: 每页显示的工单数量。
  - `page`: 当前页码。
  - `total`: 工单总数。
  - `filter_by_role`: 存储按角色筛选的参数。
- 实现要点:
  - 初始化了组件在整个生命周期中需要用到的所有状态属性，并为它们设置了合理的默认值。

#### getColumns
- 用途: 获取并返回 antd `Table` 组件所需的列配置数组。
- 逐一说明输入参数: 无。
- 输出: 一个包含列配置对象的数组。
- 实现要点:
  - **单号列**: 
    - 使用 `RoleTagCell` 组件根据 `role_type` 来渲染带有特殊标记（如“[申请]”或“[抄送]”）的单号。
    - 定义了“申请”和“抄送”的筛选选项。
    - `filteredValue` 绑定到 `this.state.filter_by_role`，以控制筛选状态。
  - **详情列**: 
    - 使用 `render` 方法渲染一个 `OrderDetailDrawer` 组件，用于点击查看工单详情。
  - **其他列**: 定义了工单类型、节点数、当前节点、状态、申请人、运维负责人和申请日期等列的显示方式。
  - **排序**: 为“申请日期”列添加了 `sorter` 属性，但实现 `a.age - b.age` 似乎是一个笔误，因为数据中没有 `age` 字段。

#### getRoleType
- 用途: 根据 `is_cc` 字段的值判断当前用户在工单中的角色。
- 逐一说明输入参数:
  - `is_cc`: 一个数字，1 表示抄送，其他值表示申请人。
- 输出: 角色类型字符串，`'CC_TO_ME'` 或 `'APPLICANT'`。
- 实现要点:
  - 使用三元运算符判断 `is_cc` 是否等于 1，并返回相应的字符串。

#### componentDidMount
- 用途: 在组件挂载后立即请求第一页的工单数据。
- 逐一说明输入参数: 无。
- 输出: 无。
- 实现要点:
  - 调用 `this.requestMyDonePageOrder()` 方法来获取数据。

#### requestMyDonePageOrder
- 用途: 异步请求“已完成”的工单数据，并支持分页和角色筛选。
- 逐一说明输入参数: 无（从 `this.state` 中获取分页和筛选参数）。
- 输出: 无（异步更新组件的 `state`）。
- 实现要点:
  - 从 `state` 中获取 `page`, `pageSize`, 和 `filter_by_role`。
  - 调用 `requestMyDoneOrder` API，并传递分页和角色筛选参数。
  - **数据处理**: 
    - 在 `.then` 回调中处理返回的数据。
    - 使用 `map` 方法遍历返回的 `orders` 数组，将其转换为 `dataSource` 需要的格式。
    - 在转换过程中，调用 `getRoleType` 来确定每个工单的角色类型。
  - **状态更新**: 调用 `setState` 更新 `dataSource` 和 `total`。

#### handlePageChange
- 用途: 处理分页组件的页码变化事件。
- 逐一说明输入参数:
  - `page`: 新的页码。
- 输出: 无。
- 实现要点:
  - 使用 `setState` 更新 `page`。
  - 在 `setState` 的回调函数中调用 `this.requestMyDonePageOrder()` 来请求新页码的数据。

#### handlePageSizeChange
- 用途: 处理分页组件的每页显示数量变化事件。
- 逐一说明输入参数:
  - `current`: 当前页码（未使用）。
  - `size`: 新的每页显示数量。
- 输出: 无。
- 实现要点:
  - 使用 `setState` 将 `page` 重置为 1，并更新 `pageSize`。
  - 在 `setState` 的回调函数中调用 `this.requestMyDonePageOrder()` 来请求数据。

#### handleTableChange
- 用途: 处理 antd `Table` 的变化事件，主要用于处理角色筛选。
- 逐一说明输入参数:
  - `pagination`: 分页信息（未使用）。
  - `filters`: 包含当前筛选值的对象，其中 `filters.orderID` 对应角色筛选。
  - `sorter`: 排序信息（未使用）。
- 输出: 无。
- 实现要点:
  - 从 `filters` 对象中提取角色筛选的值（`orderID` 字段似乎是一个笔误，应与 `getColumns` 中的 `dataIndex` 对应）。
  - 使用 `setState` 更新 `filter_by_role` 状态，并将 `page` 重置为 1。
  - 在 `setState` 的回调中调用 `requestMyDonePageOrder` 来根据新的筛选条件请求数据。

#### render
- 用途: 渲染 `DoneOrder` 组件的 UI。
- 逐一说明输入参数: 无。
- 输出: React 元素。
- 实现要点:
  - 从 `this.state` 中解构出 `dataSource`, `total`, `page`, 和 `pageSize`。
  - 渲染一个自定义样式的 `DoneTable` (antd `Table`) 组件，并传入 `dataSource` 和通过 `getColumns()` 获取的列配置。
  - 将 `pagination` 设置为 `false`，因为使用了自定义的分页组件。
  - 绑定 `handleTableChange` 到 `Table` 的 `onChange` 事件。
  - 渲染 `OrderPagination` 组件，并传递 `total`, `current`, `pageSize` 等 props，以及分页变化的回调函数。

## 文件用途
1. 代码用途概述
   - 该文件实现了一个 React 类组件 `DoneOrder`，用于展示当前用户“已完成”的工单列表。这包括用户自己申请的工单和抄送给用户的工单。组件提供了分页、按角色（申请/抄送）筛选以及查看工单详情的功能。

2. 使用场景和流程
   - **使用场景**: 用户在系统中导航到“已完成的工单”页面时，会加载此组件以查看所有已经关闭或完成的相关工单。
   - **流程**:
     1. 组件挂载后，自动调用 API 请求第一页的已完成工单数据。
     2. API 返回数据后，组件更新状态，并在表格中渲染工单列表。
     3. 用户可以通过点击表头的筛选按钮，选择只看“申请”的工单或“抄送”的工单，或者两者都看。
     4. 用户的筛选操作会触发一次新的 API 请求，以获取符合筛选条件的数据并刷新表格。
     5. 用户可以使用底部的分页组件来浏览不同页面的工单。
     6. 用户可以点击每一行末尾的“详情”链接，这会弹出一个抽屉（`OrderDetailDrawer`）来展示该工单的完整信息。

## 文件内类图
```mermaid
classDiagram
    class DoneOrder {
        +state
        +getColumns()
        +getRoleType(is_cc)
        +componentDidMount()
        +requestMyDonePageOrder()
        +handlePageChange(page)
        +handlePageSizeChange(current, size)
        +handleTableChange(pagination, filters, sorter)
        +render()
    }
    class Component
    DoneOrder --|> Component
```

## 调用时序图
```mermaid
sequenceDiagram
    participant User
    participant DoneOrder
    participant API

    User->>DoneOrder: 访问页面
    activate DoneOrder
    DoneOrder->>API: componentDidMount() -> requestMyDonePageOrder()
    activate API
    API-->>DoneOrder: 返回工单数据
    deactivate API
    DoneOrder-->>User: 渲染工单列表
    deactivate DoneOrder

    User->>DoneOrder: 筛选角色 (申请/抄送)
    activate DoneOrder
    DoneOrder->>DoneOrder: handleTableChange() - 更新筛选状态
    DoneOrder->>API: requestMyDonePageOrder()
    activate API
    API-->>DoneOrder: 返回筛选后的数据
    deactivate API
    DoneOrder-->>User: 渲染更新后的列表
    deactivate DoneOrder

    User->>DoneOrder: 切换分页
    activate DoneOrder
    DoneOrder->>DoneOrder: handlePageChange() / handlePageSizeChange()
    DoneOrder->>API: requestMyDonePageOrder()
    activate API
    API-->>DoneOrder: 返回新页面的数据
    deactivate API
    DoneOrder-->>User: 渲染新页面的列表
    deactivate DoneOrder
```

