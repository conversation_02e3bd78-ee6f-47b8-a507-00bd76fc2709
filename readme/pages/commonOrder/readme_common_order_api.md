# /Users/<USER>/soft/develop/GoProject/wfo-frontend/src/pages/commonOrder/index.js

## 类/方法/函数详解

### CommonOrderForm
#### constructor
- 用途: 初始化 `CommonOrderForm` 组件，并预加载部门领导和二级领导的邮件数据。
- 逐一说明输入参数:
  - `props`: React 组件的属性。
- 输出: 无。
- 实现要点:
  - 调用父类的构造函数 `super(props)`。
  - 调用 `requestLeaderEmail` 和 `requestSuperLeaderEmails` API 预加载数据，并将返回的 Promise 存储在 `this.leaderPromise` 和 `this.superLeaderPromise` 中，以便在 `componentDidMount` 中使用，从而优化加载性能。

#### state
- 用途: 定义和管理 `CommonOrderForm` 组件的状态。
- 逐一说明状态属性:
  - `show_pointed_approver`: 控制是否显示“指定审批人”输入框的布尔值。
  - `pointed_approver`: 存储指定审批人邮箱的字符串。
  - `order_type`: 当前选择的工单类型。
  - `title`: 工单标题。
  - `apply_msg`: 申请信息。
  - `ops_audit_email`: 运维审批人邮件列表。
  - `selected_ops_audit_email`: 当前选择的运维审批人。
  - `leader_emails`: 领导的邮件地址。
  - `dutySpectrumMemberMap`: 职责范围与成员的映射关系。
  - `file_infos`: 已上传文件的信息列表。
  - `showCcInput`: 控制是否显示抄送人输入框的布尔值。
  - `ccList`: 抄送人列表。
- 实现要点:
  - 初始化了表单所需的各种状态，包括UI控制、用户输入和从API获取的数据。

#### componentDidMount
- 用途: 在组件挂载后，获取并设置表单所需的初始数据。
- 逐一说明输入参数: 无。
- 输出: 无。
- 实现要点:
  - 使用在 `constructor` 中预加载的 `this.leaderPromise` 来设置部门领导的邮件。
  - 并行发起 `requestOpsMember` 和 `requestOpsMemberDutySpectrum` 请求，以获取运维成员和职责范围数据。
  - 使用 `Promise.all` 来等待所有数据加载完成，并在所有 Promise 都解决后更新组件状态。
  - 包含一个 `catch` 块来处理在数据加载过程中可能发生的任何错误。

#### componentWillUnmount
- 用途: 在组件即将卸载时，重写 `setState` 方法以防止在已卸载的组件上调用它，从而避免潜在的内存泄漏和错误。
- 逐一说明输入参数: 无。
- 输出: 无。
- 实现要点:
  - 将 `this.setState` 替换为一个空函数，这样任何在组件卸载后尝试更新状态的异步操作都将静默失败，而不会引发 React 警告。

#### handleTitleChange
- 用途: 处理标题输入框的变化事件。
- 逐一说明输入参数:
  - `e`: 事件对象，其中 `e.target.value` 包含输入框的当前值。
- 输出: 无。
- 实现要点:
  - 调用 `setState` 更新 `title` 状态。

#### handleApplyMsgChange
- 用途: 处理申请信息文本域的变化事件。
- 逐一说明输入参数:
  - `e`: 事件对象，其中 `e.target.value` 包含文本域的当前值。
- 输出: 无。
- 实现要点:
  - 调用 `setState` 更新 `apply_msg` 状态。

#### handleAuditEmailChange
- 用途: 处理运维审批人下拉框的变化事件。
- 逐一说明输入参数:
  - `value`: 下拉框中选中的值。
- 输出: 无。
- 实现要点:
  - 调用 `setState` 更新 `selected_ops_audit_email` 状态。

#### handleDutyChange
- 用途: 处理业务类型下拉框的变化事件，并根据所选类型自动填充申请信息和分配运维审批人。
- 逐一说明输入参数:
  - `value`: 下拉框中选中的业务类型。
- 输出: 无。
- 实现要点:
  - 如果选择的业务类型是“新系统上线资源申请”，则自动填充 `apply_msg` 为预设的模板文本。
  - 从 `dutySpectrumMemberMap` 中获取该业务类型对应的审批人列表。
  - 使用 `Math.random()` 从列表中随机选择一个审批人，并更新 `selected_ops_audit_email` 状态。

#### handleFileUpload
- 用途: 处理文件上传，包括计算文件 MD5、获取腾讯云 COS 临时凭证以及将文件上传到 COS。
- 逐一说明输入参数:
  - `options`: antd `Upload` 组件的 `customRequest` 提供的对象，包含 `file`, `onProgress`, `onSuccess`, `onError` 等。
- 输出: 无。
- 实现要点:
  - **文件处理**: 使用 `getFileContent` 读取文件内容，然后用 `CryptoJS` 计算文件的 MD5 值。
  - **路径生成**: 根据当前日期和文件 MD5、文件名生成一个在 COS 中的唯一存储路径。
  - **获取凭证**: 调用 `requestTxCamCosAuth` API 获取用于上传到腾讯云 COS 的临时安全凭证（SecretId, SecretKey, SecurityToken）。
  - **执行上传**: 
    - 初始化 `cos-js-sdk-v5` 实例。
    - 调用 `cos.putObject` 方法执行上传。
    - 在上传过程中，通过 `onProgress` 回调更新上传进度。
    - 上传成功后，调用 `options.onSuccess`，并将文件的路径和名称存入 `file_infos` 状态。
    - 上传失败后，调用 `options.onError`。

#### handleCcChange
- 用途: 处理抄送人列表的变化。
- 逐一说明输入参数:
  - `newList`: 最新的抄送人列表。
- 输出: 无。
- 实现要点:
  - 调用 `setState` 更新 `ccList` 状态。

#### resetHandle
- 用途: 重置表单到初始状态。
- 逐一说明输入参数: 无。
- 输出: 无。
- 实现要点:
  - 调用 `setState` 将 `order_type`, `title`, `apply_msg`, `selected_ops_audit_email`, `pointed_approver`, `ccList`, 和 `showCcInput` 等状态重置为它们的默认值。

#### handleSubmit
- 用途: 处理表单提交，包括数据验证、参数构造和 API 调用。
- 逐一说明输入参数: 无。
- 输出: 无。
- 实现要点:
  - **数据验证**:
    - 验证 `title` 是否为空。
    - 验证 `apply_msg` 的长度是否大于 5。
    - 如果选择了“指定审批人”，则验证 `pointed_approver` 是否为空。
  - **数据清理**:
    - 从 `apply_msg` 中移除特定的模板文本。
    - 检查并移除 `apply_msg` 中的非法字符（`'`, `"`, `	`），并给出提示。
  - **参数构造**:
    - 创建 `apply_info` 对象，包含标题和运维审批人信息。
    - 如果是“指定审批人”模式，则将审批人邮箱添加到 `apply_info`。
    - 将抄送人列表 `ccList` 转换为 `cc_user_infos` 格式。
    - 构造最终的 API 请求参数 `args`，包含 `apply_info`, `order_type`, `title`, `apply_msg`, `attachment_info`, 和 `cc_user_infos`。
  - **API 调用**:
    - 调用 `requestCommonOrder` API 提交工单。
    - 提交成功后，调用 `navigateModal` 函数，在 3 秒后跳转到“进行中的工单”页面。

#### handleOrderTypeChange
- 用途: 处理审批模式（工单类型）的变化，并根据所选模式更新 UI 和审批人信息。
- 逐一说明输入参数:
  - `value`: 所选的工单类型值。
- 输出: 无。
- 实现要点:
  - 首先清空 `pointed_approver` 状态。
  - **UI 控制**: 根据 `value` 是否为 `common_pointed_approver` 来设置 `show_pointed_approver` 状态，以决定是否显示“指定审批人”输入框。
  - **审批人更新**:
    - 如果选择的不是“指定审批人”，则根据不同的 `value`（`common`, `common_super_leader`, `common_three_level_leader`）使用预加载的 `leaderPromise` 或 `superLeaderPromise` 来获取并设置相应的领导邮箱列表（`leader_emails`）。
    - 对于“三级领导”，在二级领导的基础上拼接了额外的邮箱地址。
  - **状态更新**: 最后，调用 `setState` 一次性更新 `order_type` 和 `show_pointed_approver` 状态。

#### render
- 用途: 渲染 `CommonOrderForm` 组件的 UI。
- 逐一说明输入参数: 无。
- 输出: React 元素。
- 实现要点:
  - 使用自定义的 styled-components（如 `SideContent`, `CommonForm`, `OrderRow` 等）来布局和美化表单。
  - 渲染业务类型和运维审批人的下拉选择框。
  - 渲染审批模式的 `Segmented` 组件。
  - 根据 `show_pointed_approver` 状态，条件性地显示领导邮箱列表（`Tag`）或指定审批人输入框（`Input`）。
  - 渲染标题、需求描述、附件上传和抄送功能的 UI 组件。
  - 渲染“提交”和“重置”按钮，并绑定相应的处理函数。

## 文件用途
1. 代码用途概述
   - 该文件实现了一个用于创建“通用运维工单”的 React 表单组件。它允许用户填写工单的详细信息，如业务类型、审批模式、标题、需求描述，并支持附件上传和添加抄送人。组件还集成了与后端 API 的交互，用于获取审批人信息、上传文件和最终提交工单。

2. 使用场景和流程
   - **使用场景**: 当用户需要发起一个通用的运维请求时，会使用此页面来创建工单。
   - **流程**:
     1. 用户访问页面，组件加载并自动获取必要的初始数据（如审批人列表）。
     2. 用户选择业务类型，系统可能会自动分配一个运维审批人。
     3. 用户选择审批模式（如部门领导、两级领导、指定审批人等）。
     4. 根据所选模式，系统会显示相应的审批人或提供输入框让用户指定。
     5. 用户填写工单标题和详细的需求描述。
     6. （可选）用户可以上传相关附件。
     7. （可选）用户可以添加抄送人。
     8. 用户点击“提交”按钮，表单进行数据验证，然后将工单信息发送到后端 API。
     9. 提交成功后，页面会提示用户并自动跳转到“进行中的工单”列表。
     10. 用户可以随时点击“重置”按钮清空所有输入。

## 文件内类图
```mermaid
classDiagram
    class CommonOrderForm {
        +constructor(props)
        +state
        +componentDidMount()
        +componentWillUnmount()
        +handleTitleChange(e)
        +handleApplyMsgChange(e)
        +handleAuditEmailChange(value)
        +handleDutyChange(value)
        +handleFileUpload(options)
        +handleCcChange(newList)
        +resetHandle()
        +handleSubmit()
        +handleOrderTypeChange(value)
        +render()
    }
    class Component
    CommonOrderForm --|> Component
```

## 调用时序图
```mermaid
sequenceDiagram
    participant User
    participant CommonOrderForm
    participant API
    participant COS

    User->>CommonOrderForm: 访问页面
    activate CommonOrderForm
    CommonOrderForm->>API: constructor() - 预加载领导邮件
    CommonOrderForm->>API: componentDidMount() - 获取运维/职责数据
    activate API
    API-->>CommonOrderForm: 返回数据
    deactivate API
    CommonOrderForm-->>User: 渲染表单
    deactivate CommonOrderForm

    User->>CommonOrderForm: 填写表单信息

    User->>CommonOrderForm: 上传附件
    activate CommonOrderForm
    CommonOrderForm->>API: handleFileUpload() - 获取COS临时凭证
    activate API
    API-->>CommonOrderForm: 返回凭证
    deactivate API
    CommonOrderForm->>COS: 上传文件
    activate COS
    COS-->>CommonOrderForm: 返回上传结果
    deactivate COS
    deactivate CommonOrderForm

    User->>CommonOrderForm: 点击提交
    activate CommonOrderForm
    CommonOrderForm->>CommonOrderForm: handleSubmit() - 验证和构造数据
    CommonOrderForm->>API: requestCommonOrder(args)
    activate API
    API-->>CommonOrderForm: 返回提交结果
    deactivate API
    CommonOrderForm->>User: 显示成功提示并跳转页面
    deactivate CommonOrderForm
```

