# OrderDetailDrawer/index.js

## 类/方法/函数详解

### OrderDetailDrawer
#### getCnMap
- 用途：根据工单类型获取对应的中文字段映射配置
- 输入参数：
  - orderType: 工单类型字符串，如 "sql_audit_execute", "server_jump_impower" 等
- 输出：返回对应工单类型的字段映射对象，每个字段包含 [跨度, 中文名称] 的数组
- 实现要点：
  ```
  如果 orderType 存在于 cnMap 中：
    返回 cnMap[orderType]
  否则：
    返回默认配置 { "apply_msg": [6, "申请理由"] }
  ```

#### componentDidUpdate
- 用途：React 生命周期方法，监听 drawerVisible 状态变化，当抽屉从关闭变为打开时获取工单详情
- 输入参数：
  - prevProps: 上一次的 props 对象
  - prevState: 上一次的 state 对象
- 输出：无返回值，通过 setState 更新组件状态
- 实现要点：
  ```
  如果 drawerVisible 从 false 变为 true：
    1. 获取用户邮箱（从 Cookie）
    2. 调用 requestOrderDetail API 获取工单详情
    3. 解析 stage_infos 和 order_info
    4. 根据当前用户邮箱和工单状态决定审批按钮显示
    5. 处理 orderInfo.info 字符串转 JSON 对象
    6. 构建流程表格数据 columnData
    7. 更新组件状态
  ```

#### componentWillUnmount
- 用途：React 生命周期方法，组件销毁前的清理工作
- 输入参数：无
- 输出：无返回值
- 实现要点：
  ```
  重写 setState 方法为空函数，避免组件销毁后仍有异步操作尝试更新状态导致的警告
  ```

#### showDrawer
- 用途：显示订单详情抽屉
- 输入参数：无
- 输出：无返回值
- 实现要点：
  ```
  设置 drawerVisible 为 true
  ```

#### closeDrawer
- 用途：隐藏订单详情抽屉
- 输入参数：无
- 输出：无返回值
- 实现要点：
  ```
  设置 drawerVisible 为 false
  ```

#### showAuditModel
- 用途：显示审批对话框
- 输入参数：无
- 输出：无返回值
- 实现要点：
  ```
  设置 auditVisible 为 true
  ```

#### closeAuditModel
- 用途：关闭审批对话框
- 输入参数：无
- 输出：无返回值
- 实现要点：
  ```
  设置 auditVisible 为 false
  ```

#### handleAudit
- 用途：处理审批结果提交
- 输入参数：
  - result: 审批结果字符串，"yes" 表示同意，"no" 表示驳回
- 输出：无返回值
- 实现要点：
  ```
  1. 构建审批参数对象（order_id, stage_num, chosen_action）
  2. 立即关闭审批对话框
  3. 关闭详情抽屉
  4. 异步调用 requestFlowApproval API 提交审批结果
  5. 根据响应结果显示成功或失败消息
  ```

#### showTurnAuditVisible
- 用途：显示审批人变更对话框
- 输入参数：无
- 输出：无返回值
- 实现要点：
  ```
  设置 turnAuditVisible 为 true
  ```

#### closeTurnAuditVisible
- 用途：关闭审批人变更对话框
- 输入参数：无
- 输出：无返回值
- 实现要点：
  ```
  设置 turnAuditVisible 为 false
  ```

#### handleTurnAudit
- 用途：处理审批人变更请求
- 输入参数：无
- 输出：无返回值
- 实现要点：
  ```
  1. 验证邮箱不为空
  2. 验证邮箱格式（使用正则表达式）
  3. 构建参数对象（order_id, stage_num, new_operator_email）
  4. 立即关闭审批人变更对话框
  5. 关闭详情抽屉
  6. 异步调用 requestFlowAuditTurn API 提交变更请求
  7. 根据响应结果显示成功或失败消息
  ```

#### handleAuditEmailChange
- 用途：处理审批人邮箱输入变化
- 输入参数：
  - e: 输入事件对象
- 输出：无返回值
- 实现要点：
  ```
  更新 state 中的 auditEmail 字段为输入框的值
  ```

#### saveRemark
- 用途：保存工单备注
- 输入参数：
  - e: 事件对象
- 输出：无返回值
- 实现要点：
  ```
  调用 requestUpdateOrderRemark API 更新工单备注
  参数包括 order_id 和 去除空格的 remark 内容
  ```

#### 类用途
OrderDetailDrawer 是一个 React 类组件，用于显示工单详情的抽屉组件。主要功能包括：
- 展示工单审批流程进度
- 显示工单详细信息
- 提供审批操作功能
- 支持审批人变更
- 集成评论功能
- 处理工单备注更新

使用场景：在工单列表页面中，当用户点击查看工单详情时，会弹出此抽屉组件展示完整的工单信息和操作界面。

## 文件用途
1. 代码用途概述
   - 实现工单详情展示的抽屉组件
   - 提供工单审批流程的可视化界面
   - 集成工单审批、审批人变更等操作功能
   - 支持不同类型工单的字段映射显示
   - 集成评论系统用于工单沟通

2. 使用场景和流程
   - 用户在工单列表页面点击"查看详情"按钮
   - 组件显示抽屉，同时触发 componentDidUpdate 获取工单详情
   - 展示工单审批流程进度条和详细信息
   - 根据用户权限显示相应的操作按钮（审批、审批人变更）
   - 用户可以进行审批操作或审批人变更
   - 支持在工单上添加评论和备注

## 调用时序图

### 打开工单详情流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant Button as 详情按钮
    participant Component as OrderDetailDrawer
    participant Cookie as Cookies
    participant API as requestOrderDetail
    participant State as Component State

    User->>Button: 点击详情按钮
    Button->>Component: showDrawer()
    Component->>State: setState({drawerVisible: true})
    State->>Component: componentDidUpdate()
    Component->>Cookie: Cookies.get("user_email")
    Cookie-->>Component: 返回用户邮箱
    Component->>API: requestOrderDetail({order_id})
    API-->>Component: 返回工单详情数据
    Component->>Component: 解析数据，构建状态
    Component->>State: setState({stageInfos, orderInfo, columnData})
    State->>Component: 重新渲染抽屉内容
```

### 审批工单流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant Button as 审批按钮
    participant Component as OrderDetailDrawer
    participant Modal as 审批对话框
    participant API as requestFlowApproval
    participant Message as 消息提示

    User->>Button: 点击审批按钮
    Button->>Component: showAuditModel()
    Component->>Modal: 显示审批对话框
    User->>Modal: 选择同意/驳回
    Modal->>Component: handleAudit(result)
    Component->>Component: closeAuditModel()
    Component->>Component: closeDrawer()
    Component->>API: requestFlowApproval({order_id, stage_num, chosen_action})
    API-->>Component: 返回审批结果
    Component->>Message: 显示成功/失败消息
```

### 审批人变更流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant Button as 审批人变更按钮
    participant Component as OrderDetailDrawer
    participant Modal as 变更对话框
    participant Input as 邮箱输入
    participant API as requestFlowAuditTurn
    participant Message as 消息提示

    User->>Button: 点击审批人变更按钮
    Button->>Component: showTurnAuditVisible()
    Component->>Modal: 显示变更对话框
    User->>Input: 输入新审批人邮箱
    Input->>Component: handleAuditEmailChange()
    User->>Modal: 点击提交
    Modal->>Component: handleTurnAudit()
    Component->>Component: 验证邮箱格式
    Component->>Component: closeTurnAuditVisible()
    Component->>Component: closeDrawer()
    Component->>API: requestFlowAuditTurn({order_id, stage_num, new_operator_email})
    API-->>Component: 返回变更结果
    Component->>Message: 显示成功/失败消息
```

### 保存备注流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant Component as OrderDetailDrawer
    participant API as requestUpdateOrderRemark

    User->>Component: 触发保存备注
    Component->>Component: saveRemark()
    Component->>API: requestUpdateOrderRemark({order_id, remark})
    API-->>Component: 返回保存结果
```
