# /Users/<USER>/soft/develop/GoProject/wfo-frontend/src/pages/auditOrder/index.js

## 类/方法/函数详解

### AuditOrder
#### constructor
- 用途: 初始化 `AuditOrder` 组件，并创建 `SearchForm` 的 ref 引用。
- 逐一说明输入参数:
  - `props`: React 组件的属性。
- 输出: 无。
- 实现要点:
  - 调用父类的构造函数 `super(props)`。
  - 使用 `React.createRef()` 创建一个 ref，并将其赋值给 `this.searchFormRef`，用于后续访问 `SearchForm` 组件实例。

#### state
- 用途: 定义和管理 `AuditOrder` 组件的状态。
- 逐一说明状态属性:
  - `dataSource`: 存储工单数据的数组。
  - `pageSize`: 每页显示的工单数量。
  - `pageNum`: 当前页码。
  - `total`: 工单总数。
  - `loading`: 表示是否正在加载数据的布尔值。
  - `searchParams`: 存储搜索条件的对急，包括 `orderId`, `title`, `appliedStartDate`, `appliedEndDate`, `orderTypes`, 和 `filter_by_role`。
  - `orderTypeFilters`: 存储工单类型筛选选项的数组。
- 实现要点:
  - 初始化了组件在整个生命周期中需要用到的所有状态属性，并为它们设置了合理的默认值。

#### componentDidMount
- 用途: 在组件挂载后立即请求第一页的工单数据。
- 逐一说明输入参数: 无。
- 输出: 无。
- 实现要点:
  - 调用 `this.requestMyAuditPageOrder()` 方法来获取数据。

#### parseOrderTitle
- 用途: 安全地从工单的 `info` 字段（JSON 字符串）中解析出标题。
- 逐一说明输入参数:
  - `info`: 包含工单信息的 JSON 字符串。
  - `orderId`: 工单 ID，用于在解析失败时生成默认标题。
- 输出: 解析出的工单标题字符串，如果解析失败则返回一个默认标题。
- 实现要点:
  - 使用 `try...catch` 块来处理 JSON 解析可能出现的异常。
  - 如果 `info` 存在且不为空，则尝试用 `JSON.parse()` 解析。
  - 如果解析成功且包含 `title` 属性，则返回 `title`。
  - 如果解析失败或 `info` 为空，则返回一个格式为 `工单-${orderId}` 的默认标题。

#### getRoleType
- 用途: 根据 `approval_status_for_me` 和 `is_cc` 字段的值，组合判断当前用户在工单中的角色类型。
- 逐一说明输入参数:
  - `approval_status_for_me`: 当前用户的审批状态。
  - `is_cc`: 一个标志，指示当前用户是否是抄送人。
- 输出: 表示角色类型的字符串，例如 `'CC_TO_ME'`, `'TO_BE_APPROVED'`, `'WILL_BE_APPROVED'`, `'ALREADY_APPROVED'`, `'COMPLETED'`，或者在无法确定时返回 `null`。
- 实现要点:
  - 首先检查 `is_cc` 是否为 1，如果是，则直接返回 `'CC_TO_ME'`。
  - 如果不是抄送，则使用 `switch` 语句根据 `approval_status_for_me` 的值返回相应的审批状态字符串。
  - 如果 `approval_status_for_me` 的值没有匹配的 case，则返回 `null`。

#### requestMyAuditPageOrder
- 用途: 异步请求需要我审批的工单数据，并支持搜索、筛选和分页功能。
- 逐一说明输入参数: 无（从 `this.state` 中获取分页和搜索参数）。
- 输出: 无（异步更新组件的 `state`）。
- 实现要点:
  - 设置 `loading` 状态为 `true`。
  - 从 `state` 中获取 `pageNum`, `pageSize`, 和 `searchParams`。
  - 构建请求参数对象，包含分页和所有搜索/筛选条件。
  - 调用 `requestMyAuditOrderWithSearch` API 发送请求。
  - **成功响应处理**:
    - 检查响应中的 `resp_common.ret` 是否为 0。
    - 将返回的 `orders` 数组映射为 `dataSource` 需要的格式，包括解析工单标题、确定角色类型等。
    - 如果后端返回了 `order_types`，则调用 `updateOrderTypeFiltersFromResponse` 更新筛选选项。
    - 否则，如果本地没有筛选选项，则调用 `extractOrderTypeFilters` 从当前返回的工单中提取。
    - 使用返回的数据更新 `dataSource`, `total`, 和 `loading` 状态。
  - **失败或异常处理**:
    - 在 `catch` 块中捕获请求过程中的任何错误。
    - 将 `dataSource` 设置为空数组，`total` 为 0，并设置 `loading` 为 `false`。
    - 如果是首次加载且没有筛选选项，则调用 `useMockOrderTypeFilters` 使用模拟数据。
    - 调用 `handleValidationError` 显示错误提示。

#### handlePageChange
- 用途: 处理分页组件的页码变化事件。
- 逐一说明输入参数:
  - `pageNum`: 新的页码。
- 输出: 无。
- 实现要点:
  - 使用 `setState` 更新 `pageNum`。
  - 在 `setState` 的回调函数中调用 `this.requestMyAuditPageOrder()` 来请求新页码的数据。

#### handlePageSizeChange
- 用途: 处理分页组件的每页显示数量变化事件。
- 逐一说明输入参数:
  - `current`: 当前页码（未使用）。
  - `size`: 新的每页显示数量。
- 输出: 无。
- 实现要点:
  - 使用 `setState` 将 `pageNum` 重置为 1，并更新 `pageSize`。
  - 在 `setState` 的回调函数中调用 `this.requestMyAuditPageOrder()` 来请求数据。

#### handleSearch
- 用途: 处理来自 `SearchForm` 组件的搜索事件。
- 逐一说明输入参数:
  - `searchParams`: 包含搜索条件的对象。
  - `hasValidationErrors`: 一个布尔值，指示搜索条件是否存在校验错误。
- 输出: 无。
- 实现要点:
  - 如果 `hasValidationErrors` 为 `true`，则直接返回，不执行搜索。
  - 合并新的搜索条件和已有的筛选条件（`orderTypes` 和 `filter_by_role`）。
  - 使用 `setState` 更新 `searchParams` 并将 `pageNum` 重置为 1。
  - 在 `setState` 的回调函数中调用 `this.requestMyAuditPageOrder()` 来根据新的搜索和筛选条件请求数据。

#### handleSearchParamsChange
- 用途: 处理 `SearchForm` 中搜索参数的实时变化，主要用于同步清空操作，而不触发 API 请求。
- 逐一说明输入参数:
  - `searchParams`: 包含搜索条件的对象。
- 输出: 无。
- 实现要点:
  - 合并新的搜索条件和已有的筛选条件（`orderTypes` 和 `filter_by_role`）。
  - 只调用 `setState` 更新 `searchParams`，但不包含请求数据的回调，从而避免了在用户输入过程中的频繁 API 调用。

#### handleValidationError
- 用途: 显示一个全局的警告提示信息。
- 逐一说明输入参数:
  - `errorMessage`: 要显示的错误信息字符串。
- 输出: 无。
- 实现要点:
  - 调用 antd 的 `message.warn()` 方法来显示警告信息。

#### getCurrentSearchParams
- 用途: 获取 `SearchForm` 子组件当前的搜索条件。
- 逐一说明输入参数: 无。
- 输出: 一个包含当前搜索条件的对象，如果 `SearchForm` 的 ref 不可用，则返回一个包含空字符串和 `null` 值的默认对象。
- 实现要点:
  - 通过 `this.searchFormRef.current` 访问 `SearchForm` 组件的实例。
  - 如果 ref 存在，则调用其 `getCurrentSearchParams` 方法来获取最新的搜索参数。
  - 如果 ref 不存在，则返回一个默认的搜索参数对象，以防止错误。

#### handleTableChange
- 用途: 统一处理 `OrderTable` 组件的变化事件，包括筛选。
- 逐一说明输入参数:
  - `pagination`: 分页信息（未使用）。
  - `filters`: 包含当前筛选值的对象。
  - `sorter`: 排序信息（未使用）。
- 输出: 无。
- 实现要点:
  - 调用 `getCurrentSearchParams` 获取 `SearchForm` 中最新的搜索条件。
  - 从 `filters` 参数中提取工单类型（`orderType`）和角色类型（`orderId`，此处似乎是字段误用，应为 `role_type`）的筛选值。
  - 合并最新的搜索条件和表格筛选条件，创建一个新的 `searchParams` 对象。
  - 使用 `setState` 更新 `searchParams` 并将 `pageNum` 重置为 1。
  - 在 `setState` 的回调中调用 `requestMyAuditPageOrder` 来根据最新的组合条件请求数据。

#### handleOrderTypeFilterChange
- 用途: 处理工单类型筛选的变化，并触发数据重新请求（为保持兼容性而保留）。
- 逐一说明输入参数:
  - `selectedTypes`: 包含所选工单类型值的数组。
- 输出: 无。
- 实现要点:
  - 使用扩展运算符 `...` 创建一个新的 `searchParams` 对象，并更新其中的 `orderTypes`。
  - 使用 `setState` 更新 `searchParams` 并将 `pageNum` 重置为 1。
  - 在 `setState` 的回调中调用 `requestMyAuditPageOrder` 来获取筛选后的数据。

#### handleRoleFilterChange
- 用途: 处理角色筛选的变化，并触发数据重新请求（为保持兼容性而保留）。
- 逐一说明输入参数:
  - `selectedRoles`: 包含所选角色类型值的数组。
- 输出: 无。
- 实现要点:
  - 使用扩展运算符 `...` 创建一个新的 `searchParams` 对象，并更新其中的 `filter_by_role`。
  - 使用 `setState` 更新 `searchParams` 并将 `pageNum` 重置为 1。
  - 在 `setState` 的回调中调用 `requestMyAuditPageOrder` 来获取筛选后的数据。

#### extractOrderTypeFilters
- 用途: 从工单数据中提取唯一的工单类型，用于生成表格的筛选选项。
- 逐一说明输入参数:
  - `orders`: 从 API 获取的原始工单数据数组。
- 输出: 无（通过 `setState` 更新 `orderTypeFilters`）。
- 实现要点:
  - 如果 `orders` 数组为空，则调用 `useMockOrderTypeFilters` 使用模拟数据。
  - 使用 `Map` 来存储唯一的工单类型，以 `order_type` 为键，`order_type_name` 为值，自动去重。
  - 遍历 `orders` 数组，将有效的工单类型（`order_type` 和 `order_type_name` 均不为空）存入 Map。
  - 将 Map 转换为 antd Table `filters` 所需的格式（`{ text: '显示文本', value: '筛选值' }`）。
  - 按工单类型的中文名称对筛选选项进行排序。
  - 如果成功提取到筛选选项，则通过 `setState` 更新 `orderTypeFilters`；否则，调用 `useMockOrderTypeFilters`。

#### updateOrderTypeFiltersFromResponse
- 用途: 使用后端直接返回的工单类型列表来更新筛选选项。
- 逐一说明输入参数:
  - `orderTypes`: 后端返回的工单类型字符串数组。
- 输出: 无（通过 `setState` 更新 `orderTypeFilters`）。
- 实现要点:
  - 遍历 `orderTypes` 数组。
  - 对每个 `orderType`，调用 `getOrderTypeDisplayName` 获取其对应的显示名称。
  - 将每个 `orderType` 及其显示名称转换为 antd Table `filters` 所需的格式。
  - 按显示名称对生成的筛选选项进行排序。
  - 通过 `setState` 更新 `orderTypeFilters`。

#### getOrderTypeDisplayName
- 用途: 将工单类型的标识符（如 `ks_server_apply`）映射为用户友好的中文显示名称（如 “金山云服务器申请”）。
- 逐一说明输入参数:
  - `orderType`: 工单类型的标识符字符串。
- 输出: 对应的中文显示名称字符串。如果未找到映射，则返回原始的 `orderType` 字符串。
- 实现要点:
  - 内部维护一个 `typeNameMap` 对象，作为工单类型标识符到中文名称的映射表。
  - 通过 `typeNameMap[orderType]` 查找映射关系。
  - 如果找到，则返回对应的中文名称；否则，返回原始的 `orderType` 作为备用。

#### useMockOrderTypeFilters
- 用途: 在无法从后端获取工单类型时，提供一组硬编码的模拟工单类型筛选选项作为备用方案。
- 逐一说明输入参数: 无。
- 输出: 无（通过 `setState` 更新 `orderTypeFilters`）。
- 实现要点:
  - 定义一个 `mockFilters` 数组，其中包含多个预设的工单类型筛选选项对象。
  - 每个对象都符合 antd Table `filters` 的格式（`{ text: '...', value: '...' }`）。
  - 调用 `setState` 将 `orderTypeFilters` 更新为这个 `mockFilters` 数组。

#### render
- 用途: 渲染 `AuditOrder` 组件的 UI。
- 逐一说明输入参数: 无。
- 输出: React 元素。
- 实现要点:
  - 从 `this.state` 中解构出 `dataSource`, `loading`, `total`, `pageNum`, `pageSize`, `orderTypeFilters`, 和 `searchParams`。
  - 渲染 `SearchForm` 组件，并传递必要的 props，如 `onSearch`, `onSearchParamsChange`, `loading`, 和 `onValidationError`。
  - 根据 `loading` 状态，条件性地渲染 `LoadingSkeleton` 或 `OrderTable` 组件。
  - 渲染 `OrderTable` 组件，并传递 `dataSource`, `loading`, `orderTypeFilters` 等 props。
  - 渲染 `OrderPagination` 组件，并传递 `total`, `current`, `pageSize` 等 props，以及分页变化的回调函数。

## 文件用途
1. 代码用途概述
   - 该文件实现了一个 React 类组件 `AuditOrder`，用于展示一个需要当前用户审批的工单列表页面。它集成了搜索、筛选、分页和加载状态显示等功能，并通过调用 API 与后端进行数据交互。

2. 使用场景和流程
   - **使用场景**: 当用户需要查看、搜索和处理待其审批、将要审批、已审批或抄送给他们的工单时，会加载此页面。
   - **流程**:
     1. 组件挂载后，自动请求第一页的工单数据。
     2. 用户可以在 `SearchForm` 中输入工单 ID、标题、申请日期范围进行搜索。
     3. 用户可以点击表格头部的筛选按钮，按工单类型或角色类型进行筛选。
     4. 用户可以点击 `OrderPagination` 组件切换页面或改变每页显示的条数。
     5. 所有搜索、筛选和分页操作都会触发 `requestMyAuditPageOrder` 方法，向后端请求更新后的工单数据，并刷新表格显示。

## 文件内类图
```mermaid
classDiagram
    class AuditOrder {
        +constructor(props)
        +state
        +componentDidMount()
        +parseOrderTitle(info, orderId)
        +getRoleType(approval_status_for_me, is_cc)
        +requestMyAuditPageOrder()
        +handlePageChange(pageNum)
        +handlePageSizeChange(current, size)
        +handleSearch(searchParams, hasValidationErrors)
        +handleSearchParamsChange(searchParams)
        +handleValidationError(errorMessage)
        +getCurrentSearchParams()
        +handleTableChange(pagination, filters, sorter)
        +handleOrderTypeFilterChange(selectedTypes)
        +handleRoleFilterChange(selectedRoles)
        +extractOrderTypeFilters(orders)
        +updateOrderTypeFiltersFromResponse(orderTypes)
        +getOrderTypeDisplayName(orderType)
        +useMockOrderTypeFilters()
        +render()
    }
    class Component
    AuditOrder --|> Component
```

## 调用时序图
```mermaid
sequenceDiagram
    participant User
    participant SearchForm
    participant OrderTable
    participant OrderPagination
    participant AuditOrder
    participant API

    User->>AuditOrder: 页面加载
    activate AuditOrder
    AuditOrder->>API: requestMyAuditPageOrder()
    activate API
    API-->>AuditOrder: 返回工单数据
    deactivate API
    AuditOrder->>OrderTable: 更新dataSource
    deactivate AuditOrder

    User->>SearchForm: 输入搜索条件并点击搜索
    activate SearchForm
    SearchForm->>AuditOrder: handleSearch(searchParams)
    deactivate SearchForm
    activate AuditOrder
    AuditOrder->>API: requestMyAuditPageOrder()
    activate API
    API-->>AuditOrder: 返回搜索结果
    deactivate API
    AuditOrder->>OrderTable: 更新dataSource
    deactivate AuditOrder

    User->>OrderTable: 点击筛选
    activate OrderTable
    OrderTable->>AuditOrder: handleTableChange(filters)
    deactivate OrderTable
    activate AuditOrder
    AuditOrder->>API: requestMyAuditPageOrder()
    activate API
    API-->>AuditOrder: 返回筛选结果
    deactivate API
    AuditOrder->>OrderTable: 更新dataSource
    deactivate AuditOrder

    User->>OrderPagination: 切换页面
    activate OrderPagination
    OrderPagination->>AuditOrder: handlePageChange(pageNum)
    deactivate OrderPagination
    activate AuditOrder
    AuditOrder->>API: requestMyAuditPageOrder()
    activate API
    API-->>AuditOrder: 返回新页面的数据
    deactivate API
    AuditOrder->>OrderTable: 更新dataSource
    deactivate AuditOrder
```

