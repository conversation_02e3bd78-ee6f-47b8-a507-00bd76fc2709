## 调用链

### 节点(scripts/start.js)
**所在代码文件相对路径**
`scripts/start.js`

**用途**
此脚本是项目的开发环境入口。它负责设置环境变量，检查必需的文件是否存在，选择可用的端口，然后配置并启动一个 `WebpackDevServer` 实例来提供前端应用，并支持热重载。

**输入参数**
无直接函数参数，但通过 `process.env` 读取环境变量，例如 `PORT`、`HOST`、`HTTPS` 等。

**输出说明**
无直接返回值。此脚本会启动一个长期运行的Node.js进程（Webpack Dev Server），在本地启动一个Web服务器，并在浏览器中打开应用页面。

**Mermaid图**
```mermaid
flowchart TD
    A[开始] --> B{设置环境变量};
    B --> C{检查必需文件};
    C --> D{检查浏览器兼容性};
    D --> E{选择可用端口};
    E --> F{创建Webpack配置};
    F --> G{创建Webpack编译器};
    G --> H{准备代理配置};
    H --> I{创建WebpackDevServer配置};
    I --> J[实例化WebpackDevServer];
    J --> K[启动服务器];
    K --> L{在浏览器中打开URL};
    L --> M[结束];
```

### 节点(config/env.js)
**所在代码文件相对路径**
`config/env.js`

**用途**
此模块负责加载环境变量。它会根据 `NODE_ENV` (例如, `development`, `test`, `production`) 从 `.env` 文件系列 (`.env.development`, `.env.local` 等) 中读取配置，并将它们设置到 `process.env` 中。它还收集所有以 `REACT_APP_` 开头的环境变量，准备将它们注入到客户端代码中。

**输入参数**
- `publicUrl` (在 `getClientEnvironment` 函数中): 公共URL路径，用于解析静态资源。

**输出说明**
- `getClientEnvironment` 函数返回一个对象，包含两个属性：
    - `raw`: 包含原始环境变量键值对的对象。
    - `stringified`: 一个将 `raw` 中的值字符串化的对象，用于 Webpack 的 `DefinePlugin`。

**Mermaid图**
```mermaid
flowchart TD
    A[开始] --> B{检查NODE_ENV是否存在};
    B --> C{确定.env文件列表};
    C --> D[遍历.env文件];
    D --> E{文件是否存在?};
    E -- 是 --> F[加载环境变量];
    F --> D;
    E -- 否 --> D;
    D -- 完成 --> G{设置NODE_PATH};
    G --> H[定义getClientEnvironment函数];
    H --> I[导出getClientEnvironment];
    I --> J[结束];
```

### 节点(config/paths.js)
**所在代码文件相对路径**
`config/paths.js`

**用途**
该模块定义了项目中所有重要文件和目录的绝对路径。它通过解析当前工作目录来确保路径的正确性，并处理了符号链接的情况。这些路径在整个构建和开发过程中被广泛使用，例如用于查找入口文件、HTML模板、node_modules目录等。

**输入参数**
无直接函数参数，但会读取 `process.env.NODE_ENV`、`process.env.PUBLIC_URL` 和 `package.json` 中的 `homepage` 字段。

**输出说明**
导出一个对象，其中包含项目中各个关键位置的绝对路径，例如 `appPath`, `appBuild`, `appSrc`, `appHtml`, `appIndexJs` 等。

**Mermaid图**
```mermaid
flowchart TD
    A[开始] --> B{获取当前工作目录};
    B --> C{解析应用根目录的真实路径};
    C --> D{定义resolveApp函数以生成绝对路径};
    D --> E{获取publicUrlOrPath};
    E --> F{定义模块文件扩展名};
    F --> G{定义resolveModule函数以解析模块路径};
    G --> H[导出包含所有路径的对象];
    H --> I[结束];
```

### 节点(config/webpack.config.js)
**所在代码文件相对路径**
`config/webpack.config.js`

**用途**
这是一个工厂函数，根据 `webpackEnv`（`development` 或 `production`）参数，生成并返回一个完整的 Webpack 配置对象。这个配置对象定义了如何打包项目，包括入口文件、输出路径、加载器（loaders）、插件（plugins）、优化选项等。它处理 JavaScript、CSS、图片等各种资源，并针对开发和生产环境进行不同的优化。

**输入参数**
- `webpackEnv`: 一个字符串，值为 `'development'` 或 `'production'`，用于确定构建环境。

**输出说明**
返回一个 Webpack 配置对象，该对象可被 Webpack 用于编译和打包项目。

**Mermaid图**
```mermaid
flowchart TD
    A[开始] --> B{判断开发/生产环境};
    B --> C{获取环境变量};
    C --> D{定义getStyleLoaders函数};
    D --> E["配置入口(entry)和输出(output)"];
    E --> F["配置优化选项(optimization)"];
    F --> G["配置模块解析(resolve)"];
    G --> H["配置模块规则(module.rules)"];
    H --> I["配置插件(plugins)"];
    I --> J["配置node stubs"];
    J --> K["返回Webpack配置对象"];
    K --> L[结束];
```

### 节点(config/webpackDevServer.config.js)
**所在代码文件相对路径**
`config/webpackDevServer.config.js`

**用途**
此模块导出一个函数，该函数创建用于 `webpack-dev-server` 的配置对象。此配置控制开发服务器的行为，包括代理请求、HTTPS、热重载以及如何提供静态文件等设置。

**输入参数**
- `proxy`: 代理配置对象。
- `allowedHost`: 一个表示允许的主机的字符串。

**输出说明**
返回 `webpack-dev-server` 的配置对象。

**Mermaid图**
```mermaid
flowchart TD
    A[开始] --> B{接收 proxy 和 allowedHost};
    B --> C["配置主机检查和压缩"];
    C --> D["配置静态文件服务(contentBase)"];
    D --> E["启用热重载(hot)"];
    E --> F["配置WebSocket传输模式"];
    F --> G["配置historyApiFallback"];
    G --> H["配置HTTPS"];
    H --> I["设置代理(proxy)"];
    I --> J["定义before和after中间件"];
    J --> K["返回WebpackDevServer配置对象"];
    K --> L[结束];
```

## 整体用途
该调用链的整体用途是启动一个用于本地开发的Web服务器。它通过一系列的配置和脚本，最终运行一个 `webpack-dev-server` 实例。这个服务器不仅提供了静态文件服务，还通过Webpack实现了模块打包、热重载（HMR）、代理API请求等功能，为前端开发者提供了一个高效、便捷的开发环境。

## 目录结构
```
/Users/<USER>/soft/develop/GoProject/wfo-frontend/
├───config/
│   ├───env.js
│   ├───paths.js
│   ├───webpack.config.js
│   └───webpackDevServer.config.js
└───scripts/
    └───start.js
```

## 调用时序图
```mermaid
sequenceDiagram
    participant User
    participant scripts/start.js
    participant config/env.js
    participant config/paths.js
    participant config/webpack.config.js
    participant config/webpackDevServer.config.js
    participant WebpackDevServer

    User->>scripts/start.js: 执行 `node scripts/start.js`
    scripts/start.js->>config/env.js: require('../config/env')
    config/env.js-->>scripts/start.js: 加载环境变量
    scripts/start.js->>config/paths.js: require('../config/paths')
    config/paths.js-->>scripts/start.js: 返回路径对象
    scripts/start.js->>config/webpack.config.js: configFactory('development')
    config/webpack.config.js-->>scripts/start.js: 返回Webpack配置
    scripts/start.js->>config/webpackDevServer.config.js: createDevServerConfig(...)
    config/webpackDevServer.config.js-->>scripts/start.js: 返回DevServer配置
    scripts/start.js->>WebpackDevServer: new WebpackDevServer(compiler, serverConfig)
    WebpackDevServer->>WebpackDevServer: listen(port, HOST, ...)
    WebpackDevServer-->>User: 启动开发服务器并打开浏览器
```
