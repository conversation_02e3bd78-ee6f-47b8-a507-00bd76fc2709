worker_processes 2;

events {
  worker_connections  1024;
}

http {
  include       mime.types;
  default_type  application/octet-stream;

  #如果有proxy_pass的转发到后台的需求，反注释该段，修改后端的server信息
  # upstream backend {
  #  server https://baofun-cms.zhhainiao.com;
  # }
  gzip on;
  gzip_min_length 1k;
  gzip_buffers 4 16k;
  gzip_comp_level 2;
  gzip_types text/plain application/x-javascript application/json text/css application/xml text/javascript application/javascript application/x-httpd-php image/jpeg image/gif image/png;
  gzip_vary off;
  gzip_disable "MSIE [1-6]\.";

  server {
    server_name _;
    listen 8000 default_server;

    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forward-For $remote_addr;
    proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
    proxy_buffer_size 64k;
    proxy_buffers 32 64k;
    client_max_body_size 0;
    charset utf-8;

    location = /_healthz {
      add_header Content-Type text/plain;
      return 200 'ok';
    }

    #如果有proxy_pass的转发到后台的需求，反注释该段
    # location /api/ {
    #  client_max_body_size 24m;
    #  proxy_read_timeout 600s;
    #  proxy_send_timeout 600s;
    #  proxy_pass http://backend;
    # }

    # 此处root的位置需要和Dockerfile里面编译输出的位置一致
    location / {
      root /data/apps/fe/dist/;
      if ($request_filename ~ .*\.(css|js|png|jpg|svg|webp|gif|ico|ttf|woff|woff2|eot)$) {
        add_header Cache-Control "public, max-age=2592000";
      }
      # if ($request_filename ~ .*\.(html)$) {
      #   add_header Cache-Control "public, max-age=1200";
      # }
      try_files $uri $uri/ /index.html;
    }
  }
}
