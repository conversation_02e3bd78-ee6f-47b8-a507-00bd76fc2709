token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoid2ZvLWZyb250ZW5kIiwibmFtZXNwYWNlIjoib3BzIiwia2luZCI6IlNlcnZpY2UiLCJpc3MiOiJrOHMtYXBpLWxpdGUifQ.Gluniy4ifIe_psteOh5mqAXFyTGzcUQKYER5FmW4xSk

namespace: ops # 这是服务部署到的命名空间，按业务部署，相同业务的部署到同一namespace
name: wfo-frontend # 这是服务名

ports: # 服务的端口，需要和Docker image暴露的一致
  - name: http
    port: 8000

replicas: 2 # 服务正式部署的副本数

requests: # 部署的最低资源需求
  cpu: 0.01 # 0.01个CPU核
  memory: 64Mi # 64MB内存

limits: # 部署的最大资源限制
  cpu: 1 # 1个CPU核
  memory: 1024Mi # 1024MB内存

option:
  readinessProbe: # ready检查
    failureThreshold: 3
    httpGet:
      path: /_healthz # 这个路径是在nginx.conf里面返回的
      port: 8000
      scheme: HTTP
    initialDelaySeconds: 3
    periodSeconds: 5
    successThreshold: 1
    timeoutSeconds: 2
