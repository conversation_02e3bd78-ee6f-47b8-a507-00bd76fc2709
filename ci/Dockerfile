# 使用两阶段编译
# 第一阶段builder用的镜像
FROM node:22.17-alpine as builder

ARG BUILD=build

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && apk update && apk add tzdata ca-certificates && apk add curl

# 用npm执行编译命令
WORKDIR /data/build
ADD ./ /data/build
# RUN curl -XPOST http://npm.t.iweikan.cn:8001/ott/require > ~/.npmrc
RUN apk add --no-cache yarn
# RUN npm install -g nrm open@8.4.2
RUN npm install -g --registry https://mirrors.cloud.tencent.com/npm nrm open@8.4.2

RUN nrm ls
RUN nrm use tencent
# RUN npm install yarn

RUN cd /data/build && npx yarn && npx yarn $BUILD 

# 正式image用的基础镜像
FROM nginx:1.17.6-alpine

# 从上一阶段拷贝编译结果到第二阶段
COPY --from=builder /data/build/build /data/apps/fe/dist
COPY ci/nginx.conf /etc/nginx/nginx.conf

