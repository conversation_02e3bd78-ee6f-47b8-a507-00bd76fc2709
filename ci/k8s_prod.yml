variables:
  TX_CONTAINER_IMAGE: k8s-reg.cmcm.com/ops/wfo-frontend

## 生产环境镜像
k8s_build_image:
  image: "k8s-reg.cmcm.com/k8s/deploy_helper:latest"
  script:
    - docker pull $TX_CONTAINER_IMAGE:latest || true
    - docker build -f ./ci/Dockerfile --cache-from $TX_CONTAINER_IMAGE:latest --build-arg BUILD=build --tag $TX_CONTAINER_IMAGE:$CI_COMMIT_TAG .
    - docker push $TX_CONTAINER_IMAGE:$CI_COMMIT_TAG
  stage: build
  tags:
    - k8s
  only:
    variables:
      - $CI_COMMIT_TAG =~ /^.*-txy/

# 部署ingress，把k8s服务暴露给外网，这一段按实际需求看是否有必要，如果.k8s-ingress.yml后续没有变化，就不需要每次都部署
k8s_deploy_ingress: 
  image: "k8s-reg.cmcm.com/k8s/deploy_helper:latest"
  script:
    - deploy_helper -yaml=ci/.k8s-ingress.yml -kind=deploy_ingress -cls=haokan # -cls需要根据实际情况修改
  stage: deploy
  tags:
    - k8s
  when: manual
  only:
    variables:
      - $CI_COMMIT_TAG =~ /^.*-txy/

## 部署Service
k8s_deploy_service:
  image: "k8s-reg.cmcm.com/k8s/deploy_helper:latest"
  script:
    - deploy_helper -yaml=ci/.k8s-service.yml -kind=deploy_service -tag=$CI_COMMIT_TAG -image=$TX_CONTAINER_IMAGE -cls=haokan # -cls需要根据实际情况修改
  stage: deploy
  tags:
    - k8s
  dependencies:
    - k8s_build_image
  when: manual
  only:
    variables:
      - $CI_COMMIT_TAG =~ /^.*-txy/
  
