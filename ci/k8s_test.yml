variables:
  TX_CONTAINER_IMAGE: k8s-reg.cmcm.com/ops/wfo-frontend

## 测试环境镜像
k8s_build_test_image:
  image: "k8s-reg.cmcm.com/k8s/deploy_helper:latest"
  script:
    - echo test:$TX_CONTAINER_IMAGE:image
    - docker pull $TX_CONTAINER_IMAGE:latest || true
    - docker build -f ./ci/Dockerfile --cache-from $TX_CONTAINER_IMAGE:latest --build-arg BUILD=test --tag $TX_CONTAINER_IMAGE:$CI_COMMIT_TAG .
    - docker push $TX_CONTAINER_IMAGE:$CI_COMMIT_TAG
  stage: build
  tags:
    - k8s
  only:
    variables:
      - $CI_COMMIT_TAG =~ /^test-*/

## 测试环境 Service
k8s_deploy_test_service:
  image: "k8s-reg.cmcm.com/k8s/deploy_helper:latest"
  script:
    - deploy_helper -yaml=ci/.k8s-service.yml -kind=deploy_service -tag=$CI_COMMIT_TAG -image=$TX_CONTAINER_IMAGE -test=1 -cls=txytest
  stage: deploy
  tags:
    - k8s
  dependencies:
    - k8s_build_test_image
  # when: manual
  only:
    variables:
      - $CI_COMMIT_TAG =~ /^test-*/
