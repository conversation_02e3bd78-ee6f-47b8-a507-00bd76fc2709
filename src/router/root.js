import React, { Component } from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import BaseLayout from "@/common/baseLayout";
import Confirm from "@/pages/resourceDel/confirm";
import Login from "@/pages/login";
const rootRules = [
  { path: "/login", componet: <Login /> },
  { path: "/resdel/confirm", componet: <Confirm /> },
  { path: "/*", componet: <BaseLayout /> },
];

export default class RootRouter extends Component {
  render() {
    return (
      <BrowserRouter>
        <Routes>
          {rootRules.map((rule, index) => {
            return (
              <Route path={rule.path} element={rule.componet} key={index} />
            );
          })}
        </Routes>
      </BrowserRouter>
    );
  }
}
export { rootRules };
