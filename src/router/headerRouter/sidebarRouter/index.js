import { Component } from "react";
import { Routes, Route } from "react-router-dom";



import ServerAccountOrderForm from "@/pages/serverAccountOrder";
import CommonOrderForm from "@/pages/commonOrder";
import SqlOrder from "@/pages/sqlOrder";
import CdnFlush from "@/pages/cdn";
import CdnFlushHistory from "@/pages/cdn/flushHistory";
import CertUpload from "@/pages/cert";
// import CertUpdate from "@/pages/cert/update";
import Cdn from "@/pages/cdn/apply";
import DNS from "@/pages/dns";

import CmdbModelManger from "@/pages/cmdbModel";

import ServerOrder from "@/pages/serverOrder";
import MysqlOrder from "@/pages/mysqlOrder";
import RedisOrder from "@/pages/redisOrder";
import DomainOrder from "@/pages/domain";
import ServiceOwnerUpdate from "@/pages/resourceUpdate"


import TxServerApplyForm from "@/pages/serverOrder/applyForm/tx"
import AliServerApplyForm from "@/pages/serverOrder/applyForm/ali"
import KsServerApplyForm from "@/pages/serverOrder/applyForm/ks"
import HwServerApplyForm from "@/pages/serverOrder/applyForm/hw"
import TxMysqlApplyForm from "@/pages/mysqlOrder/applyForm/tx"
import KsMysql from "@/pages/mysqlOrder/applyForm/ks"
import AliMysql from "@/pages/mysqlOrder/applyForm/ali"
import HwMysql from "@/pages/mysqlOrder/applyForm/hw"
import TxRedisApplyForm from "@/pages/redisOrder/applyForm/tx"
import KsRedis from "@/pages/redisOrder/applyForm/ks"
import AliRedis from "@/pages/redisOrder/applyForm/ali"
import HwRedis from "@/pages/redisOrder/applyForm/hw"
import ResourceDel from "@/pages/resourceDel/index"
import K8sToken from "@/pages/k8stoken/index"
// 侧边栏导航栏路由
const sidebarRules = [
  {
    path: "dns",
    component: <DNS />,
    id: "dns",
  },
  {
    path: "resource-del",
    component: <ResourceDel />,
    id: "resource-del",
  },
  {
    path: "k8stoken",
    component: <K8sToken />,
    id: "k8stoken",
  },
  {
    path: "cdn",
    component: <Cdn />,
    id: "cdn",
  },
  {
    path: "server",
    component: <ServerOrder />,
    id: "server-order",
  },
  {
    path: "mysql",
    component: <MysqlOrder />,
    id: "mysql-order",
  },
  {
    path: "redis",
    component: <RedisOrder />,
    id: "redis-order",
  },
  {
    path: "domain",
    component: <DomainOrder />,
    id: "domain-order",
  },
  {
    path: "server-account",
    component: <ServerAccountOrderForm />,
    id: "server-account",
  },
  {
    path: "common",
    component: <CommonOrderForm />,
    id: "common-order",
  },
  {
    path: "sql",
    component: <SqlOrder />,
    id: "sql-order",
  },
  {
    path: "cdn-flush",
    component: <CdnFlush />,
    id: "cdn-flush",
  },
  {
    path: "cdn-flush-history",
    component: <CdnFlushHistory />,
    id: "cdn-flush-history",
  },
  {
    path: "cmdb/model-manager",
    component: < CmdbModelManger />,
    id: "cmdb-model-manager",
  },
  {
    path: "cert/upload",
    component: <CertUpload />,
    id: "cert-upload",
  },
  // {
  //   path: "cert/update",
  //   component: <CertUpdate />,
  //   id: "cert-update",
  // },
  {
    path: "service_owner/update",
    component: <ServiceOwnerUpdate />,
    id: "serviceOwner-update",
  },
];

const applyFormRules = [
  {
    path: "server/tx/apply-form",
    component: <TxServerApplyForm />,
    id: "tx-server-apply-form",
  },
  {
    path: "server/ali/apply-form",
    component: <AliServerApplyForm />,
    id: "ali-server-apply-form",
  },
  {
    path: "server/ks/apply-form",
    component: <KsServerApplyForm />,
    id: "ks-server-apply-form",
  },
  {
    path: "server/hw/apply-form",
    component: <HwServerApplyForm />,
    id: "hw-server-apply-form",
  },
  {
    path: "mysql/tx/apply-form",
    component: <TxMysqlApplyForm />,
    id: "tx-mysql-apply-form",
  },
  {
    path: "mysql/ks/apply-form",
    component: <KsMysql />,
    id: "ks-mysql-apply-form",
  },
  {
    path: "mysql/ali/apply-form",
    component: <AliMysql />,
    id: "ali-mysql-apply-form",
  },
  {
    path: "mysql/hw/apply-form",
    component: <HwMysql />,
    id: "hw-mysql-apply-form",
  },
  {
    path: "redis/tx/apply-form",
    component: <TxRedisApplyForm />,
    id: "tx-mysql-apply-form",
  },
  {
    path: "redis/ks/apply-form",
    component: <KsRedis />,
    id: "ks-mysql-apply-form",
  },
  {
    path: "redis/ali/apply-form",
    component: <AliRedis />,
    id: "ali-mysql-apply-form",
  },
  {
    path: "redis/hw/apply-form",
    component: <HwRedis />,
    id: "hw-mysql-apply-form",
  }
];
export default class SidebarRouter extends Component {
  render() {
    return (
      <Routes>
        {
          // 侧边栏导航项目路由
          sidebarRules.map((rule) => {
            return (
              <Route path={rule.path} element={rule.component} key={rule.id} />
            );
          })
        }
        {
          // 侧边栏其他路由
          applyFormRules.map((rule) => {
            return (
              <Route path={rule.path} element={rule.component} key={rule.id} />
            );
          })
        }
      </Routes>
    );
  }
}
export { sidebarRules };
