import { requestManagementPermission } from '../request/api';

/**
 * 检查当前用户是否拥有工单管理页权限
 * @returns {Promise<boolean>} 返回 true 或 false
 */
export const checkManagementPermission = async () => {
  try {
    const response = await requestManagementPermission();
    
    // 检查响应是否成功且ret===0
    if (response && response.resp_common && response.resp_common.ret === 0) {
      return response.has_permission || false;
    }
    
    // 请求失败，记录错误日志
    console.error('检查工单管理权限失败:', response);
    return false;
  } catch (error) {
    // 网络错误或其他异常，记录错误日志
    console.error('检查工单管理权限异常:', error);
    return false;
  }
};
