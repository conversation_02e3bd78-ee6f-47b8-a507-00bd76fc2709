import { requestManageOrder } from '../request/api';

/**
 * 获取并处理工单管理列表的数据
 * @param {Object} params - 对应 GetManageOrdersReq 结构的对象
 * @param {number} params.page - 页码
 * @param {number} params.page_size - 每页数量
 * @param {string} params.order_id - 工单号
 * @param {string} params.title - 工单标题
 * @param {string} params.applied_start_date - 申请开始日期
 * @param {string} params.applied_end_date - 申请结束日期
 * @param {Array<string>} params.order_types - 工单类型数组
 * @param {Array<string>} params.statuses - 工单状态数组
 * @param {Array<string>} params.applicants - 申请人邮箱数组
 * @param {Array<string>} params.ops_leads - 运维负责人邮箱数组
 * @param {Array<string>} params.cc_list - 抄送人邮箱数组
 * @param {string} params.total_duration_filter - 总耗时筛选
 * @param {string} params.node_stay_duration_filter - 当前节点停留时长筛选
 * @param {string} params.sort_by - 排序字段
 * @param {string} params.sort_order - 排序顺序
 * @returns {Promise<Object>} 返回 { success: true, data: GetManageOrdersResp } 或 { success: false, message: '...' }
 */
export const getManageOrders = async (params) => {
  try {
    const response = await requestManageOrder(params);
    
    // 检查API响应是否成功且ret===0
    if (response && response.resp_common && response.resp_common.ret === 0) {
      return {
        success: true,
        data: {
          orders: response.orders || [],
          total: response.total || 0,
          available_order_types: response.available_order_types || [],
          available_applicants: response.available_applicants || [],
          available_ops_leads: response.available_ops_leads || [],
          available_cc_users: response.available_cc_users || []
        }
      };
    }
    
    // API返回错误
    const errorMessage = response?.resp_common?.msg || '获取工单列表失败';
    console.error('获取工单管理列表失败:', response);
    return {
      success: false,
      message: errorMessage
    };
  } catch (error) {
    // 网络错误或其他异常
    console.error('获取工单管理列表异常:', error);
    return {
      success: false,
      message: '网络请求失败，请稍后重试'
    };
  }
};
