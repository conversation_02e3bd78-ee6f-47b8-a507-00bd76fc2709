/**
 * 评论服务层
 * 封装评论相关的API调用，作为组件与API层的桥梁
 * 为未来添加数据转换、缓存等前端业务逻辑预留空间
 */

import * as api from '../request/api';

/**
 * 获取评论列表
 * @param {string} orderId - 工单ID
 * @param {number} cursor - 游标，上次查询结果中最后一条评论的comment_id，首次查询时传0
 * @param {number} limit - 每页数量
 * @returns {Promise} API响应
 */
export const fetchComments = (orderId, cursor = 0, limit = 10) => {
  return api.fetchComments(orderId, cursor, limit);
};

/**
 * 发表新评论
 * @param {string} orderId - 工单ID
 * @param {string} text - 评论内容
 * @param {number} currentStage - 当前阶段
 * @returns {Promise} API响应
 */
export const postComment = (orderId, text, currentStage) => {
  return api.postComment(orderId, text, currentStage);
};

// 预留：未来可在此添加数据转换、缓存、本地存储等业务逻辑 