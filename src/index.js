import ReactDOM from "react-dom";
// 引入全局样式
// import "./style.css";
import "antd/dist/antd.min.css";
import GlobalStyle from '@/style.js';

import RootRouter from "@/router/root";
// 暴露版本号到全局，兜底（可被 .env 覆盖）
if (!process.env.REACT_APP_VERSION) {
  // eslint-disable-next-line no-undef
  process.env.REACT_APP_VERSION = "v0.1.0";
}

ReactDOM.render(
    <>
        <GlobalStyle />
        <RootRouter />
    </>,
    document.getElementById("root")
);
