// 工单管理模块相关常量定义

// 工单状态枚举（英文到中文映射）
export const ORDER_STATUS = {
  // 小写英文状态映射
  'approving': '审批中',
  'completed': '已完结',
  'rejected': '已驳回',
  'failed': '已失败',
  'pending': '待审批',
  'processing': '处理中',
  'cancelled': '已取消',
  // 大写英文状态映射（兼容）
  'APPROVING': '审批中',
  'COMPLETED': '已完结',
  'REJECTED': '已驳回',
  'FAILED': '已失败',
  'PENDING': '待审批',
  'PROCESSING': '处理中',
  'CANCELLED': '已取消',
  // 中文状态映射（兼容）
  '审批中': '审批中',
  '已完结': '已完结',
  '已驳回': '已驳回',
  '已失败': '已失败',
  '待审批': '待审批',
  '处理中': '处理中',
  '已取消': '已取消'
};

// 工单状态映射（前端显示值到后端值）
export const ORDER_STATUS_MAP = {
  // 仅保留四种状态的映射，确保与后端参数一致
  '审批中': 'approving',
  '已完结': 'completed',
  '已驳回': 'rejected',
  '已失败': 'failed'
};

// 耗时筛选选项
export const DURATION_FILTER_OPTIONS = {
  TOTAL_DURATION: {
    OVER_1D: 'over_1d',
    OVER_3D: 'over_3d', 
    OVER_1W: 'over_1w'
  },
  NODE_STAY_DURATION: {
    OVER_12H: 'over_12h',
    OVER_1D: 'over_1d',  // 将OVER_24H改为OVER_1D
    OVER_3D: 'over_3d'
  }
};

// 耗时筛选选项显示文本
export const DURATION_FILTER_LABELS = {
  TOTAL_DURATION: {
    [DURATION_FILTER_OPTIONS.TOTAL_DURATION.OVER_1D]: '超过1天',
    [DURATION_FILTER_OPTIONS.TOTAL_DURATION.OVER_3D]: '超过3天',
    [DURATION_FILTER_OPTIONS.TOTAL_DURATION.OVER_1W]: '超过1周'
  },
  NODE_STAY_DURATION: {
    [DURATION_FILTER_OPTIONS.NODE_STAY_DURATION.OVER_12H]: '超过12小时',
    [DURATION_FILTER_OPTIONS.NODE_STAY_DURATION.OVER_1D]: '超过1天',  // 将超过24小时改为超过1天
    [DURATION_FILTER_OPTIONS.NODE_STAY_DURATION.OVER_3D]: '超过3天'
  }
};

// 排序字段
export const SORT_FIELDS = {
  APPLY_TIME: 'apply_time',
  LATEST_UPDATE_TIME: 'latest_update_time',
  TOTAL_DURATION: 'total_duration',
  CURRENT_NODE_STAY_DURATION: 'current_node_stay_duration'
};

// 排序顺序
export const SORT_ORDER = {
  ASC: 'asc',
  DESC: 'desc'
};

// SLA等级
export const SLA_LEVEL = {
  NORMAL: 'normal',
  WARNING: 'warning', 
  CRITICAL: 'critical'
};

// 默认分页配置
export const DEFAULT_PAGINATION = {
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
};

// 默认排序配置
export const DEFAULT_SORTER = {
  field: SORT_FIELDS.APPLY_TIME,
  order: SORT_ORDER.DESC
};
