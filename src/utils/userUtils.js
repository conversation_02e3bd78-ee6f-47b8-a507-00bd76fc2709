import Cookies from 'js-cookie';

/**
 * 用户信息工具函数
 */

/**
 * 从JWT token中解析用户信息
 * @param {string} token - JWT token
 * @returns {Object|null} 解析出的用户信息，包含 UserEmail 和 UserName
 */
const parseJWTToken = (token) => {
  try {
    if (!token) return null;

    // JWT token 由三部分组成，用.分隔：header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) return null;

    // 解析payload部分（第二部分）
    const payload = parts[1];

    // 添加必要的padding
    let paddedPayload = payload;
    while (paddedPayload.length % 4) {
      paddedPayload += '=';
    }

    // Base64 URL decode
    const decoded = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));

    // 解析JSON
    const userInfo = JSON.parse(decoded);

    return userInfo;
  } catch (error) {
    console.error('解析JWT token失败:', error);
    return null;
  }
};

/**
 * 获取当前登录用户的信息
 * @returns {Object} 用户信息对象
 */
export const getCurrentUser = () => {
  // 从Cookie获取用户邮箱
  const userEmail = Cookies.get('user_email');

  // 暂时直接使用邮箱前缀作为用户名，避免JWT解析中文编码问题
  let userName = '未知用户';

  if (userEmail) {
    // 从邮箱中提取用户名部分
    const emailPrefix = userEmail.split('@')[0];
    userName = emailPrefix || '未知用户';
  }

  return {
    email: userEmail || '',
    name: userName,
    fullInfo: { UserEmail: userEmail, UserName: userName }
  };
};

/**
 * 获取当前用户的显示名称
 * @returns {string} 用户显示名称
 */
export const getCurrentUserName = () => {
  const user = getCurrentUser();
  return user.name;
};

/**
 * 获取当前用户的邮箱
 * @returns {string} 用户邮箱
 */
export const getCurrentUserEmail = () => {
  const user = getCurrentUser();
  return user.email;
};
