export function getSubdomain(domain) {
    try {
        const ipAddressReg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
        // 若为 IP 地址、localhost，则直接返回
        if (ipAddressReg.test(domain) || domain === 'localhost') {
            return domain
        }
        let items = domain.split(".").slice()
        return items.slice(items.length - 2).join(".")
    } catch (e) {
        return domain
    }
}

/**
 * 对字符串进行去空格处理，如果去除空格后为空字符串或非字符串类型，则返回空字符串。
 * @param {string} str - 需要处理的字符串。
 * @returns {string} 处理后的字符串。
 */
export function trimAndHandleEmpty(str) {
  if (typeof str !== 'string' || str === null || str === undefined) {
    return ''; // 非字符串或null/undefined时返回空字符串
  }
  const trimmedStr = str.trim(); // 去除字符串两端空格
  return trimmedStr === '' ? '' : trimmedStr; // 如果去空格后为空，返回空字符串，否则返回去空格后的字符串
}