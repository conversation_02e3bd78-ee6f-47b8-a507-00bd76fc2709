export function deepClone(obj, hash = new WeakMap()) {
    if (obj === null) return obj; //如果是null，则不需要做任何的操作，返回即可
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof RegExp) return new RegExp(obj);
    if (typeof obj !== 'object') return obj;//如果是函数直接返回即可，没必要进行拷贝
    //上面是将不是object的直接返回出去
    if (hash.get(obj)) return hash.get(obj);//这里主要是剪枝的作用
    let cloneObj = new obj.constructor()
    //obj,constructor指向的是自己，相当于重新生成了一份新的自己给新的对象，这就实现了两个对象的属性拥有不同的地址
    hash.set(obj, cloneObj)//以键值对的方式存放,这也保证了唯一性
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            //如果某个属性A下面还有属性B，则进入属性B，属性B处理完以后A就算处理完了，继续指向for in循环
            cloneObj[key] = deepClone(obj[key], hash)
        }
    }
    return cloneObj
}