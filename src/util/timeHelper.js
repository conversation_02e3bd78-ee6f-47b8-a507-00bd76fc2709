export default function sleep(delay) {
    var start = (new Date()).getTime();
    while ((new Date()).getTime() - start < delay) {
        continue;
    }
}

/**
 * 日期处理工具函数
 * 用于处理日期转换、校验等操作
 */

/**
 * 将日期对象转换为ISO 8601字符串格式或返回null
 * @param {Date|string|null} date - 输入的日期对象、字符串或null
 * @returns {string|null} ISO 8601格式的日期字符串或null
 */
export const toISOStringOrNull = (date) => {
  if (!date) {
    return null;
  }
  
  try {
    // 如果已经是字符串格式，直接返回
    if (typeof date === 'string') {
      // 验证是否为有效的日期字符串
      const parsedDate = new Date(date);
      if (isNaN(parsedDate.getTime())) {
        return null;
      }
      return parsedDate.toISOString();
    }
    
    // 如果是Date对象
    if (date instanceof Date) {
      if (isNaN(date.getTime())) {
        return null;
      }
      return date.toISOString();
    }
    
    // 其他情况返回null
    return null;
  } catch (error) {
    console.warn('Date conversion error:', error);
    return null;
  }
};

/**
 * 校验日期范围是否有效（结束日期不能早于开始日期）
 * @param {Date|string|null} startDate - 开始日期
 * @param {Date|string|null} endDate - 结束日期
 * @returns {boolean} 日期范围是否有效
 */
export const isValidDateRange = (startDate, endDate) => {
  // 如果任一日期为空，认为是有效的（允许单边搜索）
  if (!startDate || !endDate) {
    return true;
  }
  
  try {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // 检查日期是否有效
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return false;
    }
    
    // 结束日期不能早于开始日期
    return end >= start;
  } catch (error) {
    console.warn('Date range validation error:', error);
    return false;
  }
};

/**
 * 格式化日期为用户友好的显示格式
 * @param {Date|string} date - 输入日期
 * @param {string} format - 格式类型 ('date', 'datetime', 'time')
 * @returns {string} 格式化后的日期字符串
 */
export const formatDateForDisplay = (date, format = 'datetime') => {
  if (!date) {
    return '';
  }
  
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      return '';
    }
    
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');
    
    switch (format) {
      case 'date':
        return `${year}-${month}-${day}`;
      case 'time':
        return `${hours}:${minutes}:${seconds}`;
      case 'datetime':
      default:
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  } catch (error) {
    console.warn('Date formatting error:', error);
    return '';
  }
};