import { SLA_LEVEL } from '../constants/order_constants';

/**
 * 根据工单的SLA等级返回对应的CSS类名
 * @param {Object} record - 工单记录对象
 * @param {string} record.sla_level - SLA等级 ('normal', 'warning', 'critical')
 * @param {string} record.status - 工单状态
 * @returns {string} CSS类名
 */
export const getRowClassName = (record) => {
  // 只对审批中的工单进行SLA高亮
  // 支持英文和中文状态判断
  const isApproving = record.status === 'approving' || record.status === '审批中';
  
  // 调试信息
  console.log('SLA Debug:', {
    order_id: record.order_id,
    status: record.status,
    isApproving,
    sla_level: record.sla_level
  });
  
  if (!isApproving) {
    return '';
  }

  switch (record.sla_level) {
    case SLA_LEVEL.CRITICAL:
      console.log('Applying sla-critical class to', record.order_id);
      return 'sla-critical';
    case SLA_LEVEL.WARNING:
      console.log('Applying sla-warning class to', record.order_id);
      return 'sla-warning';
    case SLA_LEVEL.NORMAL:
    default:
      return '';
  }
};
