import { Modal } from "antd";
import { CheckCircleTwoTone } from "@ant-design/icons";

import "@/common/global";

function navigateModal(navigateHandle, targetUrlSuffix, secondsToGo) {
    let navigateOrNot = 1;
    const modal = Modal.confirm({
        icon: <CheckCircleTwoTone twoToneColor="#52c41a" />,
        title: "订单提交成功",
        content: `即将跟踪工单，${secondsToGo}秒。`,
        cancelText: "取消跳转",
        onCancel() {
            navigateOrNot = 0;
        },
        onOk() {
            clearInterval(timer);
            modal.destroy();
            global.programRedirect = true;
            navigateHandle(targetUrlSuffix);
        }
    });
    const timer = setInterval(() => {
        secondsToGo -= 1;
        modal.update({
            content: `即将跟踪工单，${secondsToGo}秒。`,
        });
    }, 1000);
    setTimeout(() => {
        clearInterval(timer);
        modal.destroy();
        global.programRedirect = true;
        if (navigateOrNot === 1) {
            navigateHandle(targetUrlSuffix);
        }
    }, secondsToGo * 1000);
}
export default navigateModal;