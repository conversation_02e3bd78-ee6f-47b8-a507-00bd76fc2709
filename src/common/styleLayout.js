import styled from "styled-components";
import { Form, Pagination } from "antd";

export const SideContent = styled.div`
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
`

export const ShadeForm = styled(Form)`
    height: 100%;
    width: 60%;
    padding: 1.5vw;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 10px 20px 0 rgba(153, 153, 153, 0.25);
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;
`

export const OrderTitle = styled.span`
    width: 100%;  
    font-size: x-large;
    font-weight: bolder;
    margin-bottom: 0.5vw;  
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
`
export const OrderWideRow = styled.div`
    width: 100%;   
    /* height :100% ; */
    /* max-height: 2vw; */
    margin: 0.58vw 0vw;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    white-space: nowrap;
`

export const OrderRow = styled.div`
    width: 100%;   
    /* height :100% ; */
    /* max-height: 2vw; */
    margin: 0.35vw 0vw;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    white-space: nowrap;
`
export const OrderTextRow = styled.div`
    width: 100%;   
    /* height :100% ; */
    margin: 0.45vw 0vw;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    white-space: nowrap;
`

export const OrderCompactRow = styled.div`
    width: 100%;
    height: 100%;
    max-height: 2.5vw;
    margin: 0.1vw 0vw;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    white-space: nowrap;
`

export const OrderPagination = styled(Pagination)`
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
`

export const OrderSubmit = styled.div`
  margin-top: 0.5vw;
  display: flex;
  width: 100%;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-around;
  align-items: center;
`
export const OrderCol = styled.div`
    width:100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    margin:0 0.25vw;
`

export const OrderHalfCol = styled.div`
    width: 50%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    margin:0 0.25vw;
`
export const OrderCenterCol = styled.div`
    width:100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    margin:0 0.25vw;
`
export const OrderStartCol = styled.div`
    width:100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;
    margin:0 0.25vw;
`
export const OrderEndCol = styled.div`
    width:100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-end;
    align-items: center;
    margin:0 0.25vw;
`