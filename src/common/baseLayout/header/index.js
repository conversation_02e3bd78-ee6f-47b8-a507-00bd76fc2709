import { Component } from "react";
import { Layout, <PERSON>u, <PERSON>over, Button, Modal, Input, InputNumber, message, Dropdown } from "antd";
import { NavLink } from "react-router-dom";
import Cookies from "js-cookie";

import styled, { createGlobalStyle } from "styled-components";
import { UserOutlined, PoweroffOutlined } from "@ant-design/icons";

import { headerRules } from "@/router/headerRouter";
import withRouter from "@/util/withRouter";
import { NewOrderUrlSuffix, DoingOrderUrlSuffix, DoneOrderUrlSuffix, AuditOrderUrlSuffix, ManageOrderUrlSuffix } from "@/global";
import { requestNewOrRefreshLongTermToken, requestViewLongTermToken, requestManagementPermission } from "@/request/api";
import "@/common/global";
import { Domain } from "@/common/wildcardlDomain";

const { Header } = Layout;
// css-js start ↓↓↓
const WfoHeader = styled(Header)`
    padding: 12px 24px;
    height: 64px;
    background: #ffffff;
    border-bottom: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    line-height: normal; /* 覆盖 antd Header 默认 64px 行高，避免子元素按整头部行高对齐 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
`
const Logo = styled.img`
    float: left;
    width: 190px;
    height: 100%;
    padding: 15px 30px 15px 0;
    object-fit: contain;
    filter: brightness(0);
    cursor: pointer;
`;
// 文字品牌样式
const Brand = styled.div`
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    font-size: 18px;
    color: #1a1a1a;
    cursor: pointer;
`;
const VersionTag = styled.span`
    padding: 0 6px;
    font-size: 12px;
    font-weight: 600;
    color: #3b6ef5;
`;
// 全局 Select 下拉弹窗风格（统一全站，包括分页每页数量下拉等）
const SelectDropdownStyle = createGlobalStyle`
  .ant-select-dropdown {
    border-radius: 12px !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.12), 0 4px 12px rgba(0,0,0,0.08) !important;
    padding: 6px 0 !important;
  }
  .ant-select-item {
    padding: 10px 12px;
    font-size: 14px;
  }
  .ant-select-item-option-active {
    background: #f5f7fb !important;
  }
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background: #eaf2ff !important;
  }
`;
// 头部下拉菜单全局样式（现代简约）
const DropdownStyle = createGlobalStyle`
  .wfo-header-dropdown .ant-dropdown-menu {
    padding: 8px;
    border-radius: 12px;
    border: none;
    box-shadow: 0 12px 32px rgba(0,0,0,0.12), 0 4px 12px rgba(0,0,0,0.08);
    min-width: 200px;
  }
  .wfo-header-dropdown .ant-dropdown-menu-item {
    padding: 10px 14px;
    border-radius: 8px;
    font-size: 14px; /* 略小一点，和头部Tab一致 */
    font-weight: 400; /* 明确不加粗 */
    color: #1a1a1a;
  }
  .wfo-header-dropdown .ant-dropdown-menu-title-content,
  .wfo-header-dropdown .ant-dropdown-menu-item a {
    font-size: 14px;
    font-weight: 400;
    color: #1a1a1a;
  }
  .wfo-header-dropdown .ant-dropdown-menu-item:not(:last-child){
    margin-bottom: 4px;
  }
  .wfo-header-dropdown .ant-dropdown-menu-item:hover {
    background: #f5f7fb;
    color: #1a1a1a;
  }
  .wfo-header-dropdown .ant-dropdown-menu-item:active {
    background: #eaf2ff;
  }
  .wfo-header-dropdown .ant-dropdown-menu-item-divider {
    margin: 6px 0;
    background-color: #f0f2f5;
  }
`;
// 统一行高容器，确保胶囊上下留白
const HeaderRow = styled.div`
    height: 40px; /* 小于 Header 64px，留白更大 */
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 32px; /* 品牌与导航间距 */
    flex: 1 1 auto; /* 占据剩余空间，但不强制 100% 宽度 */
    min-width: 0;
`;
const UserBox = styled.div`
    float: right;
    width: 210px;
    height: 100%;
    font-size: medium;
    color: #1a1a1a;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
`
const UserDiv = styled.div`
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
`

const UserLogo = styled(UserOutlined)`
    display: flex;
    align-items: center;
    font-size: x-large;
`
const UserContentDiv = styled.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
`
const UserContentButton = styled(Button)`
    width: 150px;
    margin: 3px 0px;
`
const UserTokenDiv = styled.div`
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
`
const UserTokenRow = styled.div`
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    white-space: nowrap;
    margin-top: 10px;
`
// css-js end   ↑↑↑





class HeaderNav extends Component {
    componentDidMount() {
        // 设置当前选中导航为真实地址（首页保持在“/”）
        const current = this.props.location.pathname || "/";
        this.setState({ selectNav: [current] });
        // 首屏检查工单管理权限，用于控制是否展示“工单管理”Tab
        this.checkManagePermission();
    }
    componentDidUpdate(prevProps) {
        // 当路由变化时，同步高亮
        if (prevProps.location.pathname !== this.props.location.pathname) {
            const current = this.props.location.pathname || "/";
            if (this.state.selectNav[0] !== current) {
                this.setState({ selectNav: [current] });
            }
        }
    }
    state = {
        selectNav: ["/"],
        newOrRefreshTokenVisible: false,
        validDay: 30,
        applyMsg: "",
        // 管理页权限，默认false以避免无权限用户短暂看到Tab
        hasManagePermission: false,
    }
    // 检查是否有工单管理权限，并在无权限时隐藏Tab且必要时重定向
    checkManagePermission = async () => {
        try {
            const resp = await requestManagementPermission();
            const ok = !!(resp && resp.resp_common && resp.resp_common.ret === 0 && (resp.has_permission === true || (resp.data && resp.data.has_permission === true)));
            this.setState({ hasManagePermission: ok }, () => {
                if (!ok && this.props.location.pathname === ManageOrderUrlSuffix) {
                    // 当前在管理页但无权限，重定向到工单申请
                    this.props.navigate(NewOrderUrlSuffix);
                }
            });
        } catch (e) {
            // 出错时按无权限处理，避免泄露入口
            this.setState({ hasManagePermission: false }, () => {
                if (this.props.location.pathname === ManageOrderUrlSuffix) {
                    this.props.navigate(NewOrderUrlSuffix);
                }
            });
        }
    }
    handleNewOrRefreshClick = () => {
        this.setState(
            { newOrRefreshTokenVisible: true }
        )
    }
    handleValiDayChange = (value) => {
        this.setState({
            validDay: value
        })
    }
    handleApplyMsgChange = (e) => {
        this.setState({
            applyMsg: e.target.value
        })
    }
    handleNewOrRefreshCancel = () => {
        this.setState(
            { newOrRefreshTokenVisible: false }
        )
    }
    handleNewOrRefreshOk = () => {
        if (this.state.applyMsg.length <= 10) {
            message.error("申请理由、用途简述需要超过10个字符。")
            return
        }
        let args = {
            valid_day: this.state.validDay,
            apply_msg: this.state.applyMsg
        }
        requestNewOrRefreshLongTermToken(args).then((data) => {
            if (data !== null) {
                message.success("申请已成功提交！")
            }
            this.setState({
                applyMsg: "",
                validDay: 30
            })
        })
        this.handleNewOrRefreshCancel()
    }
    handleViewTokenClick = () => {
        requestViewLongTermToken({}).then((data) => {
            if (data !== null) {
                Modal.success({
                    content: '查看申请已提交，请前往飞书·运维工单操作查收。',
                });
            }
        })
    }
    handleTopNavClick = (path) => {
        this.setState({ selectNav: [path] });
        this.props.navigate(path);
    }
    logOut = () => {
        let cookieAttributes = { domain: Domain }
        // console.log("wfo logout, domain:", Domain)
        Cookies.remove("sid", cookieAttributes);
        Cookies.remove("token", cookieAttributes);
        Cookies.remove("user_email", cookieAttributes);
        // console.log("wfo logout, after token:", Cookies.get("token"))
        window.location.replace(
            process.env.REACT_APP_OA_HOST +
            "/r/w?cmd=API_com.cm.cheetah.httpapi.login&forward=" +
            process.env.REACT_APP_HOST +
            "/login"
        );
    }
    render() {
        const appVersion = process.env.REACT_APP_VERSION || "v0.1.0";
        return (
            <WfoHeader>
                <DropdownStyle />
                <SelectDropdownStyle />
                {/* 顶部主导航（严格对齐你给的样式） */}
                <HeaderRow>
                    {/* 左侧品牌：文字标志 + 版本号 */}
                    <Brand onClick={() => this.handleTopNavClick("/")}>
                        <span>工单管理平台</span>
                        <VersionTag>{appVersion}</VersionTag>
                    </Brand>
                    {/* 一级导航 */}
                    <nav style={{ display: "flex", gap: 12, alignItems: "center", padding: "6px 0" }}>
                        
                        {/* 通用工单 */}
                        <div
                            onClick={() => this.handleTopNavClick("/new-order/common")}
                            style={{
                                padding: "10px 18px",
                                borderRadius: 16,
                                backgroundColor: this.state.selectNav[0] === "/new-order/common" ? "#4d8dfb" : "transparent",
                                color: this.state.selectNav[0] === "/new-order/common" ? "#ffffff" : "#666",
                                cursor: "pointer",
                                transition: "all 0.2s ease",
                                display: "flex",
                                alignItems: "center",
                                gap: 4,
                                userSelect: "none",
                                fontWeight: 400,
                                fontSize: 14
                            }}
                            onMouseEnter={(e)=>{ if(this.state.selectNav[0] !== "/new-order/common"){ e.currentTarget.style.backgroundColor="#f0f2f5"; e.currentTarget.style.color="#1a1a1a"; } }}
                            onMouseLeave={(e)=>{ if(this.state.selectNav[0] !== "/new-order/common"){ e.currentTarget.style.backgroundColor="transparent"; e.currentTarget.style.color="#666"; } }}
                        >
                            通用工单
                        </div>
                        <Dropdown overlay={
                            <Menu onClick={({ key }) => this.handleTopNavClick(key)}>
                                <Menu.Item key="/new-order/cdn-flush">CDN刷新</Menu.Item>
                                <Menu.Item key="/new-order/cdn-flush-history">CDN刷新历史</Menu.Item>
                            </Menu>
                        } placement="bottomLeft" trigger={["click"]} overlayClassName="wfo-header-dropdown">
                            <div style={{ padding: "10px 18px", borderRadius: 16, background: this.state.selectNav[0]?.startsWith("/new-order/cdn") ? "#4d8dfb" : "transparent", color: this.state.selectNav[0]?.startsWith("/new-order/cdn") ? "#fff" : "#666", cursor: "pointer", transition: "all 0.2s ease", userSelect: "none", fontWeight: 400, fontSize: 14, display: "flex", alignItems: "center", gap: 4 }} onMouseEnter={(e)=>{ if(!this.state.selectNav[0]?.startsWith("/new-order/cdn")){ e.currentTarget.style.backgroundColor="#f0f2f5"; e.currentTarget.style.color="#1a1a1a"; } }} onMouseLeave={(e)=>{ if(!this.state.selectNav[0]?.startsWith("/new-order/cdn")){ e.currentTarget.style.backgroundColor="transparent"; e.currentTarget.style.color="#666"; } }}>CDN管理 ▼</div>
                        </Dropdown>
                        <div onClick={() => this.handleTopNavClick("/new-order/server-account")} style={{ padding: "10px 18px", borderRadius: 16, background: this.state.selectNav[0] === "/new-order/server-account" ? "#4d8dfb" : "transparent", color: this.state.selectNav[0] === "/new-order/server-account" ? "#fff" : "#666", cursor: "pointer", transition: "all 0.2s ease", userSelect: "none", fontWeight: 400, fontSize: 14, display: "flex", alignItems: "center", gap: 4 }} onMouseEnter={(e)=>{ if(this.state.selectNav[0] !== "/new-order/server-account"){ e.currentTarget.style.backgroundColor="#f0f2f5"; e.currentTarget.style.color="#1a1a1a"; } }} onMouseLeave={(e)=>{ if(this.state.selectNav[0] !== "/new-order/server-account"){ e.currentTarget.style.backgroundColor="transparent"; e.currentTarget.style.color="#666"; } }}>账号申请</div>
                        <div onClick={() => this.handleTopNavClick("/new-order/k8stoken")} style={{ padding: "10px 18px", borderRadius: 16, background: this.state.selectNav[0] === "/new-order/k8stoken" ? "#4d8dfb" : "transparent", color: this.state.selectNav[0] === "/new-order/k8stoken" ? "#fff" : "#666", cursor: "pointer", transition: "all 0.2s ease", userSelect: "none", fontWeight: 400, fontSize: 14, display: "flex", alignItems: "center", gap: 4 }} onMouseEnter={(e)=>{ if(this.state.selectNav[0] !== "/new-order/k8stoken"){ e.currentTarget.style.backgroundColor="#f0f2f5"; e.currentTarget.style.color="#1a1a1a"; } }} onMouseLeave={(e)=>{ if(this.state.selectNav[0] !== "/new-order/k8stoken"){ e.currentTarget.style.backgroundColor="transparent"; e.currentTarget.style.color="#666"; } }}>Token申请</div>
                        <div onClick={() => this.handleTopNavClick("/new-order/dns")} style={{ padding: "10px 18px", borderRadius: 16, background: this.state.selectNav[0] === "/new-order/dns" ? "#4d8dfb" : "transparent", color: this.state.selectNav[0] === "/new-order/dns" ? "#fff" : "#666", cursor: "pointer", transition: "all 0.2s ease", userSelect: "none", fontWeight: 400, fontSize: 14, display: "flex", alignItems: "center", gap: 4 }} onMouseEnter={(e)=>{ if(this.state.selectNav[0] !== "/new-order/dns"){ e.currentTarget.style.backgroundColor="#f0f2f5"; e.currentTarget.style.color="#1a1a1a"; } }} onMouseLeave={(e)=>{ if(this.state.selectNav[0] !== "/new-order/dns"){ e.currentTarget.style.backgroundColor="transparent"; e.currentTarget.style.color="#666"; } }}>域名解析</div>
                        <div onClick={() => this.handleTopNavClick("/new-order/sql")} style={{ padding: "10px 18px", borderRadius: 16, background: this.state.selectNav[0] === "/new-order/sql" ? "#4d8dfb" : "transparent", color: this.state.selectNav[0] === "/new-order/sql" ? "#fff" : "#666", cursor: "pointer", transition: "all 0.2s ease", userSelect: "none", fontWeight: 400, fontSize: 14, display: "flex", alignItems: "center", gap: 4 }} onMouseEnter={(e)=>{ if(this.state.selectNav[0] !== "/new-order/sql"){ e.currentTarget.style.backgroundColor="#f0f2f5"; e.currentTarget.style.color="#1a1a1a"; } }} onMouseLeave={(e)=>{ if(this.state.selectNav[0] !== "/new-order/sql"){ e.currentTarget.style.backgroundColor="transparent"; e.currentTarget.style.color="#666"; } }}>SQL审计执行</div>
                        <Dropdown overlay={
                            <Menu onClick={({ key }) => this.handleTopNavClick(key)}>
                                <Menu.Item key="/new-order/server">云服务器申请</Menu.Item>
                                <Menu.Item key="/new-order/redis">Redis申请</Menu.Item>
                                <Menu.Item key="/new-order/mysql">Mysql申请</Menu.Item>
                                <Menu.Item key="/new-order/domain">域名申请</Menu.Item>
                                <Menu.Divider />
                                <Menu.Item key="/new-order/resource-del">资源销毁</Menu.Item>
                                <Menu.Item key="/new-order/service_owner/update">资源变更</Menu.Item>
                            </Menu>
                        } placement="bottomLeft" trigger={["click"]} overlayClassName="wfo-header-dropdown">
                            <div style={{ padding: "10px 18px", borderRadius: 16, background: ["/new-order/server","/new-order/redis","/new-order/mysql","/new-order/domain","/new-order/resource-del","/new-order/service_owner/update"].includes(this.state.selectNav[0]) ? "#4d8dfb" : "transparent", color: ["/new-order/server","/new-order/redis","/new-order/mysql","/new-order/domain","/new-order/resource-del","/new-order/service_owner/update"].includes(this.state.selectNav[0]) ? "#fff" : "#666", cursor: "pointer", transition: "all 0.2s ease", userSelect: "none", fontWeight: 400, fontSize: 14, display: "flex", alignItems: "center", gap: 4 }} onMouseEnter={(e)=>{ if(!["/new-order/server","/new-order/redis","/new-order/mysql","/new-order/domain","/new-order/resource-del","/new-order/service_owner/update"].includes(this.state.selectNav[0])){ e.currentTarget.style.backgroundColor="#f0f2f5"; e.currentTarget.style.color="#1a1a1a"; } }} onMouseLeave={(e)=>{ if(!["/new-order/server","/new-order/redis","/new-order/mysql","/new-order/domain","/new-order/resource-del","/new-order/service_owner/update"].includes(this.state.selectNav[0])){ e.currentTarget.style.backgroundColor="transparent"; e.currentTarget.style.color="#666"; } }}>资源管理 ▼</div>
                        </Dropdown>
                        <div onClick={() => this.handleTopNavClick("/new-order/cmdb/model-manager")} style={{ padding: "10px 18px", borderRadius: 16, background: this.state.selectNav[0] === "/new-order/cmdb/model-manager" ? "#4d8dfb" : "transparent", color: this.state.selectNav[0] === "/new-order/cmdb/model-manager" ? "#fff" : "#666", cursor: "pointer", transition: "all 0.2s ease", userSelect: "none", fontWeight: 400, fontSize: 14, display: "flex", alignItems: "center", gap: 4 }} onMouseEnter={(e)=>{ if(this.state.selectNav[0] !== "/new-order/cmdb/model-manager"){ e.currentTarget.style.backgroundColor="#f0f2f5"; e.currentTarget.style.color="#1a1a1a"; } }} onMouseLeave={(e)=>{ if(this.state.selectNav[0] !== "/new-order/cmdb/model-manager"){ e.currentTarget.style.backgroundColor="transparent"; e.currentTarget.style.color="#666"; } }}>业务树管理</div>
                        <Dropdown overlay={
                            <Menu onClick={({ key }) => this.handleTopNavClick(key)}>
                                <Menu.Item key="/doing-order">进行中工单</Menu.Item>
                                <Menu.Item key="/done-order">完结工单</Menu.Item>
                                <Menu.Item key="/audit-order">我的审批工单</Menu.Item>
                                {this.state.hasManagePermission && <Menu.Item key="/manage-order">工单管理</Menu.Item>}
                            </Menu>
                        } placement="bottomLeft" trigger={["click"]} overlayClassName="wfo-header-dropdown">
                            <div style={{ padding: "10px 18px", borderRadius: 16, background: ["/doing-order","/done-order","/audit-order","/manage-order"].includes(this.state.selectNav[0]) ? "#4d8dfb" : "transparent", color: ["/doing-order","/done-order","/audit-order","/manage-order"].includes(this.state.selectNav[0]) ? "#fff" : "#666", cursor: "pointer", transition: "all 0.2s ease", userSelect: "none", fontWeight: 400, fontSize: 14, display: "flex", alignItems: "center", gap: 4 }} onMouseEnter={(e)=>{ if(!["/doing-order","/done-order","/audit-order","/manage-order"].includes(this.state.selectNav[0])){ e.currentTarget.style.backgroundColor="#f0f2f5"; e.currentTarget.style.color="#1a1a1a"; } }} onMouseLeave={(e)=>{ if(!["/doing-order","/done-order","/audit-order","/manage-order"].includes(this.state.selectNav[0])){ e.currentTarget.style.backgroundColor="transparent"; e.currentTarget.style.color="#666"; } }}>工单台 ▼</div>
                        </Dropdown>
                    </nav>
                </HeaderRow>

                <UserBox>
                    <Popover
                        placement="bottom"
                        content={
                            <UserContentDiv>
                                <UserContentButton
                                    type="dashed"
                                    size="small"
                                    onClick={this.handleNewOrRefreshClick}
                                >
                                    申请/创建长效 token
                                </UserContentButton>
                                <UserContentButton
                                    type="dashed"
                                    size="small"
                                    onClick={this.handleViewTokenClick}
                                >
                                    查看我的长效 token
                                </UserContentButton>
                                <UserContentButton
                                    type="dashed"
                                    danger
                                    size="small"
                                    onClick={this.logOut}
                                >
                                    注销登录<PoweroffOutlined />
                                </UserContentButton>
                                <Modal
                                    title="申请/刷新长效token"
                                    open={this.state.newOrRefreshTokenVisible}
                                    onCancel={this.handleNewOrRefreshCancel}
                                    onOk={this.handleNewOrRefreshOk}
                                    cancelText="取消"
                                    okText="申请/刷新"
                                >
                                    <UserTokenDiv>
                                        <Input.TextArea
                                            placeholder="请简述申请理由、用途。"
                                            rows={5}
                                            value={this.state.applyMsg}
                                            onChange={this.handleApplyMsgChange}
                                        />
                                        <UserTokenRow>
                                            <span>
                                                token有效期：
                                                <InputNumber
                                                    size="small"
                                                    min={30}
                                                    max={3650}
                                                    defaultValue={30}
                                                    value={this.state.validDay}
                                                    onChange={this.handleValiDayChange}
                                                />&nbsp;/天
                                            </span>
                                            <Button size="small" type="primary" onClick={() => { this.setState({ validDay: 365 }) }}>1 年</Button>
                                            <Button size="small" type="primary" onClick={() => { this.setState({ validDay: 365 * 3 }) }}>3 年</Button>
                                            <Button size="small" type="primary" onClick={() => { this.setState({ validDay: 365 * 5 }) }}>5 年</Button>
                                            <Button size="small" type="primary" onClick={() => { this.setState({ validDay: 3650 }) }}>10年</Button>
                                        </UserTokenRow>
                                    </UserTokenDiv>
                                </Modal>
                            </UserContentDiv>

                        }
                    >
                        <UserDiv>
                            <span className="user-name">{Cookies.get("user_email")}</span>
                        </UserDiv>
                    </Popover>

                </UserBox >
                {/* 旧 Menu 顶部导航已移除 */}
            </WfoHeader >
        );
    }
}

export default withRouter(HeaderNav);
