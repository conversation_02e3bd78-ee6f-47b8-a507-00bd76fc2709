import { Component } from "react";
import { Layout } from "antd";
import styled from "styled-components";
import HeaderRouter from "@/router/headerRouter";

const { Content } = Layout;

const MainContent = styled(Content)`
  width: "100%";
  height: "100%" ;
  background: #f5f7fb;
  padding-top: 1px;
`

export default class HeadContent extends Component {
  render() {
    return (
      <MainContent >
        <div className="content-wrap" style={{ width: "100%", height: "100%" }}>
          <HeaderRouter />
        </div>
      </MainContent>
    );
  }
}
