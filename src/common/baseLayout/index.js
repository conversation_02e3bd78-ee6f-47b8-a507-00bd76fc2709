import Cookies from "js-cookie";
import { Layout } from "antd";
import { Component } from "react";
import styled from "styled-components";


import HeaderNav from "./header";
import HeadContent from "./header/content";

// css-js start ↓↓↓
const HeadNavLayout = styled(Layout)`
  width: 100%;
  height: 100%;
`
// css-js end   ↑↑↑



export default class BaseLayout extends Component {
  componentDidMount(props) {
    // 暂时禁用登录检查
    // const token = Cookies.get("token");
    // if (typeof token == "undefined") {
    //   window.location.replace(
    //     process.env.REACT_APP_OA_HOST +
    //     "/r/w?cmd=API_com.cm.cheetah.httpapi.login&forward=" +
    //     process.env.REACT_APP_HOST +
    //     "/login"
    //   );
    // }
    
    // 开发环境下设置默认cookie，模拟登录状态
    const devUserEmail = "<EMAIL>";
    Cookies.set("user_email", devUserEmail);
    Cookies.set("token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyRW1haWwiOiJ0YW5taW5neXVAY21jbS5jb20iLCJVc2VyTmFtZSI6IuiwreaYjuWuhyIsImV4cCI6MTc1NTI0MjI4OSwiaWF0IjoxNzU0NjM3NDg5LCJzdWIiOiJ3Zm8ifQ.Dy_08P5jHJq-SF4MWuygTzBO8sYd0ZscuZkOtxwuwMk");
    Cookies.set("sid", "5abc08c7-c318-478d-91f4-a01d6c6e602b");
    console.log(`开发环境：设置默认用户邮箱为 ${devUserEmail}`);
    
    console.log('已临时禁用登录检查');
  }

  render() {
    return (
      <HeadNavLayout>
        <HeaderNav />
        <HeadContent />
      </HeadNavLayout>
    );
  }
}
