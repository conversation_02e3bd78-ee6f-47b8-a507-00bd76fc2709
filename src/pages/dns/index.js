import { Component } from "react";
import { Input, Button, Select, Tag, message } from "antd";
import { UserOutlined } from "@ant-design/icons";
import styled from "styled-components";

import withRouter from "@/util/withRouter";
import { OrderEndCol, OrderRow, OrderTitle, ShadeForm, SideContent } from "@/common/styleLayout";
import { requestLeaderEmail, requestOpsMemberDutySpectrum, requestTxDNSApply, requestTxDomainCheck } from "@/request/api";
import { getSubdomain } from "@/util/strHelper";

const { TextArea } = Input;

// css-js start ↓↓↓
const DNSApplyForm = styled(ShadeForm)`
    width: 48%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
`
const ApplyMsgRow = styled(OrderRow)`
    align-items: flex-start;
`
const DNSSpan = styled.span`
    min-width: 80px;
`

const DNSButton = styled(Button)`
    min-width: 80px;
`
const DNSOpsAuditDiv = styled.div`
    width: 55%;
    display:flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
`
const PsSpan = styled.span`
    margin-top: 1vw;
    font-weight: bold;
    font-size: large;
    color: red;
`
const AuditSelect = styled(Select)`
    max-width: 15vw;
`
// css-js end   ↑↑↑



class DNS extends Component {
    state = {
        targetDomain: "",
        targetIP: "",
        RecordType: "A",
        leaderEmail: undefined,
        opsAuditEmail: [],
        opsAuditEmailSelected: undefined,
        ApplyMsg: "",
        domainCheckPassOrNot: false
    }
    componentDidMount() {
        requestLeaderEmail({}).then((data) => {
            this.setState({
                leaderEmail: data.email,
            });
        })
        requestOpsMemberDutySpectrum({}).then((data) => {
            this.setState({
                opsAuditEmail: data.duty_member_map["域名与DNS"].map((item) => { return { label: item, value: item } }),
            });
        })
    }
    handleApplyMsgChange = (e) => {
        this.setState({
            ApplyMsg: e.target.value
        })
    }
    handleDomainChange = (e) => {
        this.setState({
            targetDomain: e.target.value,
            domainCheckPassOrNot: false
        })
    }
    handleIPChange = (e) => {
        this.setState({
            targetIP: e.target.value
        })
    }
    handleOpsAuditEmailChange = (value) => {
        this.setState({
            opsAuditEmailSelected: value
        })
    }
    handleDomainCheck = () => {
        if (this.state.targetDomain === "") {
            message.error("域名不能为空！")
            return
        }
        let args = { domain: getSubdomain(this.state.targetDomain) }
        requestTxDomainCheck(args).then((data) => {
            if (data !== null) {
                if (data.exist) {
                    this.setState({
                        domainCheckPassOrNot: data.exist
                    })
                    message.success("域名校验成功!")
                } else {
                    message.error("域名校验失败!")
                }
            }
        })
    }
    handleIPBinding = () => {
        console.log(this.state)
        if (this.state.domainCheckPassOrNot !== true) {
            message.error("请先校验域名！")
            return
        }
        if (this.state.ApplyMsg.length <= 5) {
            message.error("申请理由不能少于5个字符！")
            return
        }
        if (this.state.targetIP === "") {
            message.error("ip不能为空！")
            return
        }
        if (this.state.opsAuditEmailSelected === undefined) {
            message.error("运维审批人")
            return
        }
        let args = {
            domain: this.state.targetDomain,
            record_type: this.state.RecordType,
            value: this.state.targetIP,
            ops_audit_email: this.state.opsAuditEmailSelected,
            apply_msg: this.state.ApplyMsg
        }
        requestTxDNSApply(args).then((data) => {
            if (data !== null) {
                message.success("订单已成功提交。")
            }
        })
    }
    render() {
        return (
            <SideContent>
                <DNSApplyForm>
                    <OrderTitle>DNS/域名解析</OrderTitle>
                    <OrderRow>
                        <DNSOpsAuditDiv>
                            <DNSSpan>运维审批：</DNSSpan>
                            <AuditSelect
                                placeholder="运维审批人员"
                                options={this.state.opsAuditEmail}
                                value={this.state.opsAuditEmailSelected}
                                onChange={this.handleOpsAuditEmailChange}
                            />
                        </DNSOpsAuditDiv>
                        <OrderEndCol>
                            <DNSSpan>领导审批：</DNSSpan><Tag color="#108ee9" icon={<UserOutlined />}>{this.state.leaderEmail}</Tag>
                        </OrderEndCol>
                    </OrderRow>
                    <ApplyMsgRow>
                        <DNSSpan>申请理由：</DNSSpan>
                        <TextArea
                            placeholder="请简单描述申请域名解析的理由"
                            rows={3}
                            // style={{ "width": "100px" }}
                            value={this.state.ApplyMsg}
                            onChange={this.handleApplyMsgChange}
                        />
                    </ApplyMsgRow>
                    <OrderRow>
                        <DNSSpan>步骤一：</DNSSpan>
                        <Input
                            placeholder="请输入域名，然后点击域名校验"
                            value={this.state.targetDomain}
                            onChange={this.handleDomainChange}
                        />
                        <DNSButton type="primary" onClick={this.handleDomainCheck}>校验域名</DNSButton>
                    </OrderRow>
                    <OrderRow>
                        <DNSSpan>步骤二：</DNSSpan>
                        <Input
                            placeholder="请输入IP，然后点击绑定IP"
                            value={this.state.targetIP}
                            onChange={this.handleIPChange}
                        />
                        <DNSButton type="primary" onClick={this.handleIPBinding}>绑定IP</DNSButton>
                    </OrderRow>
                    <OrderRow>
                        <PsSpan> PS：海外域名不支持自动解析，请提通用工单。</PsSpan>
                    </OrderRow>
                </DNSApplyForm>
            </SideContent>
        )
    }
}

export default withRouter(DNS)