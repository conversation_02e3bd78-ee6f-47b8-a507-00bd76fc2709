import React from 'react';
import { Card, Empty } from 'antd';
import styled from 'styled-components';

const DashboardContainer = styled.div`
  padding: 24px;
`;

const PlaceholderCard = styled(Card)`
  text-align: center;
  .ant-card-body {
    padding: 48px 24px;
  }
`;

/**
 * 报表仪表盘组件（占位实现）
 * 为迭代3的报表功能预留，当前返回固定占位内容
 */
const ReportDashboard = () => {
  return (
    <DashboardContainer>
      <PlaceholderCard title="数据洞察与报表">
        <Empty
          description="报表功能将在后续迭代中实现"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
        <p style={{ color: '#666', marginTop: 16 }}>
          预计功能包括：
        </p>
        <ul style={{ color: '#666', textAlign: 'left', display: 'inline-block' }}>
          <li>部门/类型工单量统计图表</li>
          <li>平均处理时长分析</li>
          <li>工单状态分布饼图</li>
          <li>SLA达成率趋势图</li>
          <li>处理效率排行榜</li>
        </ul>
      </PlaceholderCard>
    </DashboardContainer>
  );
};

export default ReportDashboard;
