import React, { useEffect, useCallback } from 'react';
import { Modal, Form, Select, DatePicker } from 'antd';
import styled from 'styled-components';
import { 
  ORDER_STATUS_MAP, 
  DURATION_FILTER_OPTIONS, 
  DURATION_FILTER_LABELS 
} from '../../../constants/order_constants';

const { Option } = Select;
const { RangePicker } = DatePicker;

const StyledModal = styled(Modal)`
  .ant-modal-body {
    padding: 24px;
  }
  
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  .ant-form-item-label {
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .ant-select {
    width: 100% !important;
    min-width: 200px;
  }
  
  .ant-select-selector {
    width: 100% !important;
    min-width: 200px;
  }
  
  .ant-input {
    width: 100% !important;
    min-width: 200px;
  }
`;

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  margin-bottom: 8px;
  
  .ant-form-item {
    flex: 1;
    margin-bottom: 16px;
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0;
  }
`;

/**
 * 高级筛选弹窗组件
 * 提供工单类型、状态、人员等多维度筛选功能
 */
const AdvancedFilterModal = ({
  visible,
  onCancel,
  onOk,
  initialValues = {},
  availableOptions = {}
}) => {
  const [form] = Form.useForm();

  // 使用useCallback统一设置表单值的方法
  const setFormValues = useCallback((values) => {
    if (form && values) {
      // 过滤掉时间戳字段
      const { _timestamp, ...filteredValues } = values;
      
      // 合并默认值和传入值
      const completeValues = {
        // 单选字段默认值（为空，显示placeholder文本）
        total_duration_filter: undefined,
        node_stay_duration_filter: undefined,
        // 多选字段默认值（空数组）
        order_types: [],
        statuses: [],
        applicants: [],
        ops_leads: [],
        cc_list: [],
        ...filteredValues
      };
      
      form.setFieldsValue(completeValues);
    }
  }, [form]);

  // 当弹窗显示时，设置表单值
  useEffect(() => {
    if (visible) {
      // 使用双重setTimeout确保异步设置
      setTimeout(() => {
        setTimeout(() => {
          setFormValues(initialValues);
        }, 0);
      }, 0);
    }
  }, [visible, setFormValues, initialValues]);

  // 当initialValues变化时，无论弹窗是否打开都要更新表单值
  useEffect(() => {
    if (visible) {
      setFormValues(initialValues);
    }
  }, [initialValues, visible, setFormValues]);

  // 处理确定按钮点击
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onOk && onOk(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理取消按钮点击
  const handleCancel = () => {
    form.resetFields();
    onCancel && onCancel();
  };

  // 获取工单状态选项
  const getStatusOptions = () => {
    return Object.entries(ORDER_STATUS_MAP).map(([label, value]) => (
      <Option key={value} value={value}>
        {label}
      </Option>
    ));
  };

  // 获取工单类型选项（显示中文名，提交英文码）
  const getOrderTypeOptions = () => {
    const { available_order_types = [] } = availableOptions;
    return available_order_types.map((item) => {
      if (typeof item === 'string') {
        // 兼容旧格式：字符串数组
        return (
          <Option key={item} value={item}>
            {item}
          </Option>
        );
      }
      const code = item.order_type;
      const name = item.order_type_name || code;
      return (
        <Option key={code} value={code}>
          {name}
        </Option>
      );
    });
  };

  // 获取申请人选项
  const getApplicantOptions = () => {
    const { available_applicants = [] } = availableOptions;
    return available_applicants.map(applicant => (
      <Option key={applicant} value={applicant}>
        {applicant}
      </Option>
    ));
  };

  // 获取运维负责人选项
  const getOpsLeadOptions = () => {
    const { available_ops_leads = [] } = availableOptions;
    return available_ops_leads.map(lead => (
      <Option key={lead} value={lead}>
        {lead}
      </Option>
    ));
  };

  // 获取抄送人选项
  const getCcUserOptions = () => {
    const { available_cc_users = [] } = availableOptions;
    return available_cc_users.map(user => (
      <Option key={user} value={user}>
        {user}
      </Option>
    ));
  };

  // 获取总耗时筛选选项
  const getTotalDurationOptions = () => {
    return Object.entries(DURATION_FILTER_LABELS.TOTAL_DURATION).map(([value, label]) => (
      <Option key={value} value={value}>
        {label}
      </Option>
    ));
  };

  // 获取当前节点停留时长筛选选项
  const getNodeStayDurationOptions = () => {
    return Object.entries(DURATION_FILTER_LABELS.NODE_STAY_DURATION).map(([value, label]) => (
      <Option key={value} value={value}>
        {label}
      </Option>
    ));
  };

  return (
    <StyledModal
      title="高级筛选条件"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={800}
      okText="确定"
      cancelText="取消"
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
      >
        <FormRow>
          <Form.Item
            name="order_types"
            label="工单类型"
          >
            <Select
              mode="multiple"
              placeholder="请选择工单类型"
              allowClear
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {getOrderTypeOptions()}
            </Select>
          </Form.Item>

          <Form.Item
            name="statuses"
            label="工单状态"
          >
            <Select
              mode="multiple"
              placeholder="请选择工单状态"
              allowClear
            >
              {getStatusOptions()}
            </Select>
          </Form.Item>
        </FormRow>

        <FormRow>
          <Form.Item
            name="applicants"
            label="申请人"
          >
            <Select
              mode="multiple"
              placeholder="请选择申请人"
              allowClear
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {getApplicantOptions()}
            </Select>
          </Form.Item>

          <Form.Item
            name="ops_leads"
            label="运维负责人"
          >
            <Select
              mode="multiple"
              placeholder="请选择运维负责人"
              allowClear
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {getOpsLeadOptions()}
            </Select>
          </Form.Item>
        </FormRow>

        <Form.Item
          name="cc_list"
          label="抄送人"
        >
          <Select
            mode="multiple"
            placeholder="请选择抄送人"
            allowClear
            showSearch
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {getCcUserOptions()}
          </Select>
        </Form.Item>

        <FormRow>
          <Form.Item
            name="total_duration_filter"
            label="总耗时"
          >
            <Select placeholder="请选择总耗时">
              {getTotalDurationOptions()}
            </Select>
          </Form.Item>

          <Form.Item
            name="node_stay_duration_filter"
            label="当前节点停留时长"
          >
            <Select placeholder="请选择当前节点停留时长">
              {getNodeStayDurationOptions()}
            </Select>
          </Form.Item>
        </FormRow>
      </Form>
    </StyledModal>
  );
};

export default AdvancedFilterModal;
