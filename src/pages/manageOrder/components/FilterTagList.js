import React, { useState } from 'react';
import { Tag, Button, Tooltip } from 'antd';
import styled from 'styled-components';
import { isEqual } from 'lodash';
import { 
  ORDER_STATUS, 
  DURATION_FILTER_LABELS,
  DURATION_FILTER_OPTIONS 
} from '../../../constants/order_constants';

const FilterTagContainer = styled.div`
  margin-bottom: 0; /* 移除底部边距，现在在表格上方 */
  min-height: 32px; /* 与TableHeader高度匹配 */
  height: 32px; /* 与TableHeader高度匹配 */
  display: flex;
  align-items: center; /* 垂直居中 */
  flex-wrap: nowrap; /* 单行展示，防止高度变化 */
  gap: 8px;
  overflow: hidden; /* 超出隐藏，防止挤压表格 */
`;

const FilterTag = styled(Tag)`
  margin-bottom: 8px;
  padding: 4px 12px;
  border-radius: 16px;
  cursor: ${props => props.removable ? 'pointer' : 'default'};
  max-width: 280px; /* 限制单个标签最大宽度 */
  display: inline-flex;
  align-items: center;
  .tag-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    max-width: 240px; /* 留出关闭按钮空间 */
    vertical-align: middle;
  }
  
  &.pending {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #666;
  }
  
  &.applied {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }
`;

const ExpandButton = styled(Button)`
  padding: 0;
  height: auto;
  border: none;
  box-shadow: none;
  color: #1890ff;
  font-size: 12px;
`;

const CollapsedTag = styled(FilterTag)`
  .ant-tag-close-icon {
    margin-left: 8px;
  }
  .tag-text { max-width: 200px; }
`;

/**
 * 筛选条件标签列表组件
 * 根据 pendingFilters 和 appliedFilters 渲染灰色和蓝色的标签
 */
const FilterTagList = ({ 
  pendingFilters = {}, 
  appliedFilters = {}, 
  onRemoveTag, 
  onRemoveMultiSelectItem,
  availableOptions = {}
}) => {
  const [expandedFields, setExpandedFields] = useState(new Set());

  // 判断字段是否为已生效状态（蓝色）
  const isFieldApplied = (fieldKey) => {
    return isEqual(appliedFilters[fieldKey], pendingFilters[fieldKey]);
  };

  // 切换展开/折叠状态
  const toggleExpanded = (fieldKey) => {
    const newExpanded = new Set(expandedFields);
    if (newExpanded.has(fieldKey)) {
      newExpanded.delete(fieldKey);
    } else {
      newExpanded.add(fieldKey);
    }
    setExpandedFields(newExpanded);
  };

  // 获取字段显示名称
  const getFieldDisplayName = (fieldKey) => {
    const fieldNames = {
      order_id: '工单号',
      title: '标题',
      applied_start_date: '申请开始日期',
      applied_end_date: '申请结束日期',
      order_types: '工单类型',
      statuses: '工单状态',
      applicants: '申请人',
      ops_leads: '运维负责人',
      cc_list: '抄送人',
      total_duration_filter: '总耗时',
      node_stay_duration_filter: '当前节点停留时长'
    };
    return fieldNames[fieldKey] || fieldKey;
  };

  // 根据 available_order_types 构建类型名映射（code -> name）
  const getOrderTypeNameMap = () => {
    const list = availableOptions.available_order_types || [];
    const map = {};
    list.forEach(item => {
      if (typeof item === 'string') {
        map[item] = item;
      } else if (item && typeof item === 'object') {
        const code = item.order_type;
        const name = item.order_type_name || code;
        if (code) map[code] = name;
      }
    });
    return map;
  };

  // 获取值的显示文本
  const getValueDisplayText = (fieldKey, value) => {
    if (!value) return '';

    switch (fieldKey) {
      case 'statuses':
        if (Array.isArray(value)) {
          return value.map(status => ORDER_STATUS[status] || status).join(', ');
        }
        return ORDER_STATUS[value] || value;
      
      case 'order_types': {
        const nameMap = getOrderTypeNameMap();
        if (Array.isArray(value)) {
          return value.map(code => nameMap[code] || code).join(', ');
        }
        return nameMap[value] || value;
      }
      
      case 'total_duration_filter':
        return DURATION_FILTER_LABELS.TOTAL_DURATION[value] || value;
      
      case 'node_stay_duration_filter':
        return DURATION_FILTER_LABELS.NODE_STAY_DURATION[value] || value;
      
      case 'applied_start_date':
      case 'applied_end_date':
        // 日期格式化显示
        if (typeof value === 'string') {
          try {
            const date = new Date(value);
            return date.toLocaleString('zh-CN');
          } catch (e) {
            return value;
          }
        }
        return value;
      
      case 'order_types':
      case 'applicants':
      case 'ops_leads':
      case 'cc_list':
        if (Array.isArray(value)) {
          return value.join(', ');
        }
        return value;
      
      default:
        return Array.isArray(value) ? value.join(', ') : value;
    }
  };

  // 渲染单个标签
  const renderTag = (fieldKey, value) => {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      return null;
    }

    // 耗时筛选字段现在默认值为undefined，所有有值的选择都应该显示为标签
    // 不再需要跳过任何耗时筛选值

    const isApplied = isFieldApplied(fieldKey);
    const displayName = getFieldDisplayName(fieldKey);
    const displayText = getValueDisplayText(fieldKey, value);
    const tagClass = isApplied ? 'applied' : 'pending';

    // 处理多选字段的展开/折叠
    const isMultiSelect = Array.isArray(value) && value.length > 3;
    const isExpanded = expandedFields.has(fieldKey);

    if (isMultiSelect && !isExpanded) {
      // 折叠显示：显示前3个 + "..."
      const fullValue = value.join(', ');
      const displayValue = value.slice(0, 3).join(', ') + '...';
      return (
        <CollapsedTag
          key={fieldKey}
          className={tagClass}
          closable
          onClose={() => onRemoveTag && onRemoveTag(fieldKey)}
        >
          <Tooltip title={`${displayName}: ${fullValue}`} placement="top">
            <span className="tag-text">{displayName}: {displayValue}</span>
          </Tooltip>
          <ExpandButton 
            type="link" 
            size="small"
            onClick={() => toggleExpanded(fieldKey)}
          >
            [展开]
          </ExpandButton>
        </CollapsedTag>
      );
    }

    if (isMultiSelect && isExpanded) {
      // 展开显示：每个子项都有自己的删除按钮
      return (
        <div key={fieldKey} style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
          {value.map((item, index) => (
            <FilterTag
              key={`${fieldKey}-${index}`}
              className={tagClass}
              closable
              onClose={() => onRemoveMultiSelectItem && onRemoveMultiSelectItem(fieldKey, item)}
            >
              <Tooltip title={index === 0 ? `${displayName}: ${item}` : item} placement="top">
                <span className="tag-text">{index === 0 ? `${displayName}: ${item}` : item}</span>
              </Tooltip>
            </FilterTag>
          ))}
          <ExpandButton 
            type="link" 
            size="small"
            onClick={() => toggleExpanded(fieldKey)}
          >
            [收起]
          </ExpandButton>
        </div>
      );
    }

    // 普通标签
    return (
      <FilterTag
        key={fieldKey}
        className={tagClass}
        closable
        onClose={() => onRemoveTag && onRemoveTag(fieldKey)}
      >
        <Tooltip title={`${displayName}: ${displayText}`} placement="top">
          <span className="tag-text">{displayName}: {displayText}</span>
        </Tooltip>
      </FilterTag>
    );
  };

  // 收集所有有值的筛选条件
  const allFilterKeys = new Set([
    ...Object.keys(pendingFilters),
    ...Object.keys(appliedFilters)
  ]);

  const tags = Array.from(allFilterKeys)
    .map(fieldKey => renderTag(fieldKey, pendingFilters[fieldKey]))
    .filter(Boolean);

  if (tags.length === 0) {
    return null;
  }

  return (
    <FilterTagContainer>
      {tags}
    </FilterTagContainer>
  );
};

export default FilterTagList;
