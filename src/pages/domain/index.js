import { Component } from "react";
import { Select, TreeSelect, Button, InputNumber, Input, message, Tag } from "antd";
import styled from "styled-components";
import { UserOutlined } from "@ant-design/icons";
import {
	OrderRow,
	OrderTitle,
	SideContent,
	OrderSubmit,
	OrderCol,
	OrderCenterCol,
	OrderStartCol,
	OrderCompactRow,
	ShadeForm
} from "@/common/styleLayout";
import {
	postModelTree,
	postOpsLeader,
	requestLeaderEmail,
	requestOpsDBA,
	postBuyDomain,
	requestOnlySuperLeaderEmails,
} from "@/request/api";
import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";


const { TextArea } = Input;

export const DomainApllyForm = styled(ShadeForm)`
  width: 62%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
`

const TITLE = "域名购买"
export default class  DomainOrder extends Component {
	state = {
		// 服务器名称
		domain: undefined,
		whois:undefined,
		applyMsg: undefined,
		buyTime:undefined,

		// 审批信息
		opsAuditEmails: [],
		opsAuditEmailSelected: undefined,
		leaderEmails: undefined,
		opsLeaderEmail: undefined,
		superLeaderEmail:undefined,
		// 模型信息
		costTreeData: [],
		costNodeSelected: undefined,
		monitorNodeSelected: undefined,

	}
	handleDomainChange = (e) => {
		this.setState({
			domain: e.target.value
		})
	}
	handleWhoisChange = (e) => {
		this.setState({
			whois: e.target.value
		})
	}
	handleBuyTimeChange = (e) => {
		this.setState({
			buyTime: e
		})
	}
	// 运维审批人
	handleOpsAuditPeopleChange = (value) => {
		this.setState({
			opsAuditEmailSelected: value
		})
	}
	// 成本模型挂载
	handleCostTreeSelectChange = (value, lable, e) => {
		let path = value.split(".")
		if (path.length !== 3) {
			message.warn("请选择叶子节点！")
			this.setState({
				costNodeSelected: ""
			})
			return
		} else {
			this.setState({
				costNodeSelected: value
			})
		}

	}
	// 监控模型挂载
	// handleMonitorTreeSelectChange = (value) => {
	// }
	// 申请理由
	handleApplyMsgChange = (e) => {
		this.setState({
			applyMsg: e.target.value
		})
	}
	// 提交购买服务器订单
	handleSubmit = () => {
		// 处理业务树节点id
		let nodes = this.state.costNodeSelected
		let orderArg = {
			"domain":this.state.domain,
			"whois":this.state.whois,
			"describe": this.state.applyMsg,
			"buy_time":this.state.buyTime,
			"business_tree_branch": nodes,
			"ops_owner": this.state.opsAuditEmailSelected
		}
		console.log(orderArg)
		postBuyDomain(orderArg).then((resp) => {
			console.log(resp)
			if (resp !== null) {
				navigateModal(this.props.navigate, DoingOrderUrlSuffix, 5)
			}
		})
	}

	// ### 树
	// 优先遍历树，增加节点title和key属性
	bfsAddProps = (treeData) => {
		let queue = [...treeData]
		while (queue.length > 0) {
			let node = queue.shift()
			node.title = node.name
			node.label = node.name
			node.value = node.path_id
			if (node.children !== undefined && node.children !== null) {
				queue.push(...node.children)
			}
		}
	}
	componentDidMount() {
		// 获取运维审批信息
		requestOpsDBA({}).then((resp) => {
			this.setState({
				opsAuditEmails: resp.dbas.map(item => {
					return { label: item, value: item }
				})
			});
		});
		requestLeaderEmail({}).then(resp => {
			if (resp !== null) {
				this.setState({
					leaderEmails: resp.email
				})
			}
		})
		requestOnlySuperLeaderEmails({}).then(
			resp =>{
				if (resp !== null) {
					this.setState({
						superLeaderEmail: resp.email
					})
				}
			}
		)
		postOpsLeader({}).then(resp => {
			if (resp !== null) {
				this.setState({
					opsLeaderEmail: resp.ops_leader
				})
			}
		})
		// 成本模型树
		postModelTree({ "model_name": "cost", "attach_path_id": true }).then((resp) => {
			let treeData = resp.data
			this.bfsAddProps(treeData)
			this.setState({
				costTreeData: treeData
			})
		})
	}
	render() {
		return (
			<SideContent>
				<DomainApllyForm>
					<OrderTitle>{TITLE}</OrderTitle>
					<OrderCompactRow>
						<OrderCol>
							<span>运维审批：</span>
							<Select
								options={this.state.opsAuditEmails}
								placeholder="运维审批人员"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleOpsAuditPeopleChange}
								value={this.state.opsAuditEmailSelected}
							/>
						</OrderCol>
						<OrderCenterCol>
							<span>审批人员：</span>
							<Tag color="#108ee9" icon={<UserOutlined />}>
								{this.state.leaderEmails}，{this.state.opsLeaderEmail},{this.state.superLeaderEmail}
							</Tag>
						</OrderCenterCol>
					</OrderCompactRow>
					<OrderCompactRow>
						<OrderCol>
							<span>域&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</span>
							<Input
								placeholder="请填写域名"
								value={this.state.domain}
								onChange={this.handleDomainChange}
							/>
						</OrderCol>
						<OrderCol>
							<span>域名持有者：</span>
							<Input
								placeholder="请填写域名持有者"
								value={this.state.whois}
								onChange={this.handleWhoisChange}
							/>
						</OrderCol>
						<OrderCol>
							<span>购买时常：</span>
							<InputNumber
								min={1}
								max={10}
								value={this.state.buyTime}
								onChange={this.handleBuyTimeChange}
								defaultValue={1}
							/>
							<span>&nbsp;年</span>
						</OrderCol>
					</OrderCompactRow>
					{/* 节点挂载 */}
					<OrderCompactRow>
						<OrderCol>
							<span>业务树相关节点：</span>
							<TreeSelect
								placeholder="请选择业务树相关节点"
								treeData={this.state.costTreeData}
								onSelect={this.handleCostTreeSelectChange}
								value={this.state.costNodeSelected}
							/>
						</OrderCol>
						{/* <OrderCol>
							<span>监控挂载：</span>
							<TreeSelect
								placeholder="请选择监控挂载节点"
								treeData={this.state.costTreeData}
								onChange={this.handleMonitorTreeSelectChange}
								value={this.state.monitorNodeSelected}
								disabled
							/>
						</OrderCol> */}
					</OrderCompactRow>
					<OrderRow>
						<OrderStartCol>
							<span>申请理由：</span>
							<TextArea
								placeholder="请填写申请理由，不少于5个字符"
								style={{ height: "7vw" }}
								value={this.state.applyMsg}
								onChange={this.handleApplyMsgChange}
							/>
						</OrderStartCol>
					</OrderRow>
					<OrderSubmit>
						<Button type="primary" onClick={this.handleSubmit}>
							提交
						</Button>
						<Button type="dashed" onClick={this.handleCancel}>
							取消
						</Button>
					</OrderSubmit>
				</DomainApllyForm>
			</SideContent>
		);
	}
}
