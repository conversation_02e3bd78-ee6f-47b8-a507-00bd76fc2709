import { Component } from "react";
import styled from "styled-components";
import { Upload,Button,message } from 'antd'
import { InboxOutlined } from "@ant-design/icons";
import { SideContent,OrderTitle,OrderRow,ShadeForm,OrderSubmit } from "@/common/styleLayout";
import { Link } from "react-router-dom";
import {
	requestUpdateCert
} from "@/request/api";

const CertContent = styled(OrderRow)`
  flex-direction: column;
  align-items: flex-start;
  flex-wrap: wrap;
  >span{
    width: 100%;
  }

`
const CertForm = styled(ShadeForm)`
  width: 55%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
`
const CertFormItem = styled(CertForm.Item)`
	width:100%;
	margin-bottom: 20px;
`;

export default class CertUpload extends Component {
	pubFile = (e) => {
		const file = e?.file;
		this.setState({ pubFile: file });
		return file;
	};

	keyFile = (e) => {
		const file = e?.file;
		this.setState({ keyFile: file });
		return file;
	};

	constructor(props) {
		super(props);
		this.state = {
            pubFile: null,
            keyFile: null,
            isSubmitting: false // 新添加的state属性
        };
		this.pubFile = this.pubFile.bind(this);
		this.keyFile = this.keyFile.bind(this);
		this.handleSubmit = this.handleSubmit.bind(this);
	}

	handleSubmit = () => {
		this.setState({ isSubmitting: true });
		const { pubFile, keyFile } = this.state;
		const formData = new FormData();
		if (pubFile) formData.append('pub_file', pubFile);
   		if (keyFile) formData.append('key_file', keyFile);
		requestUpdateCert(formData).then((data) => {
			if (data !== null) {
				if (data.resp_common.ret === 0) {
					message.success("证书上传成功，证书ID["+ data.cert_id +"]")
				}
			}
		});

		setTimeout(() => {
            this.setState({ isSubmitting: false });
        }, 3000);
	}

	render() {
		return (
			<SideContent>
				<CertForm>
					<OrderTitle>证书上传</OrderTitle>
					<CertContent>
						<CertFormItem name="pub_file" valuePropName="file" getValueFromEvent={this.pubFile}>
							<Upload.Dragger name="pub_file" accept=".crt,.pem" beforeUpload={() => false} maxCount={1}>
							<p className="ant-upload-drag-icon">
								<InboxOutlined />
							</p>
							<p className="ant-upload-text">单击或拖动文件到此区域上传</p>
							<p className="ant-upload-hint">请上传公钥文件(后缀通常为.crt或.pem)</p>
							</Upload.Dragger>
						</CertFormItem>
					
						<CertFormItem name="key_file" valuePropName="file" getValueFromEvent={this.keyFile}>
							<Upload.Dragger name="key_file" accept=".key" beforeUpload={() => false} multiple={1}>
							<p className="ant-upload-drag-icon">
								<InboxOutlined />
							</p>
							<p className="ant-upload-text">单击或拖动文件到此区域上传</p>
							<p className="ant-upload-hint">请上传私钥文件(后缀为.key)</p>
							</Upload.Dragger>
						</CertFormItem>
					</CertContent>
					<OrderSubmit>
						<Button type="primary" onClick={this.handleSubmit} disabled={this.state.isSubmitting}>
							提交
						</Button>
						<Link to="/new-order">
							<Button type="dashed">
								取消
							</Button>
						</Link>
					</OrderSubmit>
				</CertForm>
			</SideContent>
		);
	}
}

