import { Component } from "react";
import { SideContent,OrderTitle,OrderRow } from "@/common/styleLayout";
import { Input, Collapse, Divider } from "antd";
import styled from "styled-components";

// css-js start ↓↓↓
const CertDiv = styled.div`
	height: 100%;
	width: 60%;
	padding: 1.5vw;
	background-color: #fff;
	border-radius: 10px;
	box-shadow: 0 10px 20px 0 rgba(153, 153, 153, 0.25);
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: flex-start;
    width: 48%;
`

const Span = styled.span`
    min-width: 80px;
`

const { Panel } = Collapse; // 导入Panel组件

const items = [
	{
	  	key: 'txy',
	  	label: '腾讯云',
		children:<p></p>,
	},
	{
	  	key: 'hwbq',
	  	label: '华为云-豹趣',
		children:<p></p>,
	},
	{
	  	key: 'ali',
		label: '阿里云',
		children:<p></p>,
	},
	{
		key: 'ks',
		label: '金山云',
		children:<p></p>,
	},
]

export default class CertUpdate extends Component {
	state = {
        new_cert_id: "",
        old_cert_id: "",
    }

	render() {
		return (
			<SideContent>
				<CertDiv>
				 	<OrderTitle>证书更新</OrderTitle>
				 	<OrderRow>
                        <Span>新证书：</Span>
                        <Input
                            placeholder="请输入证书ID"
                            value={this.state.new_cert_id}
                        />
                    </OrderRow>
                    <OrderRow>
                        <Span>旧证书：</Span>
                        <Input
                            placeholder="请输入证书ID"
                            value={this.state.old_cert_id}
                        />
                	</OrderRow>
					<Divider/>
					<Collapse style={{ width:'100%' }}>
						{
							items.map(item => (
								<Panel header={item.label} key={item.key}>
									{item.children}
								</Panel>
							))
						}
					</Collapse>
				</CertDiv>
				
			</SideContent>
		);
	}
}

