import { Card, Row, Col, Statistic, Progress, Dropdown, Menu } from "antd";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import {
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

// 仪表盘使用的模拟数据
const ticketData = [
  { month: "1月", total: 120, success: 95, failed: 15, pending: 10 },
  { month: "2月", total: 150, success: 120, failed: 20, pending: 10 },
  { month: "3月", total: 180, success: 145, failed: 25, pending: 10 },
  { month: "4月", total: 200, success: 165, failed: 25, pending: 10 },
  { month: "5月", total: 220, success: 185, failed: 25, pending: 10 },
  { month: "6月", total: 250, success: 210, failed: 30, pending: 10 },
];

const pieData = [
  { name: "成功", value: 820, color: "#52c41a" },
  { name: "失败", value: 140, color: "#ff4d4f" },
  { name: "处理中", value: 60, color: "#1890ff" },
];

export default function Dashboard() {
  const navigate = useNavigate();

  const quickActions = useMemo(
    () => [
      { title: "新建工单", desc: "快速创建通用工单", iconBg: "#e6f7ff", emoji: "📝", to: "/new-order/common" },
      { title: "CDN刷新", desc: "快速刷新CDN缓存", iconBg: "#f6ffed", emoji: "☁️", to: "/new-order/cdn-flush" },
      { title: "服务器账号申请", desc: "申请服务器访问权限", iconBg: "#fff2e8", emoji: "👤", to: "/new-order/server-account" },
      { title: "Token申请", desc: "申请API访问令牌", iconBg: "#f9f0ff", emoji: "🔑", to: "/new-order/k8stoken" },
    ],
    []
  );

  return (
    <div style={{ padding: "24px" }}>

      {/* 快捷操作 */}
      <div style={{ marginBottom: "16px", fontSize: "16px", fontWeight: 600, color: "#1a1a1a", display: "flex", alignItems: "center", gap: 8 }}>
        <span>⚡</span>
        <span>快捷操作</span>
      </div>
      <Row gutter={[16, 16]}>
        {quickActions.map((item) => (
          <Col span={6} key={item.title}>
            <Card
              onClick={() => navigate(item.to)}
              style={{ borderRadius: 12, border: "none", boxShadow: "0 2px 8px rgba(0,0,0,0.06)", cursor: "pointer" }}
              bodyStyle={{ padding: 16 }}
            >
              <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
                <div style={{ width: 40, height: 40, borderRadius: 8, backgroundColor: item.iconBg, display: "flex", alignItems: "center", justifyContent: "center", fontSize: 18 }}>
                  {item.emoji}
                </div>
                <div>
                  <div style={{ fontSize: 14, color: "#1a1a1a", fontWeight: 500 }}>{item.title}</div>
                  <div style={{ fontSize: 12, color: "#666" }}>{item.desc}</div>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      <div style={{ height: 1, background: "linear-gradient(90deg, transparent, #e8eaed, transparent)", margin: "32px 0" }} />

      {/* 数据统计 */}
      <div style={{ marginBottom: 16, fontSize: 16, fontWeight: 600, color: "#1a1a1a", display: "flex", alignItems: "center", gap: 8 }}>
        <span style={{
          width: 24,
          height: 24,
          borderRadius: 6,
          background: "linear-gradient(135deg,#e6f0ff,#bcd3ff)",
          display: "inline-flex",
          alignItems: "center",
          justifyContent: "center",
          color: "#3b6ef5",
          fontSize: 14,
          fontWeight: 700
        }}>Σ</span>
        <span>数据统计</span>
      </div>
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card style={{ borderRadius: 12, border: "none", boxShadow: "0 2px 8px rgba(0,0,0,0.06)" }}>
            <Statistic title={<span style={{ color: "#666", fontSize: 14 }}>总工单数</span>} value={1020} valueStyle={{ color: "#1a1a1a", fontSize: 32, fontWeight: 600 }} />
            <div style={{ marginTop: 8, color: "#52c41a", fontSize: 12 }}>↗ 12% 较上月</div>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ borderRadius: 12, border: "none", boxShadow: "0 2px 8px rgba(0,0,0,0.06)" }}>
            <Statistic title={<span style={{ color: "#666", fontSize: 14 }}>成功处理</span>} value={820} valueStyle={{ color: "#52c41a", fontSize: 32, fontWeight: 600 }} />
            <div style={{ marginTop: 8 }}>
              <Progress percent={80.4} showInfo={false} strokeColor="#52c41a" size="small" />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ borderRadius: 12, border: "none", boxShadow: "0 2px 8px rgba(0,0,0,0.06)" }}>
            <Statistic title={<span style={{ color: "#666", fontSize: 14 }}>处理失败</span>} value={140} valueStyle={{ color: "#ff4d4f", fontSize: 32, fontWeight: 600 }} />
            <div style={{ marginTop: 8 }}>
              <Progress percent={13.7} showInfo={false} strokeColor="#ff4d4f" size="small" />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ borderRadius: 12, border: "none", boxShadow: "0 2px 8px rgba(0,0,0,0.06)" }}>
            <Statistic title={<span style={{ color: "#666", fontSize: 14 }}>平均处理时长</span>} value="2.4" suffix="小时" valueStyle={{ color: "#1890ff", fontSize: 32, fontWeight: 600 }} />
            <div style={{ marginTop: 8, color: "#52c41a", fontSize: 12 }}>↘ 0.3h 较上月</div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        <Col span={16}>
          <Card title={<span style={{ fontSize: 16, fontWeight: 600, color: "#1a1a1a" }}>工单趋势分析</span>} style={{ borderRadius: 12, border: "none", boxShadow: "0 2px 8px rgba(0,0,0,0.06)" }}>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={ticketData}>
                <defs>
                  <linearGradient id="colorTotal" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#1890ff" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="#1890ff" stopOpacity={0} />
                  </linearGradient>
                  <linearGradient id="colorSuccess" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#52c41a" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="#52c41a" stopOpacity={0} />
                  </linearGradient>
                  <linearGradient id="colorFailed" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#ff4d4f" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="#ff4d4f" stopOpacity={0} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis dataKey="month" stroke="#666" />
                <YAxis stroke="#666" />
                <Tooltip contentStyle={{ backgroundColor: "#fff", border: "none", borderRadius: 8, boxShadow: "0 2px 8px rgba(0,0,0,0.06)", color: "#1a1a1a" }} />
                <Area type="monotone" dataKey="total" stroke="#1890ff" fillOpacity={1} fill="url(#colorTotal)" strokeWidth={2} />
                <Area type="monotone" dataKey="success" stroke="#52c41a" fill="url(#colorSuccess)" strokeWidth={2} />
                <Area type="monotone" dataKey="failed" stroke="#ff4d4f" fill="url(#colorFailed)" strokeWidth={2} />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col span={8}>
          <Card title={<span style={{ fontSize: 16, fontWeight: 600, color: "#1a1a1a" }}>工单状态分布</span>} style={{ borderRadius: 12, border: "none", boxShadow: "0 2px 8px rgba(0,0,0,0.06)" }}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie data={pieData} cx="50%" cy="50%" innerRadius={60} outerRadius={100} paddingAngle={5} dataKey="value">
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip contentStyle={{ backgroundColor: "#fff", border: "none", borderRadius: 8, boxShadow: "0 2px 8px rgba(0,0,0,0.06)", color: "#1a1a1a" }} />
              </PieChart>
            </ResponsiveContainer>
            <div style={{ marginTop: 16 }}>
              {pieData.map((item, index) => (
                <div key={index} style={{ display: "flex", justifyContent: "space-between", marginBottom: 8 }}>
                  <span style={{ display: "flex", alignItems: "center", color: "#1a1a1a" }}>
                    <div style={{ width: 8, height: 8, backgroundColor: item.color, borderRadius: "50%", marginRight: 8 }} />
                    {item.name}
                  </span>
                  <span style={{ fontWeight: 600, color: "#1a1a1a" }}>{item.value}</span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}


