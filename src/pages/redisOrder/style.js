import styled from "styled-components"
import { ShadeForm } from "@/common/styleLayout";
// css-js start ↓↓↓
export const RedisApllyFormBox = styled(ShadeForm)`
  width: 62%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
`
export const RowDiv = styled.div`
  margin: 0px 5px;
  width: 100%;
  display: flex;
  flex-direction:row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
`
export const FeeSpan = styled.span`
  color: red;
  font-size: large;
`
export const FeeDiv = styled(RowDiv)`
  width: 380px;
`
// css-js end   ↑↑↑
