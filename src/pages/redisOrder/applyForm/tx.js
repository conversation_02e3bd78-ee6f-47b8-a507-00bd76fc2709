import { Component } from "react";
import { Select, TreeSelect, Button, Input, message, Tag } from "antd";
import styled from "styled-components";
import { MoneyCollectOutlined, UserOutlined } from "@ant-design/icons";
import withRouter from "@/util/withRouter";
import { RedisApllyFormBox } from "../style"
import {
	OrderRow,
	OrderTitle,
	SideContent,
	OrderSubmit,
	OrderCol,
	OrderCenterCol,
	OrderStartCol,
	OrderEndCol
} from "@/common/styleLayout";
import {
	postModelTree,
	postOpsLeader,
	postTxRedisConfs,
	postTxRedisPrice,
	requestCommonOrder,
	requestLeaderEmail,
	requestOpsMember,
	// requestSuperLeaderEmails,
	requestTxOsImage,
	requestTxRegion,
	requestTxZone
} from "@/request/api";
import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";


const { TextArea } = Input;

// css-js
export const FeeSpan = styled.span`
  color: red;
  font-size: large;
`
class TxRedisApplyForm extends Component {

	state = {
		opsAuditEmails: [],
		opsAuditEmailSelected: undefined,
		leaderEmails: undefined,
		opsLeaderEmail:undefined,
		regions: [],
		regionSelected: undefined,
		zones: [],
		zoneSelected: undefined,
		zoneIDMap: {},
		sellConf: undefined,
		// 6:Redis 4.0 master-slave
		// 7:Redis 4.0 cluster
		// 8:Redis 5.0 master-slave
		// 9:Redis 5.0 cluster
		// 10:Tendis
		// 14:KeewiDB集群版
		// 15:Redis 6.0 master-slave
		// 16:Redis 6.0 cluster
		typeSet: undefined,
		typeMap: undefined,
		versionOptions: [
			{ label: "4.0", value: 4, disabled: true },
			{ label: "5.0", value: 5, disabled: true },
			{ label: "6.0", value: 6, disabled: true }
		],
		versionSelected: undefined,
		schemaOptions: [
			{ label: "主从架构", value: "master-slave", disabled: true },
			{ label: "集群架构", value: "cluster", disabled: true }
		],
		schemaSelected: undefined,
		typeSelected: undefined,
		memorySizeDisable: false,
		memorySizeOption: [],
		memorySizeSelected: undefined,
		replicaNumOptions: [],
		replicaNumSelected: undefined,
		shardHide: true,
		shardSizeOptions: [],
		shardNumOptions: [],
		shardNumSelected: undefined,
		costTreeData: [],
		costNodeSelected: undefined,
		monitorNodeSelected: undefined,
		instanceName: "",
		instanceNameIndex: undefined,
		applyMsg: "",
		instanceDiscountPrice: undefined,
		discount: undefined
	}
	// 运维审批人
	handleOpsAuditPeopleChange = (value) => {
		this.setState({
			opsAuditEmailSelected: value
		})
	}
	// 地区
	handleRegionChange = (value) => {
		this.setState({
			regionSelected: value
		})
		let arg = {
			product: "cvm",
			region: value

		}
		requestTxZone(arg).then((resp) => {
			let zoneIDMap = {}

			resp.zones.forEach(element => {
				zoneIDMap[element.zone] = element.zone_id
			})
			this.setState({
				zoneIDMap: zoneIDMap,
				zones: resp.zones.map(item => { return { label: item.zone_name, value: item.zone } }),
				zoneSelected: undefined,
				versionSelected: undefined,
				schemaSelected: undefined,
				memorySizeSelected: undefined,
				replicaNumSelected: undefined,
				shardNumSelected: undefined,
				instanceDiscountPrice: undefined
			})

		})
		requestTxOsImage(arg).then((resp) => {
			this.setState({
				osImages: resp.os_image.map(item => { return { label: item.image_name, value: item.image_id } })
			})
		})

	}
	// 可用区
	handleZoneChange = (value, option) => {
		console.log(option)
		this.setState({
			zoneSelected: value
		})
		let arg = {
			region: this.state.regionSelected,
			zone: value
		}
		postTxRedisConfs(arg).then((resp) => {
			if (resp.zoneRedisConf === null) {
				message.warn(`${this.state.zoneSelected}没有可售实例，请选择其他可用区`)
				return
			}
			// 处理redis 类型集合
			let typeSet = new Set()
			let typeMap = {}
			resp.zoneRedisConf.ProductSet.forEach(element => {
				typeSet.add(element.Type)
				typeMap[element.Type] = element
			});
			// 处理版本
			let versionOptions = this.state.versionOptions
			versionOptions[0].disabled = typeSet.has(6) || typeSet.has(7) ? false : true
			versionOptions[1].disabled = typeSet.has(8) || typeSet.has(9) ? false : true
			versionOptions[2].disabled = typeSet.has(15) || typeSet.has(16) ? false : true
			this.setState({
				sellConf: resp.zoneRedisConf,
				typeSet: typeSet,
				typeMap: typeMap,
				versionOptions: versionOptions,
				versionSelected: undefined,
				schemaSelected: undefined,
				memorySizeSelected: undefined,
				replicaNumSelected: undefined,
				shardNumSelected: undefined,
				instanceDiscountPrice: undefined
			})
		})

	}
	// 版本变更
	handleVersionChange = (value) => {
		let schemaOptions = this.state.schemaOptions
		if (value === 4) {
			schemaOptions[0].disabled = this.state.typeSet.has(6) ? false : true
			schemaOptions[1].disabled = this.state.typeSet.has(7) ? false : true
		}
		if (value === 5) {
			schemaOptions[0].disabled = this.state.typeSet.has(8) ? false : true
			schemaOptions[1].disabled = this.state.typeSet.has(9) ? false : true
		}
		if (value === 6) {
			schemaOptions[0].disabled = this.state.typeSet.has(15) ? false : true
			schemaOptions[1].disabled = this.state.typeSet.has(16) ? false : true
		}
		this.setState({
			versionSelected: value,
			schemaOptions: schemaOptions,
			schemaSelected: undefined,
			memorySizeSelected: undefined,
			replicaNumSelected: undefined,
			shardNumSelected: undefined,
			instanceDiscountPrice: undefined
		})
	}
	// 架构类型变更
	handleSchemaChange = (value) => {
		let typeSelected
		if (this.state.versionSelected === 4) {
			if (value === "master-slave") {
				typeSelected = 6
			}
			else {
				typeSelected = 7
			}
		} else if (this.state.versionSelected === 5) {
			if (value === "master-slave") {
				typeSelected = 8
			}
			else {
				typeSelected = 9
			}
		} else if (this.state.versionSelected === 6) {
			if (value === "master-slave") {
				typeSelected = 15
			}
			else {
				typeSelected = 16
			}
		}
		let memorySizeOption = this.state.typeMap[typeSelected].TotalSize.map((item) => {
			return { label: item, value: item }
		})
		let replicaNumOptions = this.state.typeMap[typeSelected].ReplicaNum.map((item) => {
			return { label: item, value: item }
		})
		let shardNumOptions = this.state.typeMap[typeSelected].ShardNum.map((item) => {
			return { label: item, value: item }
		})
		let shardSizeOptions = this.state.typeMap[typeSelected].ShardSize.map((item) => {
			return { label: item, value: item }
		})
		this.setState(
			{
				typeSelected: typeSelected,
				schemaSelected: value,
				memorySizeOption: memorySizeOption,
				replicaNumOptions: replicaNumOptions,
				shardNumOptions: shardNumOptions,
				shardSizeOptions: shardSizeOptions,
				shardHide: shardNumOptions.length <= 1,
				memorySizeSelected: undefined,
				replicaNumSelected: undefined,
				instanceDiscountPrice: undefined
			}
		)
		if (shardNumOptions.length <= 1) {
			this.setState({
				shardNumSelected: 1,
				memorySizeDisable: false
			})
		} else {
			this.setState({
				shardNumSelected: undefined,
				memorySizeDisable: true
			})
		}
	}
	// 内存大小
	handleMemorySizeChange = (value) => {
		this.setState({
			memorySizeSelected: value
		}, this.costEstimate)
	}
	// 副本数量
	handleReplicaNumChange = (value) => {
		this.setState({
			replicaNumSelected: value
		}, this.costEstimate)
	}
	// 分片数量
	handleShardNumChange = (value) => {
		this.setState({
			shardNumSelected: value
		}, this.costEstimate)
	}

	// 处理服务器名称
	handleInstanceNameChange = (e) => {
		this.setState({
			instanceName: e.target.value
		})
	}
	// 成本模型挂载
	handleCostTreeSelectChange = (value, lable, e) => {
		let path = value.split(".")
		if (path.length !== 3) {
			message.warn("请选择叶子节点！")
			this.setState({
				costNodeSelected: ""
			})
			return
		}
		this.setState({
			costNodeSelected: value
		})
	}
	// 监控模型挂载
	// handleMonitorTreeSelectChange = (value) => {
	// }
	// 申请理由
	handleApplyMsgChange = (e) => {
		this.setState({
			applyMsg: e.target.value
		})
	}
	handelRedisNameIndex = () => {
		this.setState({
			instanceNameIndex: `tx_redis_${this.state.zoneSelected}_${this.state.memorySizeSelected}G-`
		})
	}
	handleSubmit = () => {
		if (this.state.opsAuditEmailSelected === undefined || this.state.opsAuditEmailSelected.length === 0) {
			message.error("请先选择运维审批人！")
			return
		}
		if (this.state.regionSelected === undefined || this.state.regionSelected.length === 0) {
			message.error("请先选择地区！")
			return
		}
		if (this.state.regionSelected === undefined || this.state.regionSelected.length === 0) {
			message.error("请先选择可用区！")
			return
		}
		if (this.state.memorySizeSelected === undefined || this.state.memorySizeSelected.length === 0) {
			message.error("请先选择内存 或 分片内存！")
			return
		}
		if (this.state.replicaNumSelected === undefined || this.state.replicaNumSelected.length === 0) {
			message.error("请先选择副本数量！")
			return
		}
		if (this.state.shardNumSelected === undefined || this.state.shardNumSelected.length === 0) {
			message.error("请先选择分片数量！")
			return
		}
		if (this.state.costNodeSelected === undefined || this.state.costNodeSelected.length === 0) {
			message.warn("请先选择业务树相关节点！")
			return
		}
		let nodes = this.state.costNodeSelected.split(".")
		let apply_info = {
			productType: "redis",
			ops_audit_email: this.state.opsAuditEmailSelected,
			region: this.state.regionSelected,
			zoneID: this.state.zoneIDMap[this.state.zoneSelected],
			zone: this.state.zoneSelected,
			chargeType: 1,
			count: 1,
			memory: parseInt(this.state.memorySizeSelected) * 1024,
			type: this.state.typeSelected,
			shadingNum: parseInt(this.state.shardNumSelected),
			replicasNum: parseInt(this.state.replicaNumSelected),
			name: this.state.instanceNameIndex + this.state.instanceName,
			productVersion: "local",
			unit: parseInt(nodes[nodes.length - 1]),
			period: 1,
			dryRun: false
		}
		let arg = {
			order_type: "txy_redis_apply",
			apply_info: JSON.stringify(apply_info),
			apply_msg: this.state.applyMsg,
			exigency: 1
		}
		arg.apply_msg =
			`申请购买腾讯云redis，预估费用为 ${this.state.instanceDiscountPrice} 元/年
		redis配置：${this.state.memorySizeSelected}G内存，${apply_info.shadingNum}分片数量，${apply_info.replicasNum}副本数量
		地区：${apply_info.region}，可用区：${apply_info.zone}
		redis名称：${apply_info.name}
		申请理由：${arg.apply_msg}`.replaceAll("\t", "")
		requestCommonOrder(arg).then((resp) => {
			if (resp !== null) {
				navigateModal(this.props.navigate, DoingOrderUrlSuffix, 5)
			}
		})
	}
	costEstimate = () => {
		this.handelRedisNameIndex()
		if (this.state.regionSelected === undefined || this.state.regionSelected.length === 0) {
			return
		}
		if (this.state.zoneSelected === undefined || this.state.zoneSelected.length === 0) {
			return
		}
		if (this.state.memorySizeSelected === undefined || this.state.memorySizeSelected.length === 0) {
			return
		}
		if (this.state.typeSelected === undefined || this.state.typeSelected.length === 0) {
			return
		}
		if (this.state.shardNumSelected === undefined || this.state.shardNumSelected.length === 0) {
			return
		}
		if (this.state.replicaNumSelected === undefined || this.state.replicaNumSelected.length === 0) {
			return
		}
		let arg = {
			region: this.state.regionSelected,
			zone: this.state.zoneIDMap[this.state.zoneSelected],
			type: this.state.typeSelected,
			memory: parseInt(this.state.memorySizeSelected) * 1024,
			shadingNum: parseInt(this.state.shardNumSelected),
			replicasNum: parseInt(this.state.replicaNumSelected),
			productVersion: "local",
			count: 1,
			chargeType: 1,
			period: 12
		}
		console.log(arg)
		postTxRedisPrice(arg).then((resp) => {
			if (resp !== null) {
				this.setState({
					instanceDiscountPrice: resp.price / 100
				})
			}
		})

	}

	// ### 树
	// 优先遍历树，增加节点title和key属性
	bfsAddProps = (treeData) => {
		let queue = [...treeData]
		while (queue.length > 0) {
			let node = queue.shift()
			node.title = node.name
			node.label = node.name
			node.value = node.path_id
			if (node.children !== undefined && node.children !== null) {
				queue.push(...node.children)
			}
		}
	}
	componentDidMount() {
		// 获取运维审批
		requestOpsMember({}).then((resp) => {
			this.setState({
				opsAuditEmails: resp.members.map(item => {
					return { label: item.substring(0, item.lastIndexOf("@")), value: item }
				})
			});
		});
		// 获取领导
		// requestSuperLeaderEmails({}).then((resp) => {
		// 	this.setState({
		// 		leaderEmails: resp.emails.join(" ")
		// 	})
		// })
		requestLeaderEmail({}).then(resp => {
			if (resp !== null) {
				this.setState({
					leaderEmails: resp.email
				})
			}
		})
		// 获取运维领导邮箱
		postOpsLeader({}).then(resp => {
			if (resp !== null) {
				this.setState({
					opsLeaderEmail: resp.ops_leader
				})
			}
		})
		let arg = {
			product: "cvm"
		}
		// 获取地区
		requestTxRegion(arg).then((resp) => {
			this.setState({
				regions: resp.regions.map(item => { return { label: item.region_name, value: item.region } })
			})
		})
		// 成本模型树
		postModelTree({ "model_name": "cost", "attach_path_id": true }).then((resp) => {
			let treeData = resp.data
			this.bfsAddProps(treeData)
			this.setState({
				costTreeData: treeData
			})
		})
	}
	render() {
		return (
			<SideContent>
				<RedisApllyFormBox>
					<OrderTitle>腾讯云Redis申请</OrderTitle>
					<OrderRow>
						<OrderCol>
							<span>运维审批：</span>
							<Select
								options={this.state.opsAuditEmails}
								placeholder="运维审批人"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleOpsAuditPeopleChange}
								value={this.state.opsAuditEmailSelected}
							/>
						</OrderCol>
						<OrderCenterCol>
							<span>审批人员：</span>
							<Tag color="#108ee9" icon={<UserOutlined />}>
								{this.state.leaderEmails}，{this.state.opsLeaderEmail}
							</Tag>
						</OrderCenterCol>
					</OrderRow>
					{/* 区域地区 */}
					<OrderRow>
						<OrderCol>
							<span>地区城市：</span>
							<Select
								options={this.state.regions}
								placeholder="请选择地区"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleRegionChange}
							/>
						</OrderCol>
						<OrderCol>
							<span>可用区域：</span>
							<Select
								options={this.state.zones}
								placeholder="请选择可用区"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleZoneChange}
								value={this.state.zoneSelected}
							/>
						</OrderCol>
					</OrderRow>
					{/* 版本和架构类型 */}
					<OrderRow>
						<OrderCol>
							<span>兼容版本：</span>
							<Select
								options={this.state.versionOptions}
								placeholder="请选择服务器规格机型"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleVersionChange}
								value={this.state.versionSelected}
							/>
						</OrderCol>
						<OrderCol>
							<span>架构类型：</span>
							<Select
								options={this.state.schemaOptions}
								placeholder="请选择服务器硬件配置"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleSchemaChange}
								value={this.state.schemaSelected}
							/>
						</OrderCol>
					</OrderRow>

					{/*内存、副本数量*/}
					<OrderRow>
						<OrderCol>
							<span>内存大小：</span>
							<Select
								disabled={this.state.memorySizeDisable}
								options={this.state.memorySizeOption}
								placeholder="内存大小"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleMemorySizeChange}
								value={this.state.memorySizeSelected}
							/>
						</OrderCol>
						<OrderCol>
							<span>副本数量：</span>
							<Select
								options={this.state.replicaNumOptions}
								placeholder="副本数量"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleReplicaNumChange}
								value={this.state.replicaNumSelected}
							/>
						</OrderCol>
					</OrderRow>
					{/* 分片数量、分片大小 */}
					<OrderRow className={this.state.shardHide ? "hide" : ""}>
						<OrderCol>
							<span>分片数量：</span>
							<Select
								options={this.state.shardNumOptions}
								placeholder="请选择分片数量"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleShardNumChange}
								value={this.state.shardNumSelected}

							/>
						</OrderCol>
						<OrderCol>
							<span>分片容量：</span>
							<Select
								options={this.state.shardSizeOptions}
								placeholder="请选择分片内存容量"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleMemorySizeChange}
								value={this.state.memorySizeSelected}

							/>
						</OrderCol>
					</OrderRow>
					{/* 节点挂载 */}
					<OrderRow>
						<OrderCol>
							<span>业务树相关节点：</span>
							<TreeSelect
								placeholder="请选择业务树相关节点"
								treeData={this.state.costTreeData}
								onChange={this.handleCostTreeSelectChange}
								value={this.state.costNodeSelected}
							/>
						</OrderCol>
						{/* <OrderCol>
							<span>监控挂载：</span>
							<TreeSelect
								disabled={true}
								placeholder="请选择监控挂载节点"
								treeData={this.state.costTreeData}
								onChange={this.handleMonitorTreeSelectChange}
								value={this.state.monitorNodeSelected}
							/>
						</OrderCol> */}
					</OrderRow>
					{/* 服务器名称 */}
					<OrderRow>
						<OrderCol>
							<span>实例名称：</span>
							<span>{this.state.instanceNameIndex}</span>
							<Input
								placeholder="请填写实例名称"
								value={this.state.instanceName}
								onChange={this.handleInstanceNameChange}
							/>
						</OrderCol>
					</OrderRow>
					{/* 申请陈述 */}
					<OrderRow>
						<OrderStartCol>
							<span>申请陈述：</span>
							<TextArea
								placeholder="请填写申请理由，不少于5个字符"
								style={{ height: "7vw" }}
								value={this.state.applyMsg}
								onChange={this.handleApplyMsgChange}
							/>
						</OrderStartCol>
					</OrderRow>
					<OrderRow>
						<OrderEndCol>
							<FeeSpan>
								{/* 折扣：{this.state.discount}，折后 */}
								费用预估：{this.state.instanceDiscountPrice} 元/年&nbsp;
								<MoneyCollectOutlined />
							</FeeSpan>
						</OrderEndCol>
					</OrderRow>
					{/* 费用预估 */}
					<OrderSubmit>
						<Button type="primary" onClick={this.handleSubmit}>
							提交
						</Button>
						<Button type="dashed" onClick={this.handleCancel}>
							取消
						</Button>
					</OrderSubmit>
				</RedisApllyFormBox>
			</SideContent>
		);
	}
}

export default withRouter(TxRedisApplyForm);