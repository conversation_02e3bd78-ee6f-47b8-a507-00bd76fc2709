import { Component } from "react";
import { Select, Button, Input, message, Tag, TreeSelect } from "antd";
import styled from "styled-components";
import { MoneyCollectOutlined, UserOutlined } from "@ant-design/icons";
import withRouter from "@/util/withRouter";
import { RedisApllyFormBox } from "../style"
import {
    OrderCompactRow,
    OrderTitle,
    SideContent,
    OrderSubmit,
    OrderCol,
    OrderCenterCol,
    OrderStartCol,
    OrderEndCol,
    OrderTextRow
} from "@/common/styleLayout";
import {
    postModelTree,
    postOpsLeader,
    requestLeaderEmail,
    requestOpsMember,
    postCloudRedisSellConf,
    postCloudRedisPriceOrder,
    postBuyCloudRedis
} from "@/request/api";
import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";
import PropTypes from 'prop-types';


const { TextArea } = Input;

// css-js
export const FeeSpan = styled.span`
  color: red;
  font-size: large;
`

class RedisApllyForm extends Component {

    state = {
        // 审批
        opsAuditEmails: [],
        opsAuditEmailSelected: undefined,
        leaderEmails: undefined,
        opsLeaderEmail: undefined,
        // 业务树
        costTreeData: [],
        costNodeSelected: undefined,

        // 售卖规格配置
        sellConf: undefined,
        regions: undefined,
        regionIDSelected: undefined,
        zones: undefined,
        zoneIDSelected: undefined,
        versions: undefined,
        versionSelected: undefined,
        replicaNums: undefined,
        replicaNumSelected: undefined,
        // 副本可用区
        backupOneZoneIDSelected: undefined,
        backupTwoZoneIDSelected: undefined,
        // 分片
        shardNums: undefined,
        shardNumSelected: undefined,
        // 实例规格
        instanceTypes: undefined,
        instanceTypeIDSelected: undefined,

        // 服务器名称
        instanceNameIndex: undefined,
        instanceName: undefined,
        // 申请陈述
        applyMsg: undefined,
        // 费用信息
        tradePrice: undefined,
    }

    // 初始化
    componentDidMount() {
        // 获取mysql售卖规格
        postCloudRedisSellConf({ supplier: this.props.supplier }).then((resp) => {
            console.log(resp)
            let regionIDs = Object.keys(resp.data)
            this.setState({
                sellConf: resp.data,
                regions: regionIDs.map(regionID => { return { label: regionID, value: regionID } })
            })
        })
        // 获取成本模型树
        postModelTree({ "model_name": "cost", "attach_path_id": true }).then((resp) => {
            let treeData = resp.data
            this.bfsAddProps(treeData)
            this.setState({
                costTreeData: treeData
            })
        })
        // 获取运维审批
        requestOpsMember({}).then((resp) => {
            this.setState({
                opsAuditEmails: resp.members.map(item => {
                    return { label: item.substring(0, item.lastIndexOf("@")), value: item }
                })
            });
        });
        // 获取领导
        requestLeaderEmail({}).then(resp => {
            if (resp !== null) {
                this.setState({
                    leaderEmails: resp.email
                })
            }
        })
        // 获取运维领导邮箱
        postOpsLeader({}).then(resp => {
            if (resp !== null) {
                this.setState({
                    opsLeaderEmail: resp.ops_leader
                })
            }
        })
    }
    // 地区变更
    handleRegionChange = (regionID) => {
        let zoneIDs = Object.keys(this.state.sellConf[regionID])
        this.setState({
            regionIDSelected: regionID,
            zones: zoneIDs.map(zoneID => { return { label: zoneID, value: zoneID } }),
            // 级联
            zoneIDSelected: undefined,
            versions: undefined,
            versionSelected: undefined,
            replicaNums: undefined,
            replicaNumSelected: undefined,
            shardNums: undefined,
            shardNumSelected: undefined,
            backupOneZoneIDSelected: undefined,
            backupTwoZoneIDSelected: undefined,
            instanceTypes: undefined,
            instanceTypeIDSelected: undefined,
        })
    }
    // 可用区变更
    handleZoneChange = (zoneID) => {
        let regionID = this.state.regionIDSelected
        let versions = Object.keys(this.state.sellConf[regionID][zoneID])
        this.setState({
            zoneIDSelected: zoneID,
            versions: versions.map(version => { return { label: version, value: version } }),
            // 级联
            versionSelected: undefined,
            replicaNums: undefined,
            replicaNumSelected: undefined,
            shardNums: undefined,
            shardNumSelected: undefined,
            backupOneZoneIDSelected: undefined,
            backupTwoZoneIDSelected: undefined,
            instanceTypes: undefined,
            instanceTypeIDSelected: undefined,
        })
    }
    // 版本变更
    handleVersionChange = (version) => {
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        let nodeNums = Object.keys(this.state.sellConf[regionID][zoneID][version])
        this.setState({
            versionSelected: version,
            replicaNums: nodeNums.map(nodeNum => { 
                const replicaNum =  nodeNum - 1
                let desc = "单节点"
                if (replicaNum>0){
                    desc = "多节点"
                }
                return { label: `${replicaNum}-${desc}`, value: replicaNum } 
            }),
            // 级联
            replicaNumSelected: undefined,
            shardNums: undefined,
            shardNumSelected: undefined,
            backupOneZoneIDSelected: undefined,
            backupTwoZoneIDSelected: undefined,
            instanceTypes: undefined,
            instanceTypeIDSelected: undefined,
        })
    }
    // 副本数变更
    handleReplicaNumChange = (replicaNum) => {
        let nodeNum = replicaNum + 1
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        let version = this.state.versionSelected
        let shardNums = Object.keys(this.state.sellConf[regionID][zoneID][version][nodeNum])
        this.setState({
            replicaNumSelected: replicaNum,
            shardNums: shardNums.map(shardNum => { return { label: shardNum, value: shardNum } }),
            // 级联
            shardNumSelected: undefined,
            backupOneZoneIDSelected: undefined,
            backupTwoZoneIDSelected: undefined,
            instanceTypes: undefined,
            instanceTypeIDSelected: undefined,
        })
    }
    // 副本1可用区变更
    handleBackupOneZoneChange = (zoneID) => {
        this.setState({
            backupOneZoneIDSelected: zoneID
        })

    }
    // 副本2可用区变更
    handleBackupTwoZoneChange = (zoneID) => {
        this.setState({
            backupTwoZoneIDSelected: zoneID
        })
    }
    // 分片数变更
    handleShardNumChange = (shardNum) => {
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        let version = this.state.versionSelected
        let replicaNum = this.state.replicaNumSelected
        let nodeNum = replicaNum + 1
        let instanceTypes = this.state.sellConf[regionID][zoneID][version][nodeNum][shardNum]
        let instanceTypeIDs = Object.keys(instanceTypes)
        this.setState({
            shardNumSelected: shardNum,
            instanceTypes: instanceTypeIDs.map(instanceTypeID => {
                const instanceType = instanceTypes[instanceTypeID];
                const MemorySizeGB = instanceType.MemorySizeGB;
                const desc = `内存: ${MemorySizeGB}GB | 规格：${instanceTypeID}`;
                return { label: desc, value: instanceTypeID, memorySizeGB: MemorySizeGB };
            }).sort((a, b) => {
                return a.memorySizeGB - b.memorySizeGB;
            }),
            // 级联
            instanceTypeIDSelected: undefined,
        })
    }
    // 规格变更
    handleInstanceTypeChange = (instanceTypeID) => {
        // let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        // let version = this.state.versionSelected
        // let replicaNum = this.state.replicaNumSelected
        // let nodeNum = replicaNum + 1
        // let shardNum = this.state.shardNumSelected
        // let instanceType = this.state.sellConf[regionID][zoneID][version][nodeNum][shardNum][instanceTypeID]
        let instanceNameIndex = `${this.props.supplier}_redis_${zoneID}-`
        this.setState({
            instanceTypeIDSelected: instanceTypeID,
            instanceNameIndex: instanceNameIndex
        }, this.costEstimate) 
    }
    // 实例名称变更
    handleInstanceNameChange = (e) => {
        console.log(e.target.value)
        this.setState({
            instanceName: e.target.value
        }, this.costEstimate)
    }
    // 运维审批人
    handleOpsAuditPeopleChange = (value) => {
        this.setState({
            opsAuditEmailSelected: value
        })
    }
    // 成本模型挂载
    handleCostTreeSelectChange = (value, lable, e) => {
        let path = value.split(".")
        if (path.length !== 3) {
            message.warn("请选择叶子节点！")
            this.setState({
                costNodeSelected: ""
            })
            return
        }
        this.setState({
            costNodeSelected: value
        })
    }
    // 申请理由
    handleApplyMsgChange = (e) => {
        this.setState({
            applyMsg: e.target.value
        })
    }
    handleSubmit = () => {
        let opsAuditEmail = this.state.opsAuditEmailSelected
        if (opsAuditEmail === undefined) {
            message.warn("请先选择运维审批人！")
            return
        }

        // 其他参数
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        if (regionID === undefined || zoneID === undefined) {
            message.warn("请选择地区信息！")
            return
        }

        let version = this.state.versionSelected
        if (version === undefined) {
            message.warn("请选择数据库版本！")
            return
        }
        let replicaNum = this.state.replicaNumSelected
        if (replicaNum === undefined) {
            message.warn("请选择副本数量")
            return
        }
        let nodeNum = replicaNum + 1
        let shardNum = this.state.shardNumSelected
        if (shardNum === undefined) {
            message.warn("请选择分片信息！")
            return
        }
        let instanceTypeID = this.state.instanceTypeIDSelected
        if (instanceTypeID === undefined) {
            message.warn("请选择规格信息！")
            return
        }
        let instanceType = this.state.sellConf[regionID][zoneID][version][nodeNum][shardNum][instanceTypeID]

        // 处理业务树节点id
        let costNodeSelected = this.state.costNodeSelected
        if (costNodeSelected === undefined) {
            message.warn("请选择业务树相关节点！")
            return
        }
        let nodes = costNodeSelected.split(".")
        let nodeID = parseInt(nodes[nodes.length - 1])

        // 处理实例名称
        let instanceName = `${this.state.instanceNameIndex}${this.state.instanceName}`

        // 处理申请信息
        let apply_msg =
            `申请购买${this.props.supplierName}Redis，预估费用为 ${this.state.tradePrice} 元/年。
数据库版本：${version}		
副本数：${replicaNum} 
分片数：${shardNum} 分片内存：${instanceType.MemorySizeGB}G
规格ID：${instanceTypeID}
Redis名称：${instanceName}，
可用区：${zoneID}
申请理由：${this.state.applyMsg}`.replaceAll("\t", "")

        let arg = {
            "dry_run": false,
            "ops_audit_email": this.state.opsAuditEmailSelected,
            "apply_msg": apply_msg,
            "cost_model_node_id": nodeID,

            "supplier": this.props.supplier,
            "region_id": regionID,
            "zone_id": zoneID,
            "instance_type_id": instanceTypeID,
            "instance_name": instanceName,
            "version": version,
            "replica_num": Number(replicaNum),
            "shard_num": Number(shardNum),
            "memory_gb": instanceType.MemorySizeGB
        }
        postBuyCloudRedis(arg).then((resp) => {
            if (resp !== null) {
                navigateModal(this.props.navigate, DoingOrderUrlSuffix, 5)
            }
        })
        console.log(arg)
    }
    // 询价
    costEstimate = () => {
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        let version = this.state.versionSelected
        let replicaNum = this.state.replicaNumSelected
        let nodeNum = replicaNum + 1
        let shardNum = this.state.shardNumSelected
        let instanceTypeID = this.state.instanceTypeIDSelected
        let instanceType = this.state.sellConf[regionID][zoneID][version][nodeNum][shardNum][instanceTypeID]

        // 检查
        if (regionID === undefined || zoneID === undefined) {
            return
        }
        if (version === undefined || replicaNum === undefined || shardNum === undefined || instanceTypeID === undefined) {
            return
        }

        console.log(instanceType)

        let arg = {
            supplier: this.props.supplier,
            regionID: regionID,
            zoneID: zoneID,
            version: version,
            instanceTypeID: instanceTypeID,
            replicaNum: Number(replicaNum),
            shardNum: Number(shardNum),
            memoryGB: instanceType.MemorySizeGB,
        }

        postCloudRedisPriceOrder(arg).then(resp => {
            this.setState({
                tradePrice: resp.data.TradePrice.toFixed(2)
            })
        })

    }
    // ### 树
    // 优先遍历树，增加节点title和key属性
    bfsAddProps = (treeData) => {
        let queue = [...treeData]
        while (queue.length > 0) {
            let node = queue.shift()
            node.title = node.name
            node.label = node.name
            node.value = node.path_id
            if (node.children !== undefined && node.children !== null) {
                queue.push(...node.children)
            }
        }
    }


    // 组件销毁前调用，清除一些事件(比如定时事件)
    componentWillUnmount() {
        this.setState = (state, callback) => {
            return
        }
    }
    render() {
        return (
            <SideContent>
                <RedisApllyFormBox>
                    <OrderTitle>{this.props.supplierName}Redis申请</OrderTitle>
                    <OrderCompactRow>
                        <OrderCol>
                            <span>运维审批：</span>
                            <Select
                                options={this.state.opsAuditEmails}
                                placeholder="运维审批人"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                onChange={this.handleOpsAuditPeopleChange}
                                value={this.state.opsAuditEmailSelected}
                            />
                        </OrderCol>
                        <OrderCenterCol>
                            <span>审批人员：</span>
                            <Tag color="#108ee9" icon={<UserOutlined />}>
                                {this.state.leaderEmails}，{this.state.opsLeaderEmail}
                            </Tag>
                        </OrderCenterCol>
                    </OrderCompactRow>
                    {/* 区域地区 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>地区城市：</span>
                            <Select
                                placeholder="请选择地区"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.regions}
                                value={this.state.regionIDSelected}
                                onChange={this.handleRegionChange}
                            />
                        </OrderCol>
                        <OrderCol>
                            <span>可用区域：</span>
                            <Select

                                placeholder="请选择可用区"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.zones}
                                value={this.state.zoneIDSelected}
                                onChange={this.handleZoneChange}
                            />
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 架构，版本 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>版&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;本：</span>
                            <Select
                                placeholder="请选择数据库版本"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.versions}
                                value={this.state.versionSelected}
                                onChange={this.handleVersionChange}
                            />
                        </OrderCol>
                        <OrderCol>
                            <span>副本数量：</span>
                            <Select
                                placeholder="副本数量"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.replicaNums}
                                value={this.state.replicaNumSelected}
                                onChange={this.handleReplicaNumChange}

                            />
                        </OrderCol>
                        <OrderCol>
                            <span>分片数量：</span>
                            <Select
                                placeholder="分片数量"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.shardNums}
                                value={this.state.shardNumSelected}
                                onChange={this.handleShardNumChange}
                            />
                        </OrderCol>

                    </OrderCompactRow>
                    {/* 副本可用区 */}
                    <OrderCompactRow className={this.state.replicaNumSelected - 1 < 0 ? "hide" : ""}>
                        <OrderCol className={this.state.replicaNumSelected - 1 < 0 ? "hide" : ""}>
                            <span>副本 1 可用区：</span>
                            <Select
                                placeholder="请选择副本1可用区"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.zones}
                                value={this.state.backupOneZoneIDSelected}
                                onChange={this.handleBackupOneZoneChange}
                            />
                        </OrderCol>
                        <OrderCol className={this.state.replicaNumSelected - 2 < 0 ? "hide" : ""}>
                            <span>副本 2 可用区：</span>
                            <Select

                                placeholder="请选择副本2可用区"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.zones}
                                value={this.state.backupTwoZoneIDSelected}
                                onChange={this.handleBackupTwoZoneChange}
                            />
                        </OrderCol>
                    </OrderCompactRow>

                    {/* 实例规格 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>实例规格：</span>
                            <Select
                                placeholder="请选择redis实例规格"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.instanceTypes}
                                value={this.state.instanceTypeIDSelected}
                                onChange={this.handleInstanceTypeChange}
                            />
                        </OrderCol>
                    </OrderCompactRow>


                    {/* 节点挂载 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>业务树相关节点：</span>
                            <TreeSelect
                                placeholder="请选择业务树相关节点"
                                treeData={this.state.costTreeData}
                                onChange={this.handleCostTreeSelectChange}
                                value={this.state.costNodeSelected}
                            />
                        </OrderCol>
                        {/* <OrderCol>
                            <span>监控挂载：</span>
                            <TreeSelect
                                disabled={true}
                                placeholder="请选择监控挂载节点"
                                treeData={this.state.costTreeData}
                                onChange={this.handleMonitorTreeSelectChange}
                                value={this.state.monitorNodeSelected}
                            />
                        </OrderCol> */}
                    </OrderCompactRow>
                    {/* 名称 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>实例名称：</span>
                            <span>{this.state.instanceNameIndex}</span>
                            <Input
                                placeholder="请填写实例名称"
                                value={this.state.instanceName}
                                onChange={this.handleInstanceNameChange}
                            />
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 申请陈述 */}
                    <OrderTextRow>
                        <OrderStartCol>
                            <span>申请陈述：</span>
                            <TextArea
                                placeholder="请填写申请理由，不少于5个字符"
                                style={{ height: "5vw" }}
                                value={this.state.applyMsg}
                                onChange={this.handleApplyMsgChange}
                            />
                        </OrderStartCol>
                    </OrderTextRow>
                    <OrderCompactRow>
                        <OrderEndCol>
                            <FeeSpan>
                                {/* 折扣：{this.state.discount}，折后 */}
                                费用预估：{this.state.tradePrice} 元/年&nbsp;
                                <MoneyCollectOutlined />
                            </FeeSpan>
                        </OrderEndCol>
                    </OrderCompactRow>
                    {/* 费用预估 */}
                    <OrderSubmit>
                        <Button type="primary" onClick={this.handleSubmit}>
                            提交
                        </Button>
                        <Button type="dashed" onClick={this.handleCancel}>
                            取消
                        </Button>
                    </OrderSubmit>
                </RedisApllyFormBox>
            </SideContent>
        );
    }
}

RedisApllyForm.propTypes = {
    supplier: PropTypes.string.isRequired,
    supplierName: PropTypes.string.isRequired,
}

export default withRouter(RedisApllyForm);