import { Component } from "react";
import { Link } from "react-router-dom";
import OrderCard from "@/components/OrderTitleCard";
import { OrderRow, ShadeForm, SideContent } from "@/common/styleLayout";
import styled from "styled-components";

// css-js
const ServerForm = styled(ShadeForm)`
  width: 58%;
  height: 85%;
`
const CardTitleSpan = styled.span`
  font-weight: bold;
  font-size: middle;
`


export default class ServerOrder extends Component {
  render() {
    return (
      <SideContent>
        <ServerForm>
          <OrderRow>
            <Link to="tx/apply-form">
              <OrderCard title={<CardTitleSpan>服务器</CardTitleSpan>} imgURL="/logo/cloudService/tx_logo_mini.png" />
            </Link>
            <Link to="ali/apply-form">
              <OrderCard title={<CardTitleSpan>服务器</CardTitleSpan>} imgURL="/logo/cloudService/ali_logo_mini.png" />
            </Link>
            <Link to="hw/apply-form">
              <OrderCard title={<CardTitleSpan>服务器</CardTitleSpan>} imgURL="/logo/cloudService/hw_logo_mini.png" />
            </Link>
            <Link to="ks/apply-form">
              <OrderCard title={<CardTitleSpan>服务器</CardTitleSpan>} imgURL="/logo/cloudService/ks_logo_mini.png" />
            </Link>
          </OrderRow>
        </ServerForm>
      </SideContent>
    );
  }
}
