import { Component } from "react";
import { Select, TreeSelect, Button, InputNumber, Input, message, Tag } from "antd";
import styled from "styled-components";
import { MoneyCollectOutlined, UserOutlined } from "@ant-design/icons";
import withRouter from "@/util/withRouter";
import { ServerApllyForm } from "../style"
import {
	OrderRow,
	OrderTitle,
	SideContent,
	OrderSubmit,
	OrderCol,
	OrderCenterCol,
	OrderStartCol,
	OrderEndCol,
	OrderCompactRow
} from "@/common/styleLayout";
import {
	postModelTree,
	postOpsLeader,
	requestCommonOrder,
	requestLeaderEmail,
	requestOpsMember,
	requestServerOneYearPrice,
	// requestSuperLeaderEmails,
	requestTxDataDisk,
	requestTxOsImage,
	requestTxRegion,
	requestTxServerFamilyTypeConf,
	requestTxSysDisk,
	requestTxZone
} from "@/request/api";
import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";


const { TextArea } = Input;

// css-js
export const FeeSpan = styled.span`
  color: red;
  font-size: large;
`
class TxServerApplyForm extends Component {
	diskTypeNameMap = {
		LOCAL_BASIC: "本地硬盘",
		LOCAL_SSD: "本地SSD硬盘",
		CLOUD_BASIC: "普通云硬盘",
		CLOUD_SSD: "SSD云硬盘",
		CLOUD_PREMIUM: "高性能云硬盘",
		CLOUD_BSSD: "通用性SSD云硬盘"
	}
	state = {
		opsAuditEmails: [],
		opsAuditEmailSelected: undefined,
		leaderEmails: undefined,
		opsLeaderEmail: undefined,
		regions: [],
		regionSelected: undefined,
		zones: [],
		zoneSelected: undefined,
		familyTypeConfsMap: undefined,
		familyConfs: [],
		familyConfSelect: undefined,
		typeConfs: [],
		typeConfSelect: undefined,
		typeConfNameSelected: undefined,
		cpuCoreNumSelected: undefined,
		memorySelected: undefined,
		sysDiskTypes: [],
		sysDiskTypeSelected: undefined,
		dataDiskTypes: [],
		dataDiskTypeSelected: undefined,
		dataDiskSize: 50,
		dataDiskMap: undefined,
		minDataDiskSize: 1,
		maxDataDiskSize: 1024,
		costTreeData: [],
		osImages: [],
		osImageSelected: undefined,
		osImageNameSelected: undefined,
		serverNameIndex: undefined,
		serverName: undefined,
		applyMsg: undefined,
		costNodeSelected: undefined,
		monitorNodeSelected: undefined,
		instanceDiscountPrice: undefined,
		discount: undefined
	}
	// 运维审批人
	handleOpsAuditPeopleChange = (value) => {
		this.setState({
			opsAuditEmailSelected: value
		})
	}
	// 地区
	handleRegionChange = (value) => {
		this.setState({
			regionSelected: value
		})
		let arg = {
			product: "cvm",
			region: value
		}
		requestTxZone(arg).then((resp) => {
			this.setState({
				zones: resp.zones.map(item => { return { label: item.zone_name, value: item.zone } }),
				zoneSelected: undefined,
				familyConfSelect: undefined,
				typeConfSelect: undefined,
				sysDiskTypeSelected: undefined,
				dataDiskTypeSelected: undefined
			})
		})
		requestTxOsImage(arg).then((resp) => {
			this.setState({
				osImages: resp.os_image.map(item => { return { label: item.image_name, value: item.image_id } }),
				osImageSelected: undefined
			})
		})

	}
	// 可用区
	handleZoneChange = (value) => {
		this.setState({
			zoneSelected: value
		})
		let arg = {
			region: this.state.regionSelected,
			zone: value
		}
		requestTxServerFamilyTypeConf(arg).then(resp => {
			let familyTypeConfsMap = {}
			let familyConfs = []
			resp.server_family_types.forEach(element => {
				if (element.InstanceFamily in familyTypeConfsMap) {
					familyTypeConfsMap[element.InstanceFamily].push(element)
				} else {
					familyTypeConfsMap[element.InstanceFamily] = [element]
					familyConfs.push({
						label: element.TypeName,
						value: element.InstanceFamily
					})
				}
			});
			this.setState({
				familyTypeConfsMap: familyTypeConfsMap,
				familyConfs: familyConfs,
				familyConfSelect: undefined,
				typeConfSelect: undefined,
				sysDiskTypeSelected: undefined,
				dataDiskTypeSelected: undefined,
				instanceDiscountPrice: undefined
			})
		})
	}
	// 机型族
	handleFamilyConfChange = (value) => {
		this.setState({
			familyConfSelect: value
		})
		let arg = {
			"region": this.state.regionSelected,
			"zone": this.state.zoneSelected,
			"server_family_conf": value
		}
		// 获取机型
		this.setState({
			typeConfs: this.state.familyTypeConfsMap[value].map(item => {
				let typeName = `处理器（${item.CPU}核 ${item.CPUType}），内存（${item.Memory}G）`
				return { label: typeName, value: item.InstanceType, cpu: item.CPU, memory: item.Memory }
			}),
			typeConfSelect: undefined,
			instanceDiscountPrice: undefined
		})
		// 获取系统盘
		requestTxSysDisk(arg).then((resp) => {
			if (resp.disk_conf.length === 0) {
				message.error(`没有可售系统硬盘（${this.state.zoneSelected}），请选择其他可用区！`, 5)
				return
			}
			this.setState({
				sysDiskTypes: resp.disk_conf.map(item => {
					return { label: this.diskTypeNameMap[item.disk_type], value: item.disk_type }
				}),
				sysDiskTypeSelected: undefined
			})
		})
		// 获取数据盘
		requestTxDataDisk(arg).then((resp) => {
			if (resp.disk_conf.length === 0) {
				message.error(`没有可售数据硬盘（${this.state.zoneSelected}），请选择其他可用区！`, 5)
				return
			}
			let dataDiskMap = {}
			let dataDiksOptions = []
			resp.disk_conf.forEach(item => {
				dataDiskMap[item.disk_type] = item
				dataDiksOptions.push({ label: this.diskTypeNameMap[item.disk_type], value: item.disk_type })
			})
			this.setState({
				dataDiskTypes: dataDiksOptions,
				dataDiskMap: dataDiskMap,
				dataDiskTypeSelected: undefined
			})
		})
	}
	// 处理机型选择
	handleTypeConfChange = (value, option) => {
		console.log(option)
		this.setState({
			typeConfSelect: value,
			typeConfNameSelected: option.label,
			cpuCoreNumSelected: option.cpu,
			memorySelected: option.memory
		}, this.costEstimate)
	}
	// 系统盘变更
	handleSysDiskChange = (value) => {
		this.setState({
			sysDiskTypeSelected: value
		}, this.costEstimate)
	}
	// 数据盘变更
	handleDataDiskChange = (value) => {
		this.setState({
			dataDiskTypeSelected: value,
			minDataDiskSize: this.state.dataDiskMap[value].min_disk_size,
			maxDataDiskSize: this.state.dataDiskMap[value].max_disk_size
		}, this.costEstimate)
	}
	// 数据盘大小变更
	handleDataDiskSizeChange = (value) => {
		this.setState({
			dataDiskSize: value
		}, this.costEstimate)
	}
	// 操作系统镜像变更
	handleOsImageChange = (value, option) => {
		this.setState({
			osImageSelected: value,
			osImageNameSelected: option.label
		}, this.costEstimate)
	}
	handleServerNameIndex = () => {
		if (this.state.osImageNameSelected === undefined) {
			return
		}
		let osName = this.state.osImageNameSelected.replaceAll(/[^a-zA-Z]/g, "")
		this.setState({
			serverNameIndex: `tx_server_${this.state.zoneSelected}_${this.state.cpuCoreNumSelected}Core_${this.state.memorySelected}G_${osName}-`
		})
	}
	// 处理服务器名称
	handleServerNameChange = (e) => {
		this.setState({
			serverName: e.target.value
		})
	}
	// 成本模型挂载
	handleCostTreeSelectChange = (value, lable, e) => {
		let path = value.split(".")
		if (path.length !== 3) {
			message.warn("请选择叶子节点！")
			this.setState({
				costNodeSelected: ""
			})
			return
		} else {
			this.setState({
				costNodeSelected: value
			})
		}

	}
	// 监控模型挂载
	// handleMonitorTreeSelectChange = (value) => {
	// }
	// 申请理由
	handleApplyMsgChange = (e) => {
		this.setState({
			applyMsg: e.target.value
		})
	}
	handleSubmit = () => {
		if (this.state.opsAuditEmailSelected === undefined || this.state.opsAuditEmailSelected.length === 0) {
			message.warn("请先选择运维审批人！")
			return
		}
		if (this.state.costNodeSelected === undefined || this.state.costNodeSelected.length === 0) {
			message.warn("请先选择业务树相关节点！")
			return
		}
		let nodes = this.state.costNodeSelected.split(".")
		let apply_info = {
			productType: "cvm",
			ops_audit_email: this.state.opsAuditEmailSelected,
			region: this.state.regionSelected,
			zone: this.state.zoneSelected,
			TypeConf: this.state.typeConfSelect,
			SysDiskType: this.state.sysDiskTypeSelected,
			DataDiskType: this.state.dataDiskTypeSelected,
			DataDiskSize: this.state.dataDiskSize,
			OsImage: this.state.osImageSelected,
			ServerName: this.state.serverNameIndex + this.state.serverName,
			CostModelNodeID: parseInt(nodes[nodes.length - 1]),
			MonitorModelNodeID: this.state.monitorNodeSelected,
			// 是否预检而不直接购买实例
			DryRun: false
		}
		let arg = {
			order_type: "tx_server_apply",
			apply_info: JSON.stringify(apply_info),
			apply_msg: this.state.applyMsg,
			exigency: 1
		}
		if (apply_info.region === undefined || apply_info.region.length === 0) {
			message.warn("请先选择地区！")
			return
		}
		if (apply_info.zone.length === undefined || apply_info.zone.length === 0) {
			message.warn("请先选择可用区！")
			return
		}
		if (apply_info.TypeConf === undefined || apply_info.TypeConf.length === 0) {
			message.warn("请先选择规格机型和硬件配置！")
			return
		}
		if (apply_info.SysDiskType === undefined || apply_info.SysDiskType.length === 0) {
			message.warn("请先选择系统盘类型！")
			return
		}
		if (apply_info.DataDiskType === undefined || apply_info.DataDiskType.length === 0) {
			message.warn("请先选择数据盘类型！")
			return
		}
		if (apply_info.DataDiskSize === 0) {
			message.warn("数据盘大小至少为1个G！")
			return
		}
		if (apply_info.OsImage === undefined || apply_info.OsImage.length === 0) {
			message.warn("请先选择操作系统镜像！")
			return
		}
		// todo 假如监控模型是开启
		// if (apply_info.MonitorModelNodeID === undefined || apply_info.MonitorModelNodeID.length === 0) {
		// 	message.warn("监控挂载为空，将默认关闭该资源的监控与报警服务！")
		// }
		if (apply_info.ServerName === undefined || apply_info.ServerName.length === 0) {
			message.warn("请先填写服务器名称！")
			return
		}
		if (arg.apply_msg === undefined || arg.apply_msg.length <= 5) {
			message.warn("申请陈述不能少于5个字！")
			return
		}

		arg.apply_msg =
			`申请购买腾讯云服务器，预估费用为 ${this.state.instanceDiscountPrice} 元/年。
		操作系统：${apply_info.OsImage}
		服务器配置：${this.state.typeConfNameSelected} ，数据盘 ${apply_info.DataDiskSize}G
		服务器名称：${apply_info.serverName}，地区：${apply_info.region}，可用区：${apply_info.zone}
		申请理由：${arg.apply_msg}`.replaceAll("\t", "")
		requestCommonOrder(arg).then((resp) => {
			if (resp !== null) {
				navigateModal(this.props.navigate, DoingOrderUrlSuffix, 5)
			}
		})
	}
	costEstimate = () => {
		this.handleServerNameIndex()
		let arg = {
			region: this.state.regionSelected,
			zone: this.state.zoneSelected,
			server_type_conf: this.state.typeConfSelect,
			sys_disk_type: this.state.sysDiskTypeSelected,
			data_disk_type: this.state.dataDiskTypeSelected,
			data_disk_size: this.state.dataDiskSize,
			os_image: this.state.osImageSelected
		}
		if (arg.region === undefined || arg.region.length === 0) {
			return
		}
		if (arg.zone === undefined || arg.zone.length === 0) {
			return
		}
		if (arg.server_type_conf === undefined || arg.server_type_conf.length === 0) {
			return
		}
		if (arg.sys_disk_type === undefined || arg.sys_disk_type.length === 0) {
			return
		}
		if (arg.data_disk_type === undefined || arg.data_disk_type.length === 0) {
			return
		}
		if (arg.os_image === undefined || arg.os_image.length === 0) {
			return
		}
		requestServerOneYearPrice(arg).then((resp) => {
			this.setState({
				instanceDiscountPrice: resp.DiscountPrice,
				discount: resp.Discount
			})
		})
	}

	// ### 树
	// 优先遍历树，增加节点title和key属性
	bfsAddProps = (treeData) => {
		let queue = [...treeData]
		while (queue.length > 0) {
			let node = queue.shift()
			node.title = node.name
			node.label = node.name
			node.value = node.path_id
			if (node.children !== undefined && node.children !== null) {
				queue.push(...node.children)
			}
		}
	}
	componentDidMount() {
		// 获取运维审批
		requestOpsMember({}).then((resp) => {
			this.setState({
				opsAuditEmails: resp.members.map(item => {
					return { label: item.substring(0, item.lastIndexOf("@")), value: item }
				})
			});
		});
		// 获取领导
		// requestSuperLeaderEmails({}).then((resp) => {
		// 	this.setState({
		// 		leaderEmails: resp.emails.join(" ")
		// 	})
		// })
		// 获取领导邮箱
		requestLeaderEmail({}).then(resp => {
			if (resp !== null) {
				this.setState({
					leaderEmails: resp.email
				})
			}
		})
		// 获取运维领导邮箱
		postOpsLeader({}).then(resp => {
			if (resp !== null) {
				this.setState({
					opsLeaderEmail: resp.ops_leader
				})
			}
		})
		let arg = {
			product: "cvm"
		}
		// 获取地区
		requestTxRegion(arg).then((resp) => {
			this.setState({
				regions: resp.regions.map(item => { return { label: item.region_name, value: item.region } })
			})
		})
		// 成本模型树
		postModelTree({ "model_name": "cost", "attach_path_id": true }).then((resp) => {
			let treeData = resp.data
			this.bfsAddProps(treeData)
			this.setState({
				costTreeData: treeData
			})
		})
	}
	render() {
		return (
			<SideContent>
				<ServerApllyForm>
					<OrderTitle>腾讯云普通服务器申请</OrderTitle>
					<OrderCompactRow>
						<OrderCol>
							<span>运维审批：</span>
							<Select
								options={this.state.opsAuditEmails}
								placeholder="运维审批人员"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleOpsAuditPeopleChange}
								value={this.state.opsAuditEmailSelected}
							/>
						</OrderCol>
						<OrderCenterCol>
							<span>审批人员：</span>
							<Tag color="#108ee9" icon={<UserOutlined />}>
								{this.state.leaderEmails}，{this.state.opsLeaderEmail}
							</Tag>
						</OrderCenterCol>
					</OrderCompactRow>
					{/* 区域地区 */}
					<OrderCompactRow>
						<OrderCol>
							<span>地区城市：</span>
							<Select
								options={this.state.regions}
								placeholder="请选择地区"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleRegionChange}
							/>
						</OrderCol>
						<OrderCol>
							<span>可用区域：</span>
							<Select
								options={this.state.zones}
								placeholder="请选择可用区"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleZoneChange}
								value={this.state.zoneSelected}
							/>
						</OrderCol>
					</OrderCompactRow>
					{/* 机型规格 */}
					<OrderCompactRow>
						<OrderCol>
							<span>规格机型：</span>
							<Select
								options={this.state.familyConfs}
								placeholder="请选择服务器规格机型"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleFamilyConfChange}
								value={this.state.familyConfSelect}
							/>
						</OrderCol>
						<OrderCol>
							<span>硬件配置：</span>
							<Select
								options={this.state.typeConfs}
								placeholder="请选择服务器硬件配置"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleTypeConfChange}
								value={this.state.typeConfSelect}
							/>
						</OrderCol>
					</OrderCompactRow>
					{/* 磁盘配置 */}
					<OrderCompactRow>
						<OrderCol>
							<span>系统硬盘：</span>
							<Select
								options={this.state.sysDiskTypes}
								placeholder="硬盘类型"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleSysDiskChange}
								value={this.state.sysDiskTypeSelected}
							/>
						</OrderCol>
						<OrderCol>
							<span>数据硬盘：</span>
							<Select
								options={this.state.dataDiskTypes}
								placeholder="硬盘类型"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleDataDiskChange}
								value={this.state.dataDiskTypeSelected}
							/>
						</OrderCol>
						<OrderCenterCol>
							<span>数据盘规格：</span>
							<InputNumber
								min={this.state.minDataDiskSize}
								max={this.state.maxDataDiskSize}
								style={{ width: "5vw" }}
								value={this.state.dataDiskSize}
								onChange={this.handleDataDiskSizeChange}
							/>
							<span>&nbsp;GB</span>
						</OrderCenterCol>
					</OrderCompactRow>
					{/* 系统镜像 */}
					<OrderCompactRow>
						<OrderCol>
							<span>系统镜像：</span>
							<Select
								options={this.state.osImages}
								placeholder="请选择操作系统镜像"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleOsImageChange}
								value={this.state.osImageSelected}
							/>
						</OrderCol>
					</OrderCompactRow>
					{/* 节点挂载 */}
					<OrderCompactRow>
						<OrderCol>
							<span>业务树相关节点：</span>
							<TreeSelect
								placeholder="请选择业务树相关节点"
								treeData={this.state.costTreeData}
								onSelect={this.handleCostTreeSelectChange}
								value={this.state.costNodeSelected}
							/>
						</OrderCol>
						{/* <OrderCol>
							<span>监控挂载：</span>
							<TreeSelect
								placeholder="请选择监控挂载节点"
								treeData={this.state.costTreeData}
								onChange={this.handleMonitorTreeSelectChange}
								value={this.state.monitorNodeSelected}
								disabled
							/>
						</OrderCol> */}
					</OrderCompactRow>
					{/* 服务器名称 */}
					<OrderCompactRow>
						<OrderCol>
							<span>机器名称：</span>
							<span>{this.state.serverNameIndex}</span>
							<Input
								placeholder="请填写实例名称"
								value={this.state.serverName}
								onChange={this.handleServerNameChange}
							/>
						</OrderCol>
					</OrderCompactRow>
					{/* 申请陈述 */}
					<OrderRow>
						<OrderStartCol>
							<span>申请陈述：</span>
							<TextArea
								placeholder="请填写申请理由，不少于5个字符"
								style={{ height: "7vw" }}
								value={this.state.applyMsg}
								onChange={this.handleApplyMsgChange}
							/>
						</OrderStartCol>
					</OrderRow>
					<OrderRow>
						<OrderEndCol>
							<FeeSpan>
								{/* 折扣：{this.state.discount}，折后 */}
								费用预估：{this.state.instanceDiscountPrice} 元/年&nbsp;
								<MoneyCollectOutlined />
							</FeeSpan>
						</OrderEndCol>
					</OrderRow>
					{/* 费用预估 */}
					<OrderSubmit>
						<Button type="primary" onClick={this.handleSubmit}>
							提交
						</Button>
						<Button type="dashed" onClick={this.handleCancel}>
							取消
						</Button>
					</OrderSubmit>
				</ServerApllyForm>
			</SideContent>
		);
	}
}

export default withRouter(TxServerApplyForm);