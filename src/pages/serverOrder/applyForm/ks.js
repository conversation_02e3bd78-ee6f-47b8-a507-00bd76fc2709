import { Component } from "react";
import ServerApplyForm from "@/pages/serverOrder/applyForm/common"
import withRouter from "@/util/withRouter";

class KingsoftServer extends Component {

	render() {
		return (
			<ServerApplyForm
				supplier="kingsoft"
				supplierName="金山云"
				instanceFamilyUrl="https://help.aliyun.com/zh/ecs/user-guide/overview-of-instance-families?spm=a2c4g.11186623.0.0.3a2136126ZPw7D"
				diskTypeUrl="https://help.aliyun.com/zh/ecs/user-guide/disks-2?spm=a2c4g.11186623.0.0.32775d41x4vWlV"
			/>
		);
	}
}

export default withRouter(KingsoftServer);