import { Component } from "react";
import { Select, TreeSelect, Button, InputNumber, Input, message, Tag, Popover } from "antd";
import styled from "styled-components";
import { MoneyCollectOutlined, UserOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import withRouter from "@/util/withRouter";
import { ServerApllyForm } from "../style"
import PropTypes from 'prop-types';
import {
    OrderRow,
    OrderTitle,
    SideContent,
    OrderSubmit,
    OrderCol,
    OrderCenterCol,
    OrderStartCol,
    OrderEndCol,
    OrderCompactRow
} from "@/common/styleLayout";
import {
    postCloudServerSellConf,
    postCloudDiskSellConf,
    postCloudImageConf,
    postCloudServerPriceOrder,
    postModelTree,
    postOpsLeader,
    requestLeaderEmail,
    requestOpsMember,
    postBuyCloudServer,
} from "@/request/api";
import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";

const { TextArea } = Input;

// css-js
export const FeeSpan = styled.span`
  color: red;
  font-size: large;
`

const SysDiskSize = 45
const simpleSupplier = {
    "ali": "ali",
    "huawei": "hw",
    "kingsoft": "ks",
    "tencent": "tc"
}
class ServerApplyForm extends Component {
    state = {
        // 服务器售卖规格配置
        sellConf: undefined,
        // 区域
        regions: [],
        regionIDSelected: undefined,
        zones: [],
        zoneIDSelected: undefined,
        // 实例规格
        familyTypes: [],
        familyTypeSelected: undefined,
        instanceTypes: [],
        instanceTypeIDSelected: undefined,
        // 硬盘
        diskSellConf: undefined,
        sysDiskTypes: [],
        sysDiskTypeSelected: undefined,
        dataDiskTypes: [],
        dataDiskTypeSelected: undefined,
        minDataDiskSize: 1,
        maxDataDiskSize: 1024,
        dataDiskSize: 50,
        // 镜像
        images: [],
        imageIDSelected: undefined,
        imageNameSelected: undefined,
        // 服务器名称
        instanceNameIndex: undefined,
        instanceName: undefined,
        applyMsg: undefined,
        // 费用信息
        tradePrice: undefined,

        // 审批信息
        opsAuditEmails: [],
        opsAuditEmailSelected: undefined,
        leaderEmails: undefined,
        opsLeaderEmail: undefined,

        // 模型信息
        costTreeData: [],
        costNodeSelected: undefined,
        monitorNodeSelected: undefined,
    }
    instanceFamilyDesc = (
        <div>
            <a href={this.props.instanceFamilyUrl} target="_blank" rel="noopener noreferrer">规格族详情说明🔗</a>
        </div>
    )
    diskTypeDesc = (
        <div>
            <a href={this.props.diskTypeUrl} target="_blank" rel="noopener noreferrer">硬盘详情说明🔗</a>
        </div>
    )
    // 运维审批人
    handleOpsAuditPeopleChange = (value) => {
        this.setState({
            opsAuditEmailSelected: value
        })
    }
    // 地区变更
    handleRegionChange = (regionID) => {
        let regionIDSelected = regionID
        let zoneIDs = Object.keys(this.state.sellConf[regionIDSelected])
        this.setState({
            regionIDSelected: regionIDSelected,
            zones: zoneIDs.map(zoneID => { return { label: zoneID, value: zoneID } }),
            zoneIDSelected: undefined,
            familyType: undefined,
            familyTypeSelected: undefined,
            instanceTypes: undefined,
            instanceTypeIDSelected: undefined,
            diskSellConf: undefined,
            sysDiskTypes: undefined,
            sysDiskTypeSelected: undefined,
            dataDiskTypes: undefined,
            dataDiskTypeSelected: undefined,
            tradePrice: undefined
        })
    }
    // 可用区变更
    handleZoneChange = (zoneID) => {
        let regionIDSelected = this.state.regionIDSelected
        let zoneIDSelected = zoneID
        let familyTypes = Object.keys(this.state.sellConf[regionIDSelected][zoneIDSelected])
        this.setState({
            zoneIDSelected: zoneIDSelected,
            familyTypes: familyTypes.map(familyType => { return { label: familyType, value: familyType } }),
            familyTypeSelected: undefined,
            instanceTypes: undefined,
            instanceTypeIDSelected: undefined,
            diskSellConf: undefined,
            sysDiskTypes: undefined,
            sysDiskTypeSelected: undefined,
            dataDiskTypes: undefined,
            dataDiskTypeSelected: undefined,
            tradePrice: undefined,
        })
    }
    // 机型族
    handleFamilyTypeChange = (familyType) => {
        let regionIDSelected = this.state.regionIDSelected
        let zoneIDSelected = this.state.zoneIDSelected
        let familyTypeSelected = familyType
        let instanceTypes = this.state.sellConf[regionIDSelected][zoneIDSelected][familyTypeSelected]
        let instanceTypeIDs = Object.keys(instanceTypes)
        this.setState({
            familyTypeSelected: familyTypeSelected,
            instanceTypes: instanceTypeIDs.map(instanceTypeID => {
                const instanceType = instanceTypes[instanceTypeID];
                const { CPU, MemorySizeGB } = instanceType;
                const desc = `核心数:${CPU.CoreCount} | 内存: ${MemorySizeGB}GB | 规格：${instanceTypeID}`;
                return { label: desc, value: instanceTypeID, coreCount: CPU.CoreCount, memorySizeGB: MemorySizeGB };
            }).sort((a, b) => {
                if (a.coreCount === b.coreCount) {
                    return a.memorySizeGB - b.memorySizeGB;
                }
                return a.coreCount - b.coreCount;
            }),
            instanceTypeIDSelected: undefined,
            diskSellConf: undefined,
            sysDiskTypes: undefined,
            sysDiskTypeSelected: undefined,
            dataDiskTypes: undefined,
            dataDiskTypeSelected: undefined,
            tradePrice: undefined,
        })
    }
    // 处理机型选择
    handleInstanceTypeChange = (instanceTypeID, option) => {
        let instanceTypeIDSelected = instanceTypeID
        let regionIDSelected = this.state.regionIDSelected
        let zoneIDSelected = this.state.zoneIDSelected
        this.setState(
            {
                instanceTypeIDSelected: instanceTypeIDSelected
            }
        )
        // 请求硬盘参数
        let arg = {
            "supplier": this.props.supplier,
            "region_id": regionIDSelected,
            "zone_id": zoneIDSelected,
            "instance_type_id": instanceTypeIDSelected
        }
        postCloudDiskSellConf(arg).then((resp) => {
            let diskSellConf = resp.data
            let sysDisks = Object.keys(diskSellConf["SystemDisk"])
            let dataDisks = Object.keys(diskSellConf["DataDisk"])
            this.setState({
                diskSellConf: diskSellConf,
                sysDiskTypes: sysDisks.map(sysDisk => { return { label: sysDisk, value: sysDisk } }),
                dataDiskTypes: dataDisks.map(dataDisk => { return { label: dataDisk, value: dataDisk } }),
                sysDiskTypeSelected: undefined,
                dataDiskTypeSelected: undefined,
                tradePrice: undefined,
            })
        })
    }
    // 系统盘变更
    handleSysDiskChange = (diskType) => {
        this.setState({
            sysDiskTypeSelected: diskType
        }, this.costEstimate)
    }
    // 数据盘变更
    handleDataDiskChange = (diskType) => {
        this.setState({
            dataDiskTypeSelected: diskType,
            minDataDiskSize: this.state.diskSellConf["DataDisk"][diskType]["Min"],
            maxDataDiskSize: this.state.diskSellConf["DataDisk"][diskType]["Max"]
        }, this.costEstimate)
    }
    // 数据盘大小变更
    handleDataDiskSizeChange = (value) => {
        this.setState({
            dataDiskSize: value
        }, this.costEstimate)
    }
    // 操作系统镜像变更
    handleImageChange = (imageID, option) => {
        let regionIDSelected = this.state.regionIDSelected
        // let zoneIDSelected = this.state.zoneIDSelected
        // let familyTypeSelected = this.state.familyTypeSelected
        // let instanceTypeSelected = this.state.instanceTypeIDSelected
        // let instanceType = this.state.sellConf[regionIDSelected][zoneIDSelected][familyTypeSelected][instanceTypeSelected]
        // let zoneID = zoneIDSelected.replace(/-/g, "_")
        let osType = option.label.split("_")[0]
        let instanceNameIndex = `${simpleSupplier[this.props.supplier]}_server_${regionIDSelected}_${osType}-`
        this.setState({
            instanceNameIndex: instanceNameIndex,
            imageIDSelected: imageID,
            imageNameSelected: option.label,
        }, this.costEstimate)
    }
    // 处理服务器名称
    handleInstanceNameChange = (e) => {
        this.setState({
            instanceName: e.target.value
        })
    }
    // 成本模型挂载
    handleCostTreeSelectChange = (value, lable, e) => {
        let path = value.split(".")
        if (path.length !== 3) {
            message.warn("请选择叶子节点！")
            this.setState({
                costNodeSelected: ""
            })
            return
        } else {
            this.setState({
                costNodeSelected: value
            })
        }

    }
    // 监控模型挂载
    // handleMonitorTreeSelectChange = (value) => {
    // }
    // 申请理由
    handleApplyMsgChange = (e) => {
        this.setState({
            applyMsg: e.target.value
        })
    }
    // 提交购买服务器订单
    handleSubmit = () => {
        let opsAuditEmailSelected = this.state.opsAuditEmailSelected
        let supplier = this.props.supplier
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        let instanceFamily = this.state.familyTypeSelected
        let instanceTypeID = this.state.instanceTypeIDSelected
        let sysDiskType = this.state.sysDiskTypeSelected
        let dataDiskType = this.state.dataDiskTypeSelected
        let dataDiskSize = this.state.dataDiskSize
        let imageID = this.state.imageIDSelected
        let imageName = this.state.imageNameSelected
        let instanceName = `${this.state.instanceNameIndex}${this.state.instanceName}`
        let applyMsg = this.state.applyMsg
        let tradePrice = this.state.tradePrice

        // 检查
        if (opsAuditEmailSelected === undefined) {
            message.warn("请先选择运维审批人！")
            return
        }
        if (regionID === undefined || zoneID === undefined) {
            message.warn("请选择地区信息！")
            return
        }
        if (instanceTypeID === undefined) {
            message.warn("请选择规格配置")
            return
        }
        if (sysDiskType === undefined || dataDiskType === undefined || dataDiskSize === undefined) {
            message.warn("请选择硬盘配置")
            return
        }
        if (imageID === undefined) {
            message.warn("请选择操作系统")
            return
        }

        if (instanceName === undefined) {
            message.warn("填写实例名称")
            return
        }
        if (applyMsg === undefined) {
            message.warn("填写申请理由")
            return
        }
        // 处理业务树节点id
        let nodes = this.state.costNodeSelected
        if (nodes === undefined) {
            message.warn("请选择业务树相关节点")
            return
        }
        let nodeID = parseInt(nodes.split(".")[nodes.length - 1])

        let instanceType = this.state.sellConf[regionID][zoneID][instanceFamily][instanceTypeID]
        // 处理申请信息
        let apply_msg =
            `申请购买${this.props.supplierName}服务器，预估费用为 ${tradePrice} 元/年
操作系统：${imageName}
CPU：${instanceType.CPU.CoreCount}，内存：${instanceType.MemorySizeGB}G ，数据盘: ${dataDiskSize}G
规格ID：${instanceTypeID}
服务器名称：${instanceName}
可用区：${zoneID}
申请理由：${applyMsg}`.replaceAll("\t", "")

        let orderArg = {
            // "dry_run": true,
            "ops_audit_email": opsAuditEmailSelected,
            "supplier": supplier,
            "region_id": regionID,
            "zone_id": zoneID,
            "instance_type_id": instanceTypeID,
            "sys_disk": {
                "category": sysDiskType,
                "size_gb": SysDiskSize
            },
            "data_disk": [
                {
                    "category": dataDiskType,
                    "size_gb": dataDiskSize
                }
            ],
            "image_id": imageID,

            "cost_model_node_id": nodeID,
            "instance_name": instanceName,
            "apply_msg": apply_msg,
            "password": "Qyap88qwer~!",
        }

        postBuyCloudServer(orderArg).then((resp) => {
            if (resp !== null) {
                navigateModal(this.props.navigate, DoingOrderUrlSuffix, 5)
            }
        })
    }

    // 提交询价订单
    costEstimate = () => {
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        let instanceTypeID = this.state.instanceTypeIDSelected
        let sysDiskType = this.state.sysDiskTypeSelected
        let dataDiskType = this.state.dataDiskTypeSelected
        let dataDiskSize = this.state.dataDiskSize
        let imageID = this.state.imageIDSelected
        // 检查
        if (regionID === undefined || zoneID === undefined) {
            return
        }
        if (instanceTypeID === undefined) {
            return
        }
        if (sysDiskType === undefined || dataDiskType === undefined || dataDiskSize === undefined) {
            return
        }
        if (imageID === undefined) {
            return
        }
        let priceOrderArgs = {
            "supplier": this.props.supplier,
            "region_id": regionID,
            "zone_id": zoneID,
            "instance_type_id": instanceTypeID,
            "sys_disk_type": sysDiskType,
            "sys_disk_size_gb": SysDiskSize,
            "data_disk_type": dataDiskType,
            "data_disk_size_gb": dataDiskSize,
            "image_id": imageID,
        }
        postCloudServerPriceOrder(priceOrderArgs).then(resp => {
            this.setState({
                tradePrice: resp.data.TradePrice.toFixed(2)
            })
        })
    }

    // ### 树
    // 优先遍历树，增加节点title和key属性
    bfsAddProps = (treeData) => {
        let queue = [...treeData]
        while (queue.length > 0) {
            let node = queue.shift()
            node.title = node.name
            node.label = node.name
            node.value = node.path_id
            if (node.children !== undefined && node.children !== null) {
                queue.push(...node.children)
            }
        }
    }
    componentDidMount() {
        // 获取运维审批信息
        requestOpsMember({}).then((resp) => {
            this.setState({
                opsAuditEmails: resp.members.map(item => {
                    return { label: item.substring(0, item.lastIndexOf("@")), value: item }
                })
            });
        });
        requestLeaderEmail({}).then(resp => {
            if (resp !== null) {
                this.setState({
                    leaderEmails: resp.email
                })
            }
        })
        postOpsLeader({}).then(resp => {
            if (resp !== null) {
                this.setState({
                    opsLeaderEmail: resp.ops_leader
                })
            }
        })
        // 成本模型树
        postModelTree({ "model_name": "cost", "attach_path_id": true }).then((resp) => {
            let treeData = resp.data
            this.bfsAddProps(treeData)
            this.setState({
                costTreeData: treeData
            })
        })
        // 获取服务器售卖规格配置
        let cloudServerArgs = {
            "supplier": this.props.supplier
        }
        postCloudServerSellConf(cloudServerArgs).then((resp) => {
            let regionIDs = Object.keys(resp.data)
            console.log(resp.data)
            this.setState({
                sellConf: resp.data,
                regions: regionIDs.map(regionID => { return { label: regionID, value: regionID } })
            })
        })
        // 获取服务器镜像
        let cloudImageArgs = {
            "supplier": this.props.supplier
        }
        postCloudImageConf(cloudImageArgs).then((resp) => {
            this.setState({
                images: resp.data.map(image => { return { label: image.Name, value: image.ID } })
            })
        })

    }
    render() {
        return (
            <SideContent>
                <ServerApllyForm>
                    <OrderTitle>{this.props.supplierName}服务器申请</OrderTitle>
                    <OrderCompactRow>
                        <OrderCol>
                            <span>运维审批：</span>
                            <Select
                                options={this.state.opsAuditEmails}
                                placeholder="运维审批人员"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                onChange={this.handleOpsAuditPeopleChange}
                                value={this.state.opsAuditEmailSelected}
                            />
                        </OrderCol>
                        <OrderCenterCol>
                            <span>审批人员：</span>
                            <Tag color="#108ee9" icon={<UserOutlined />}>
                                {this.state.leaderEmails}，{this.state.opsLeaderEmail}
                            </Tag>
                        </OrderCenterCol>
                    </OrderCompactRow>
                    {/* 区域地区 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>地区城市：</span>
                            <Select
                                options={this.state.regions}
                                placeholder="请选择地区"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                onChange={this.handleRegionChange}
                                value={this.state.regionIDSelected}
                            />
                        </OrderCol>
                        <OrderCol>
                            <span>可用区域：</span>
                            <Select
                                options={this.state.zones}
                                placeholder="请选择可用区"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                onChange={this.handleZoneChange}
                                value={this.state.zoneIDSelected}
                            />
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 机型规格 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span><Popover placement="right" content={this.instanceFamilyDesc}>规格机型 <QuestionCircleOutlined /></Popover>：</span>
                            <Select
                                options={this.state.familyTypes}
                                placeholder="请选择服务器规格机型"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                onChange={this.handleFamilyTypeChange}
                                value={this.state.familyTypeSelected}
                            />
                        </OrderCol>
                        <OrderCol>
                            <span>硬件配置：</span>
                            <Select
                                options={this.state.instanceTypes}
                                placeholder="请选择服务器硬件配置"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                onChange={this.handleInstanceTypeChange}
                                value={this.state.instanceTypeIDSelected}
                            />
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 磁盘配置 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span><Popover placement="right" content={this.diskTypeDesc}>系统盘 <QuestionCircleOutlined />：</Popover></span>
                            <Select
                                options={this.state.sysDiskTypes}
                                placeholder="硬盘类型"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                onChange={this.handleSysDiskChange}
                                value={this.state.sysDiskTypeSelected}
                            />
                        </OrderCol>
                        <OrderCol>
                            <span><Popover placement="right" content={this.diskTypeDesc}>数据盘 <QuestionCircleOutlined />：</Popover></span>
                            <Select
                                options={this.state.dataDiskTypes}
                                placeholder="硬盘类型"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                onChange={this.handleDataDiskChange}
                                value={this.state.dataDiskTypeSelected}
                            />
                        </OrderCol>
                        <OrderCenterCol>
                            <span>数据盘规格：</span>
                            <InputNumber
                                min={this.state.minDataDiskSize}
                                max={this.state.maxDataDiskSize}
                                style={{ width: "5vw" }}
                                value={this.state.dataDiskSize}
                                onChange={this.handleDataDiskSizeChange}
                            />
                            <span>&nbsp;GB</span>
                        </OrderCenterCol>
                    </OrderCompactRow>
                    {/* 系统镜像 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>系统镜像：</span>
                            <Select
                                options={this.state.images}
                                placeholder="请选择操作系统镜像"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                onChange={this.handleImageChange}
                                value={this.state.imageIDSelected}
                            />
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 节点挂载 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>业务树相关节点：</span>
                            <TreeSelect
                                placeholder="请选择业务树相关节点"
                                treeData={this.state.costTreeData}
                                onSelect={this.handleCostTreeSelectChange}
                                value={this.state.costNodeSelected}
                            />
                        </OrderCol>
                        {/* <OrderCol>
                            <span>监控挂载：</span>
                            <TreeSelect
                                placeholder="请选择监控挂载节点"
                                treeData={this.state.costTreeData}
                                onChange={this.handleMonitorTreeSelectChange}
                                value={this.state.monitorNodeSelected}
                                disabled
                            />
                        </OrderCol> */}
                    </OrderCompactRow>
                    {/* 服务器名称 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>机器名称：</span>
                            <span>{this.state.instanceNameIndex}</span>
                            <Input
                                placeholder="请填写实例名称"
                                value={this.state.instanceName}
                                onChange={this.handleInstanceNameChange}
                            />
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 申请陈述 */}
                    <OrderRow>
                        <OrderStartCol>
                            <span>申请陈述：</span>
                            <TextArea
                                placeholder="请填写申请理由，不少于5个字符"
                                style={{ height: "7vw" }}
                                value={this.state.applyMsg}
                                onChange={this.handleApplyMsgChange}
                            />
                        </OrderStartCol>
                    </OrderRow>
                    <OrderRow>
                        <OrderEndCol>
                            <FeeSpan>
                                {/* 折扣：{this.state.discount}，折后 */}
                                费用预估：{this.state.tradePrice} 元/年&nbsp;
                                <MoneyCollectOutlined />
                            </FeeSpan>
                        </OrderEndCol>
                    </OrderRow>
                    {/* 费用预估 */}
                    <OrderSubmit>
                        <Button type="primary" onClick={this.handleSubmit}>
                            提交
                        </Button>
                        <Button type="dashed" onClick={this.handleCancel}>
                            取消
                        </Button>
                    </OrderSubmit>
                </ServerApllyForm>
            </SideContent>
        );
    }
}


ServerApplyForm.propTypes = {
    supplier: PropTypes.string.isRequired,
    supplierName: PropTypes.string.isRequired,
    instanceFamilyUrl: PropTypes.string.isRequired,
    diskTypeUrl: PropTypes.string.isRequired,
}



export default withRouter(ServerApplyForm);