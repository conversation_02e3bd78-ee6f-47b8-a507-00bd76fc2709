import { Component } from "react";
import { But<PERSON>, Select, message, TreeSelect, Tag, Input } from "antd";
import styled from "styled-components";
import withRouter from "@/util/withRouter";
import { OrderRow, OrderTitle, ShadeForm, SideContent } from "@/common/styleLayout";
import {
    // postOpsLeader,
    requestLeaderEmail,
    requestOpsMember,
    postModelTree2,
    postTokenApply,
} from "@/request/api";
import { UserOutlined } from "@ant-design/icons";
import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";


// css-js start ↓↓↓
const DelApplyForm = styled(ShadeForm)`
    width: 48%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
`

const DelSpan = styled.span`
    min-width: 80px;
`
export const OrderSubmit = styled.div`
  margin-top: 0.5vw;
  display: flex;
  width: 100%;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: right;
  align-items: center;
`

const DelDiv = styled.div`
    width: 100%;
    display:flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
`
const DelDivRight = styled.div`
    width: 100%;
    display:flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    padding: 0 0 0 1vw;
`

const DelSelect = styled(Select)`
    max-width: 60vw;
`
// css-js end   ↑↑↑

class K8sToken extends Component {
    state = {
        opsAuditEmails: [],
        opsAuditEmailSelected: undefined,
        leaderEmails: undefined,
        // opsLeaderEmail: undefined,
        cluster: "腾讯云北京公共集群(txy-bj-common-01)",
        ns: "",
        tokenType: "Service",
        name: "",
        clusters: [
            { label: "腾讯云北京公共集群(txy-bj-common-01)", value: "腾讯云北京公共集群(txy-bj-common-01)" },
            { label: "腾讯云广州公共集群(txy-gz-common-01)", value: "腾讯云广州公共集群(txy-gz-common-01)" },
            { label: "腾讯云广州会员VIP集群(txy-gz-member-vip-01)", value:"腾讯云广州会员VIP集群(txy-gz-member-vip-01)"},
            { label: "腾讯云北京PC后台集群(pcbj3)", value: "腾讯云北京PC后台集群(pcbj3)" },
            { label: "腾讯云广州好看集群(haokan)", value: "腾讯云广州好看集群(haokan)" },
            { label: "腾讯云硅谷集群(sv1)", value: "腾讯云硅谷集群(sv1)" },
            { label: "阿里云深圳壁纸集群(alisz)", value: "阿里云深圳壁纸集群(alisz)" },
            { label: "阿里云杭州集群(aliyunhz)", value: "阿里云杭州集群(aliyunhz)" },
            { label: "阿里云北京集群(alibj)", value: "阿里云北京集群(alibj)" },
            { label: "阿里云弗吉尼亚集群(alivirginia)", value: "阿里云弗吉尼亚集群(alivirginia)" },
            { label: "金山云北京水银集群(mercury)", value: "金山云北京水银集群(mercury)" },
            { label: "金山云北京云杀集群(cloudkilling)", value: "金山云北京云杀集群(cloudkilling)" },
            { label: "华为云广州豹金集群(hwyun-baojin)", value: "华为云广州豹金集群(hwyun-baojin)" },
            { label: "华为云北京集群(bjhw-common)", value: "华为云北京集群(bjhw-common)" },
            { label: "华为云北京集群(threatintelhw)", value: "华为云北京集群(threatintelhw)" },
            { label: "华为云北京集群(hwdubasecure)", value: "华为云北京集群(hwdubasecure)" },
            { label: "火山云北京公共集群(hsyq-bj-common-01)", value: "火山云北京公共集群(hsyq-bj-common-01)" }],
        tokenTypes: [{ label: "Ingress", value: "Ingress" }, { label: "Service", value: "Service" }, { label: "CronJob", value: "CronJob" }, { label: "Namespace", value: "Namespace" }],
        costTreeData: [],
        nodePath: "",
        showName: true,
    }
    // 运维审批人
    handleOpsAuditPeopleChange = (value) => {
        this.setState({
            opsAuditEmailSelected: value
        })
    }

    // ### 树
    // 优先遍历树，增加节点title和key属性
    bfsAddProps = (treeData) => {
        let queue = [...treeData]
        while (queue.length > 0) {
            let node = queue.shift()
            node.title = node.NodeName
            node.label = node.NodeName
            node.value = node.Layer + "-" + node.ID
            node.children = node.Children
            if (node.children !== undefined && node.children !== null) {
                queue.push(...node.children)
            }
        }
    }

    getPathToLeaf = (data, targetID) => {
        function findPath(node, targetID, currentPath) {
            currentPath.push(node.NodeName);

            if (node.ID === targetID) {
                return currentPath;
            }

            if (node.Children && node.Children.length > 0) {
                for (let child of node.Children) {
                    const result = findPath(child, targetID, [...currentPath]);
                    if (result) {
                        return result;
                    }
                }
            }

            return null;
        }

        for (let item of data) {
            const result = findPath(item, targetID, []);
            if (result) {
                return result;
            }
        }

        return null;
    }

    componentDidMount() {
        requestOpsMember({}).then((resp) => {
            this.setState({
                opsAuditEmails: resp.members.map(item => {
                    return { label: item.substring(0, item.lastIndexOf("@")), value: item }
                })
            });
        });
        requestLeaderEmail({}).then(resp => {
            if (resp !== null) {
                this.setState({
                    leaderEmails: resp.email
                })
            }
        })
        // postOpsLeader({}).then(resp => {
        //     if (resp !== null) {
        //         this.setState({
        //             opsLeaderEmail: resp.ops_leader
        //         })
        //     }
        // })

        // 成本模型树
        postModelTree2({ "model_name": "businessModel" }).then((resp) => {
            let treeData = resp.data
            this.bfsAddProps(treeData)
            this.setState({
                costTreeData: treeData
            })
        })
    }

    handleTokenTypeChange = (e) => {

        this.setState({
            tokenType: e
        })
        this.setState({ showName: e === "Namespace" ? false : true })
    }

    handleNsChange = (e) => {
        this.setState({
            ns: e.target.value
        })
    }
    handleNameChange = (e) => {
        this.setState({
            name: e.target.value
        })
    }
    handleClusterChange = (e) => {
        this.setState({
            cluster: e
        })
    }
    // 成本模型挂载
    handleCostTreeSelectChange = (value, lable, e) => {
        console.log(value, lable, e)
        if (value.split('-')[0] !== "2") {
            message.warn("请选择业务最小单元挂载！")
            this.setState({
                costNodeSelected: ""
            })
        } else {
            this.setState({
                costNodeSelected: value,
            })
        }
    }

    handleSubmit = () => {
        if (this.state.opsAuditEmailSelected === undefined || this.state.opsAuditEmailSelected.length === 0) {
            message.warn("请先选择运维审批人！")
            return
        }
        if (this.state.ns === undefined || this.state.ns.length === 0) {
            message.warn("请填写空间名称！")
            return
        }

        if (this.state.tokenType !== "Namespace" && (this.state.name === undefined || this.state.name.length === 0)) {
            message.warn("请填写名称！")
            return
        }

        if (this.state.costNodeSelected === undefined
            || this.state.costNodeSelected.split('-')[0] !== "2") {
            message.warn("请选择业务最小单元挂载！")
            return
        }

        let id = parseInt(this.state.costNodeSelected.split('-')[1],10);
        const path = this.getPathToLeaf(this.state.costTreeData, id)

        const args = {
            cluster: this.state.cluster,
            token_type: this.state.tokenType,
            ns: this.state.ns,
            name: this.state.name,
            cost_model_node_id: parseInt(this.state.costNodeSelected.split('-')[1]),
            path: path.join('->'),
            ops_audit_email: this.state.opsAuditEmailSelected,
        }

        postTokenApply(args).then((data) => {
            if (data != null && data.result === 0) {
                navigateModal(this.props.navigate, DoingOrderUrlSuffix, 5)
            }
        })
    }

    render() {
        return (
            <>
                <SideContent>
                    <DelApplyForm>
                        <OrderTitle>k8s token申请</OrderTitle>
                        <OrderRow>
                            <DelDiv>
                                <DelSpan>运维审批：</DelSpan>
                                <Select
                                    options={this.state.opsAuditEmails}
                                    placeholder="运维审批人员"
                                    onChange={this.handleOpsAuditPeopleChange}
                                    value={this.state.opsAuditEmailSelected}
                                />
                            </DelDiv>
                            <DelDivRight>
                                <DelSpan>审批人员：</DelSpan>
                                <Tag color="#108ee9" icon={<UserOutlined />}>
                                    {this.state.leaderEmails}
                                </Tag>
                            </DelDivRight>
                        </OrderRow>

                        <OrderRow>
                            <DelSpan>业务挂载：</DelSpan>
                            <TreeSelect
                                placeholder="请选择业务最小单元挂载"
                                treeData={this.state.costTreeData}
                                onSelect={this.handleCostTreeSelectChange}
                                value={this.state.costNodeSelected}
                            />
                        </OrderRow>

                        <OrderRow>
                            <DelDiv>
                                <DelSpan>集群：</DelSpan>
                                <DelSelect
                                    options={this.state.clusters}
                                    value={this.state.cluster}
                                    onChange={this.handleClusterChange}
                                />
                            </DelDiv>
                        </OrderRow>
                        <OrderRow>
                            <DelDiv>
                                <DelSpan>类型：</DelSpan>
                                <DelSelect
                                    options={this.state.tokenTypes}
                                    value={this.state.tokenType}
                                    onChange={this.handleTokenTypeChange}
                                />
                            </DelDiv>
                        </OrderRow>
                        <OrderRow>
                            <DelDiv>
                                <DelSpan>空间名称：</DelSpan>
                                <Input
                                    value={this.state.ns}
                                    onChange={this.handleNsChange}
                                />
                            </DelDiv>
                        </OrderRow>
                        {this.state.showName && <OrderRow>
                            <DelDiv>
                                <DelSpan>名称：</DelSpan>
                                <Input
                                    value={this.state.name}
                                    onChange={this.handleNameChange}
                                />
                            </DelDiv>
                        </OrderRow>
                        }
                        <OrderSubmit>
                            <Button type="primary" onClick={this.handleSubmit}>
                                提交
                            </Button>
                        </OrderSubmit>
                    </DelApplyForm>
                </SideContent>
            </>
        )
    }
}

export default withRouter(K8sToken)