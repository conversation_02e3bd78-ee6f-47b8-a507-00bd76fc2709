import Cookies from "js-cookie";
import { Component } from "react";
// import { Navigate } from "react-router-dom";
import withRouter from "@/util/withRouter";
import { requestAuth } from "@/request/api";
import { Domain } from "@/common/wildcardlDomain";

class Login extends Component {
  constructor(props) {
    super(props);
    const sid = this.props.params.get("sid");
    // var millisecond = new Date().getTime();
    // var expiresTime = new Date(millisecond + 60 * 1000 * 60 * 24 * 1); // 设置token 有效时间
    if (sid !== null) {
      requestAuth({ sid: sid }).then((data) => {
        let expiresTime = new Date(data.expire_sec * 1000)
        var cookieAttributes = { expires: expiresTime, domain: Domain }
        Cookies.set("sid", sid, cookieAttributes);
        Cookies.set("token", data.token, cookieAttributes);
        Cookies.set("user_email", data.user_email, cookieAttributes);
        this.props.navigate("/new-order");
      });
    }
  }
  render() {
    // return <Navigate push to="/" />;
    return "";
  }
}

export default withRouter(Login);
