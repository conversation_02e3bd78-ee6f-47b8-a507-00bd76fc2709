import { Component } from "react";
import { Select, Input, Tooltip, Button, Radio, Upload, message, Modal, Spin, Divider } from "antd";
import { LoadingOutlined, InboxOutlined } from "@ant-design/icons";
import {
  requestDbInstanceInfo,
  requestSqlCheck,
  requestSqlFileCheckResult,
  requestSqlFileCheck,
  requestCommonOrder,
  requestTxCamCosAuth,
  requestAuditDbInstance
} from "@/request/api";
import styled from "styled-components";
import { DoingOrderUrlSuffix } from "@/global"
import navigateModal from "@/util/navigateModal"
import withRouter from "@/util/withRouter";
import getFileContent from "@/util/fileHelper";
import CryptoJS from "crypto-js"
import moment from 'moment';
import { SideContent, ShadeForm, OrderTitle, OrderRow, OrderSubmit } from "@/common/styleLayout";

const { Option } = Select;
const { TextArea } = Input;
const { Dragger } = Upload;
const antIcon = <LoadingOutlined style={{ fontSize: 18 }} spin />;

// css-js start ↓↓↓
const SqlOrderForm = styled(ShadeForm)`
  width: 55%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
`
const SqlSpan = styled.span`
    min-width: 120px;
`
const DBNametip = styled(Tooltip)`
  margin-left: 0.5vw;
`
const SqlOrderSqlContent = styled(OrderRow)`
  flex-direction: column;
  align-items: flex-start;
  flex-wrap: wrap;
  >span{
    width: 100%;
  }

`
const SqlOrderSqlPattern = styled.span`
  margin-bottom: 10px;
`

// css-js end   ↑↑↑

class SqlOrder extends Component {
  state = {
    file_or_not: false,
    order_type: ["sql_audit_execute", "sql_audit_file_execute","sql_audit_resource_execute","sql_audit_resource_file_execute"],
    db_instance_infos: [], // 数据库实例信息
    db_host: undefined,
    db_name: "库 名",
    apply_msg: undefined,
    product_people_email : undefined,
    audit_db: [],
    // sql语句
    sql: undefined,
    sql_check_md5: undefined,
    sql_check_result: false,
    // sql文件
    file_infos: [], // 附件信息，包括文件名、cos路径
    file_sql_check_md5: undefined,
    file_check_result: 0, // 0 代表进行中，1 代表成功 2 代表成功但有警告 3 代表不通过 4 代表后台出错
    file_check_result_msg: "",
    file_task_id: undefined,
    file_check_timer: undefined,
    showCheckLoading: false,
  };
  componentDidMount() {
    requestDbInstanceInfo({}).then((data) => {
      this.setState({
        db_instance_infos: data.db_instances
      })
    })

    requestAuditDbInstance({}).then((data1)=>{
      this.setState({
          audit_db: data1.db_instances
      })
    })
  }

  componentWillUnmount() {
    clearInterval(this.state.file_check_timer);
  }

  handleSqlMode = (e) => {
    this.setState({
      file_or_not: e.target.value,
    });
  };

  handleDbHostChange = (index) => {
    this.setState({
      db_host: this.state.db_instance_infos[index]["db_host"],
      db_name: this.state.db_instance_infos[index]["db_name"]
    })
  }

  HandleProductPeopleEamil = (e) => {
    this.setState({
      product_people_email: e.target.value
    })
  }

  HandleApplyMsg = (e) => {
    this.setState({
      apply_msg: e.target.value
    })
  }

  HandleSqlChange = (e) => {
    this.setState({
      sql: e.target.value,
      sql_check_result: false,
      sql_check_md5: undefined,
    })
  }
  
  checkSQL = () => {
    if (this.state.db_host === undefined) {
      message.error("请选择数据库实例IP", 1);
      return
    }

    // 检查 db_host 是否在 audit_db 数组中
    for (let i = 0; i < this.state.audit_db.length; i++) {
      if (this.state.db_host === this.state.audit_db[i]) {
        if (this.state.product_people_email === undefined || this.state.product_people_email === ""){
          message.error("所选数据库实例IP在审计数据库列表中，请填写产品负责人邮箱", 1);
          return;
        }
      }
    }

    if (this.state.file_or_not) {
      this.checkSQLFile()
    } else {
      this.checkSQLText()
    }
  }

  checkSQLFile = () => {
    if (this.state.file_infos.length === 0) {
      message.error("请先上传sql文件", 1);
      return
    }
    let handle = () => {
      let args = {
        db_host: this.state.db_host,
        db_name: this.state.db_name,
        file_infos: this.state.file_infos,
        product_people_email: this.state.product_people_email
      }
      requestSqlFileCheck(args).then((data) => {

        this.setState({
          file_task_id: data.task_id
        }, this.checkSQLFileResult)
      })

    }
    this.setState({
      showCheckLoading: true
    }, handle)
  }

  checkSQLFileResult = () => {
    let args = {
      task_id: this.state.file_task_id
    }
    // 请求处理函数
    let resultHandle = () => {
      // 清除轮询计时器
      clearInterval(this.state.file_check_timer)
      // 处理结果
      if (this.state.file_check_result === 1) {
        Modal.success({
          title: 'SQL附件校验通过！',
        })
      } else if (this.state.file_check_result === 2) {
        Modal.warn({
          title: 'SQL校验通过，但有警告信息，请谨慎执行：',
          content: (<>{this.state.file_check_result_msg}</>),
          width: 600,
        });
      } else {
        Modal.error({
          title: 'SQL校验失败，错误信息为：',
          content: (<>{this.state.file_check_result_msg}</>),
          width: 600,
        });
      }
      this.setState({
        showCheckLoading: false
      })
    }
    // 请求sql附件检查结果
    let requestHandle = () => {
      if (this.state.file_check_timer !== undefined) {
        requestSqlFileCheckResult(args).then(
          (data) => {
            let resultMsg = ""
            let finishOrNot = true
            let maxErrCode = 0
            for (let item of data.sqlFileCheckTaskInfos) {

              if (item.result > 1) {
                resultMsg += item.result_msg
                this.setState({
                  file_check_result_msg: resultMsg
                })
              }
              if (item.result > maxErrCode) {
                maxErrCode = item.result
              }
              // 有文件报错则终止轮询
              if (item.result > 2) {
                this.setState({ file_check_result: maxErrCode }, resultHandle)
                clearInterval(this.state.file_check_timer)
                return
              }
              // 还有一个文件没检查完则继续轮询
              if (item.result === 0) {
                finishOrNot = false
              }
            }
            if (finishOrNot) {
              this.setState({ file_check_result: maxErrCode }, resultHandle)
              clearInterval(this.state.file_check_timer)
              return
            }

          }
        )
      }
    }
    // 开启轮询
    this.setState({
      file_check_timer: setInterval(requestHandle, 3000)
    })
  }

  localCheckSqlText(sqlText) {
    let checkResult = true
    let checkMsg = ""
    let totalLength = sqlText.length
    if (totalLength > 65535) {
      checkResult = false
      checkMsg = `Sql文本字符数：${totalLength}，超过65,535，检查不通过 ！`
      message.error(checkMsg, 3);
    }

    // 按语句
    let lines = sqlText.split(";");
    // 检查语句数量是否超过3000
    let lineNum = lines.length
    if (lineNum > 3000) {
      checkResult = false
      checkMsg = `\nSql语句数量：${lineNum}，超过3000，检查不通过 ！`
      message.error(checkMsg, 3);
    }
    // 检查每行长度是否超过3000
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].length > 3000) {
        checkResult = false
        checkMsg = `单个Sql语句字符数超过3000，检查不通过 ！`
        message.error(checkMsg, 3);
        break
      }
    }
    if (!checkResult) {
      checkMsg = "请修改Sql文本或改用附件形式上传。"
      message.error(checkMsg, 3);
    }
    return checkResult
  }

  checkSQLText = () => {
    if (this.state.sql === undefined) {
      message.error("sql语句不能为空", 1);
      return
    }
    // 本地检查sql文本总长度，行数，每行字符数
    let localCheckResult = this.localCheckSqlText(this.state.sql)
    if (!localCheckResult) {
      return
    }
    let handle = () => {
      let args = {
        db_host: this.state.db_host,
        db_name: this.state.db_name,
        sql: this.state.sql,
        product_people_email: this.state.product_people_email
      }
      requestSqlCheck(args).then((data) => {
        this.setState({
          showCheckLoading: false
        })
        if (data.sql_err_level === 0) {
          this.setState({
            check_md5: data.check_md5,
            sql_check_result: true
          })
          Modal.success({
            title: 'SQL校验通过！',
          })
        } else if (data.sql_err_level === 1) {
          this.setState({
            check_md5: data.check_md5,
            sql_check_result: true
          })
          Modal.warn({
            title: 'SQL校验通过，但存在警告信息：',
            content: (<>{data.sql_err_msg}</>),
            width: 600,
          })
        } else {
          Modal.error({
            title: 'SQL校验失败，错误信息为：',
            content: (<>{data.sql_err_msg}</>),
            width: 600,
          });
        }
      })

    }
    this.setState({
      showCheckLoading: true
    }, handle)
  }

  handleSubmit = () => {
    let order_type
    let apply_info
    if (this.state.file_or_not) {
      if (!this.state.file_check_result) {
        message.error("请先进行SQL检查", 1.5);
        return
      }
      order_type = this.state.order_type[1]
      apply_info = {
        db_host: this.state.db_host,
        db_name: this.state.db_name,
        file_infos: this.state.file_infos
      }
      if (this.state.product_people_email !== undefined && this.state.product_people_email !==""){
        order_type = this.state.order_type[3]
        apply_info.product_people_email = this.state.product_people_email
      }
    } else {
      if (!this.state.sql_check_result) {
        message.error("请先进行SQL检查", 1.5);
        return
      }
      order_type = this.state.order_type[0]
      apply_info = {
        db_host: this.state.db_host,
        sql: this.state.sql,
        check_md5: this.state.check_md5
      }
      if (this.state.product_people_email !== undefined && this.state.product_people_email !==""){
        order_type = this.state.order_type[2]
        apply_info.product_people_email = this.state.product_people_email
      }
    }
    const args = {
      apply_info: JSON.stringify(apply_info),
      order_type: order_type,
      apply_msg: this.state.apply_msg,
      product_people_email: this.state.product_people_email,
    };
    // 检测非法字符
    var hasSpecialCharacters = /['"\t]/.test(args.apply_msg );
		if (hasSpecialCharacters) {
			message.warn(`申请文本存在非法字符（"'\t），已自动去除，请知悉。`, 5);
			args.apply_msg  = args.apply_msg.replace(/['"\t]/g, '');
		}

    args.apply_msg = args.apply_msg + `\n目标数据库：${this.state.db_host} ${this.state.db_name}`

    requestCommonOrder(args).then((data) => {
      if (data !== null) {
        navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
      }
    });
  }

  handleFileUpload = (options) => {
    getFileContent(options.file).then((result) => {
      var wordArray = CryptoJS.lib.WordArray.create(result);
      var md5 = CryptoJS.MD5(wordArray).toString();
      // 获取日期
      let dateDir = moment().format('YYYY-MM-DD');
      let filePath = `sqlAttachment/${dateDir}/${md5}`
      // 获取腾讯云 cos 临时票据
      requestTxCamCosAuth({}).then((data) => {
        var COS = require('cos-js-sdk-v5');
        var cos = new COS({
          SecurityToken: data.TmpToken,
          SecretId: data.TmpSecretId,
          SecretKey: data.TmpSecretKey
        });
        cos.putObject({
          Bucket: 'ops-1252921383',   /* 必须 */
          Region: 'ap-guangzhou',     /* 存储桶所在地域，必须字段 */
          Key: filePath,              /* 必须 */
          StorageClass: 'STANDARD',
          Body: options.file, // 上传文件对象
          onProgress: function (progressData) {
            options.onProgress({ percent: progressData.percent * 100 })
          }
        }, (err, data) => {
          if (err === null) {
            this.setState({
              file_infos: [...this.state.file_infos, { "path": filePath, "file_name": options.file.name }]
            })
            options.onSuccess(data)
          } else {
            options.onError(err)
          }
        });
      })
    })
  }
  handleRemoveFile = (file) => {

  }
  handleBeforeUpload = () => {
  }

  render() {
    return (
      <SideContent>
        <SqlOrderForm>
          <OrderTitle>SQL审计执行工单</OrderTitle>
          <OrderRow>
            <SqlSpan>数据库内网IP：</SqlSpan>
            <Select
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              filterSort={(optionA, optionB) =>
                optionA.children.toLowerCase().localeCompare(optionB.children.toLowerCase())
              }
              onChange={this.handleDbHostChange}
            >
              {
                this.state.db_instance_infos.map((item, index) => {
                  return (
                    <Option value={index} key={index}>
                      {item["db_host"] + " / " + item["db_name"]}
                    </Option>)
                })
              }
            </Select>
            <DBNametip title="库 名">
              <Button className="sql-order-form-row-ip-button" type="dashed">
                {this.state.db_name}
              </Button>
            </DBNametip>
          </OrderRow>
          <OrderRow style={{ alignItems: "flex-start" }}>
            <SqlSpan>产品负责人：</SqlSpan>
            <Input placeholder="审计数据库必须填写产品审批人如：<EMAIL>，非审计数据库可不填" value={this.state.product_people_email} onChange={this.HandleProductPeopleEamil} />
          </OrderRow>
          <OrderRow style={{ alignItems: "flex-start" }}>
            <SqlSpan>申请描述：</SqlSpan>
            <TextArea rows={2} placeholder="请输入申请理由" value={this.state.apply_msg} onChange={this.HandleApplyMsg} />
          </OrderRow>
          
          <SqlOrderSqlContent>
            <SqlOrderSqlPattern>
              <Radio.Group
                buttonStyle="solid"
                value={this.state.file_or_not}
                onChange={this.handleSqlMode}
              >
                <Radio.Button value={false}>文本</Radio.Button>
                <Radio.Button value={true}>附件</Radio.Button>
              </Radio.Group>
            </SqlOrderSqlPattern>
            <Dragger
              className={this.state.file_or_not ? "" : "hide"}
              customRequest={this.handleFileUpload}
              accept=".sql"
              onRemove={this.handleRemoveFile}
              beforeUpload={this.handleBeforeUpload}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">单击或拖动文件到此区域上传</p>
              <p className="ant-upload-hint">
                单机或拖动文件上传，勿传非sql文件
              </p>
            </Dragger >
            <TextArea
              className={this.state.file_or_not ? "hide" : ""}
              style={{ minHeight: "16vw" }}
              placeholder="请输入正确的sql语句后，点击检查按钮，检查无误后点击提交按钮"
              value={this.state.sql}
              onChange={this.HandleSqlChange}
            />

          </SqlOrderSqlContent>
          <OrderSubmit>
            <Button type="primary" onClick={this.handleSubmit}>
              提交
            </Button>
            <Button type="dashed" onClick={this.checkSQL}>
              SQL检查 &nbsp;<Spin size="small" indicator={antIcon} className={this.state.showCheckLoading ? "" : "hide"} />
            </Button>
          </OrderSubmit>
          <Divider dashed />
          <Tooltip title="标注">
            <span style={{color: 'red'}}>审计数据库列表：{this.state.audit_db.join('，')}</span>
          </Tooltip>
        </SqlOrderForm >
      </SideContent >
    );
  }
}
export default withRouter(SqlOrder)