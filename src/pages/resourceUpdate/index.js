import { Component } from "react";
import { Select, Button, Input, Tag,message } from "antd";
import styled from "styled-components";
import { UserOutlined } from "@ant-design/icons";
import {
	OrderRow,
	OrderTitle,
	SideContent,
	OrderSubmit,
	OrderCol,
	OrderCenterCol,
	OrderStartCol,
	OrderCompactRow,
	ShadeForm
} from "@/common/styleLayout";
import {
	requestUserLeaderEmail,
	requestOpsMember,
	requestUpdateCMDBServiceOwner,
} from "@/request/api";
import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";


const { TextArea } = Input;

export const DomainApllyForm = styled(ShadeForm)`
  width: 62%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
`

const TITLE = "资源负责人变更"
export default class ServiceOwnerUpdate extends Component {
	state = {
		// 服务器名称
		oldServiceOwnerEmail: undefined,
		newServiceOwnerEmail:undefined,
		ids :undefined,
		applyMsg: "",

		// 审批信息
		opsAuditEmails: [],
		opsAuditEmailSelected: undefined,
		newServiceOwnerLeaderEmail: undefined,
		oldServiceOwnerLeaderEmail: undefined,

	}

	handleIDsChange = (e) => {
        let ids = e.target.value
        let idArray = ids.split("\n")
        this.setState({
            ids: idArray
        })
    }
	handleOldServiceOwnerEmailChange = (e) => {
		this.setState({
			oldServiceOwnerEmail: e.target.value
		})
	}
	handleNewServiceOwnerEmailChange = (e) => {
		this.setState({
			newServiceOwnerEmail: e.target.value
		})
	}
	// 运维审批人
	handleOpsAuditPeopleChange = (value) => {
		this.setState({
			opsAuditEmailSelected: value
		})
	}
	// 申请理由
	handleApplyMsgChange = (e) => {
		this.setState({
			applyMsg: e.target.value
		})
	}
	// 提交修改服务器负责人工单
	handleSubmit = () => {
		if (this.state.opsAuditEmailSelected === "" || this.state.opsAuditEmailSelected === undefined) {
            message.error("请选择运维审批人！")
            return
        }

		if (this.state.oldServiceOwnerEmail === "" || this.state.oldServiceOwnerEmail === undefined) {
            message.error("请输入交接人邮箱！")
            return
        }

		if (this.state.newServiceOwnerEmail === "" || this.state.newServiceOwnerEmail === undefined) {
            message.error("请输入接收人邮箱！")
            return
        }

		if (this.state.applyMsg.length <= 5) {
            message.error("申请理由不能少于5个字符！")
            return
        }

		let orderArg = {
			"old_service_owner":this.state.oldServiceOwnerEmail,
			"new_service_owner":this.state.newServiceOwnerEmail,
			"old_service_owner_leader_audit_email":this.state.oldServiceOwnerLeaderEmail,
			"new_service_owner_leader_audit_email":this.state.newServiceOwnerLeaderEmail,
			"ops_audit_email":this.state.opsAuditEmailSelected,
			"apply_msg": this.state.applyMsg,
			"ids":this.state.ids,
		}
		requestUpdateCMDBServiceOwner(orderArg).then((resp) => {
			if (resp !== null) {
				navigateModal(this.props.navigate, DoingOrderUrlSuffix, 5)
			}
		})
	}
	
	handleCancel = () => {
		this.props.navigate("/new-order");
	};

	getNewServiceOwnerLeaderEmail = () => {
		let arg = {
			"user_email":this.state.newServiceOwnerEmail
		}

		if (this.state.newServiceOwnerEmail === undefined || this.state.newServiceOwnerEmail === ""){
			return
		}

		requestUserLeaderEmail(arg).then(resp => {
			if (resp !== null) {
				this.setState({
					newServiceOwnerLeaderEmail: resp.email
				})
			}
		})
	}

	getOldServiceOwnerLeaderEmail = () => {
		let arg = {
			"user_email":this.state.oldServiceOwnerEmail
		}
		if (this.state.oldServiceOwnerEmail === undefined || this.state.oldServiceOwnerEmail === ""){
			return
		}

		requestUserLeaderEmail(arg).then(resp => {
			if (resp !== null) {
				this.setState({
					oldServiceOwnerLeaderEmail: resp.email
				})
			}
		})
	}

	componentDidMount() {
		// 获取运维审批信息
		requestOpsMember({}).then((resp) => {
			this.setState({
				opsAuditEmails: resp.members.map(item => {
                    return { label: item.substring(0, item.lastIndexOf("@")), value: item }
                })
			});
		});
	}
	render() {
		return (
			<SideContent>
				<DomainApllyForm>
					<OrderTitle>{TITLE}</OrderTitle>
					<OrderCompactRow>
						<OrderCol>
							<span>运维审批：</span>
							<Select
								options={this.state.opsAuditEmails}
								placeholder="运维审批人员"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleOpsAuditPeopleChange}
								value={this.state.opsAuditEmailSelected}
							/>
						</OrderCol>
						<OrderCenterCol>
							<span>审批人员：</span>
							<Tag color="#108ee9" icon={<UserOutlined />}>
								{this.state.oldServiceOwnerLeaderEmail}，{this.state.newServiceOwnerLeaderEmail}
							</Tag>
						</OrderCenterCol>
					</OrderCompactRow>
					<OrderCompactRow>
						<OrderCol>
							<span>交接人&nbsp;&nbsp;&nbsp;：</span>
							<Input
								placeholder="请填写资源负责人邮箱"
								value={this.state.oldServiceOwnerEmail}
								onBlur={this.getOldServiceOwnerLeaderEmail}
								onChange={this.handleOldServiceOwnerEmailChange}
							/>
						</OrderCol>
						<OrderCol>
							<span>接收人&nbsp;&nbsp;&nbsp;：</span>
							<Input
								placeholder="请填写资源接收人邮箱"
								value={this.state.newServiceOwnerEmail}
								onBlur={this.getNewServiceOwnerLeaderEmail}
								onChange={this.handleNewServiceOwnerEmailChange}
							/>
						</OrderCol>
					</OrderCompactRow>
					<OrderRow>
						<OrderStartCol>
							<span>资源ID&nbsp;&nbsp;&nbsp;：</span>
							<TextArea
								placeholder="请填写资源ID，多个资源id请使用回车换行分割，如果为空则变更所有资源"
								style={{ height: "7vw" }}
								value={this.state.ids}
								onChange={this.handleIDsChange}
							/>
						</OrderStartCol>
					</OrderRow>
					<OrderRow>
						<OrderStartCol>
							<span>申请理由：</span>
							<TextArea
								placeholder="请填写申请理由，不少于5个字符"
								style={{ height: "7vw" }}
								value={this.state.applyMsg}
								onChange={this.handleApplyMsgChange}
							/>
						</OrderStartCol>
					</OrderRow>
					<OrderSubmit>
						<Button type="primary" onClick={this.handleSubmit}>
							提交
						</Button>
						<Button type="dashed" onClick={this.handleCancel}>
							取消
						</Button>
					</OrderSubmit>
				</DomainApllyForm>
			</SideContent>
		);
	}
}
