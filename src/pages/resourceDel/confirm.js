import Cookies from "js-cookie";
import { Component } from "react";
import { postResourceDeleteConfirm } from "@/request/api";
import { requestAuth } from "@/request/api";
import { Domain } from "@/common/wildcardlDomain";

const searchParams = new URLSearchParams(window.location.search);
export default class Confirm extends Component {
    state = {
        textCnt: "正在请求接口确认销毁......"
    }
    componentDidMount(props) {
        const sid = searchParams.get("sid");
        var millisecond = new Date().getTime();
        var expiresTime = new Date(millisecond + 60 * 1000 * 60 * 24 * 1); // 设置token 有效时间
        //oa登录后的跳转，传入sid
        if (sid !== null) {
            requestAuth({ sid: sid }).then((data) => {
                var cookieAttributes = { expires: expiresTime, domain: Domain }
                Cookies.set("sid", sid, cookieAttributes);
                Cookies.set("token", data.token, cookieAttributes);
                Cookies.set("user_email", data.user_email, cookieAttributes);

                this.confirm()
            });
        } else {
            const token = Cookies.get("token");
            if (typeof token == "undefined") {
                //无登录，重定向到oa登录，登录后forwar本页面
                window.location.replace(
                    process.env.REACT_APP_OA_HOST +
                    "/r/w?cmd=API_com.cm.cheetah.httpapi.login&forward=" +
                    process.env.REACT_APP_HOST +
                    "/resdel/confirm" + window.location.search
                );
            } else {
                // 原先已经登录
                this.confirm()
            }
        }
    }

    confirm() {
        postResourceDeleteConfirm({
            id: searchParams.get("i"),
            restype: searchParams.get("t"),
            supplier: searchParams.get("s"),
            region: searchParams.get("g"),
        }).then((data) => {
            let txt = "已确认销毁"
            if (data == null) {
                txt = "无权限操作"
            }
            this.setState({
                textCnt: txt
            })
        })
    }

    render() {
        return (
            <div style={{ padding: "30px 100px" }} align="left" ><span >{searchParams.get("i")} {this.state.textCnt}</span></div>
        );
    }
}