import { Component } from "react";
import { <PERSON><PERSON>, <PERSON>, message, Modal, Tag } from "antd";
import styled from "styled-components";
import withRouter from "@/util/withRouter";
import { OrderRow, OrderTitle, ShadeForm, SideContent } from "@/common/styleLayout";
import {
    postResourceCommon,
    postResourceDelete,
    postOpsLeader,
    requestLeaderEmail,
    requestOpsMember,
} from "@/request/api";
import { UserOutlined } from "@ant-design/icons";

import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";

// css-js start ↓↓↓
const DelApplyForm = styled(ShadeForm)`
    width: 48%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
`
const RedSpan = styled.span`
color: red;
`
const DelSpan = styled.span`
    min-width: 80px;
`
export const OrderSubmit = styled.div`
  margin-top: 0.5vw;
  display: flex;
  width: 100%;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: right;
  align-items: center;
`

const DelDiv = styled.div`
    width: 100%;
    display:flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
`
const DelDivRight = styled.div`
    width: 100%;
    display:flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    padding: 0 0 0 1vw;
`

const DelSelect = styled(Select)`
    max-width: 60vw;
`
// css-js end   ↑↑↑

const searchParams = new URLSearchParams(window.location.search);
class ResourceDel extends Component {
    state = {
        opsAuditEmails: [],
        opsAuditEmailSelected: undefined,
        leaderEmails: undefined,
        opsLeaderEmail: undefined,
        supplier: 1,
        region: "",
        resourceType: "server",
        resourceName: "",
        resourceId: "",
        suppliers: [{ label: "腾讯云", value: 1 }, { label: "阿里云", value: 3 }, { label: "金山云", value: 2 }, { label: "华为云", value: 4 }], //, { label: "华为云·聚云", value: 4 } { label: "华为云·豹趣", value: 6 },
        regions: [],
        resourceTypes: [{ label: "服务器", value: "server" }, { label: "数据库", value: "mysql" }, { label: "Redis", value: "redis" }],
        resourceNames: [],
        regionToRes: [],
        idToName: [],
        isModalOpen: false,
    }
    // 运维审批人
    handleOpsAuditPeopleChange = (value) => {
        this.setState({
            opsAuditEmailSelected: value
        })
    }
    getRegionResouce(data, fromUrlParam = false) {
        const resourceNames = data.map((res) => {
            return {
                label: res[1] + " (" + res[0] + ")",
                value: res[0]
            }
        })
        let resourceName = fromUrlParam ? searchParams.get("id") : resourceNames[0].value
        return { resourceName: resourceName, resourceNames: resourceNames }
    }
    setData(supplier, resourceType, fromUrlParam = false) {
        postResourceCommon({ "supplier": supplier, "resource_type": resourceType }).then((data) => {
            if (data.regions.length === 0) {
                message.warn(supplier + "下暂无" + resourceType + "资源，请重新选择", 3)
                return
            }
            let region = fromUrlParam ? searchParams.get("region") : data.regions[0]
            const regions = data.regions.map((region) => {
                return {
                    label: region,
                    value: region,
                }
            })
            const { resourceName, resourceNames } = this.getRegionResouce(data.region_to_res[region], fromUrlParam)
            this.setState({
                regions: regions,
                resourceNames: resourceNames,
                region: region,
                resourceName: resourceName,
                resourceId: resourceName,
                idToName: data.id_to_name,
                regionToRes: data.region_to_res,
                supplier: supplier,
                resourceType: resourceType,
            });
        })
    }
    initData() {
        let fromUrlParam = false, supplier = 1, resourceType = ""
        let supplierStr = searchParams.get('supplier')
        resourceType = searchParams.get('restype');
        if (resourceType === null || resourceType === "" || supplierStr === null || supplierStr === "") {
            supplier = 1
            resourceType = 'server'
        } else {
            supplier = parseInt(supplierStr, 10);
            fromUrlParam = true
        }
        console.log(`supplier: ${supplier}, resourceType: ${resourceType}`);
        return [supplier, resourceType, fromUrlParam]
    }

    componentDidMount() {
        const [supplier, resourceType, fromUrlParam] = this.initData()
        this.setData(supplier, resourceType, fromUrlParam)
        requestOpsMember({}).then((resp) => {
            this.setState({
                opsAuditEmails: resp.members.map(item => {
                    return { label: item.substring(0, item.lastIndexOf("@")), value: item }
                })
            });
        });
        requestLeaderEmail({}).then(resp => {
            if (resp !== null) {
                this.setState({
                    leaderEmails: resp.email
                })
            }
        })
        postOpsLeader({}).then(resp => {
            if (resp !== null) {
                this.setState({
                    opsLeaderEmail: resp.ops_leader
                })
            }
        })
    }
    handleSupplierChange = (e) => {
        this.setState({
            supplier: e
        })
        this.setData(e, this.state.resourceType)
    }

    handleRegionChange = (e) => {
        const { resourceName, resourceNames } = this.getRegionResouce(this.state.regionToRes[e])
        this.setState({
            region: e,
            resourceName: resourceName,
            resourceNames: resourceNames,
            resourceId: resourceName,
        })
    }
    handleResourceTypeChange = (e) => {
        this.setState({
            resourceType: e,
        })
        this.setData(this.state.supplier, e)
    }

    handleResourceNameChange = (e) => {
        this.setState({
            resourceName: e,
            resourceId: e,
        })
    }
    onResourceNameSearch = (e) => {
        // console.log("onResourceNameSearch", e)
    }
    filterResourceNameOption = (input, option) =>
        (option?.label ?? '').toLowerCase().includes(input.toLowerCase());

    handleSubmit = () => {
        if (this.state.opsAuditEmailSelected === undefined || this.state.opsAuditEmailSelected.length === 0) {
            message.warn("请先选择运维审批人！")
            return
        }
        const args = {
            supplier: this.state.supplier,
            region: this.state.region,
            resource_type: this.state.resourceType,
            resource_id: this.state.resourceId,
            resource_name: this.state.idToName[this.state.resourceId],
            ops_audit_email: this.state.opsAuditEmailSelected,
        }
        console.log("args", args)
        postResourceDelete(args).then((data) => {
            console.log("postResourceDelete resp：", data)
            if (data != null && data.result === 0) {
                // message.success("订单已成功提交。")
                navigateModal(this.props.navigate, DoingOrderUrlSuffix, 5)
            }
        })
        // this.setState({ isModalOpen: true })
    }
    handleOk = () => {
        this.setState({ isModalOpen: false })
        const args = {
            supplier: this.state.supplier,
            region: this.state.region,
            resourceType: this.state.resourceType,
            resourceId: this.state.resourceId
        }
        console.log("args", args)
        postResourceDelete(args).then((data) => {
            if (data == null) {
                message.success("订单已成功提交。")
            }
        })
    }
    handleCancel = () => {
        this.setState({ isModalOpen: false })
    }

    render() {
        return (
            <>
                <Modal title="确认销毁"
                    open={this.state.isModalOpen}
                    onOk={this.handleOk}
                    onCancel={this.handleCancel}
                    okText="确认"
                    cancelText="取消">
                    <p>厂商：{this.state.supplier}</p>
                    <p>地区：{this.state.region}</p>
                    <p>资源名称：{this.state.resourceName}</p>
                    <p>资源ID：{this.state.resourceId}</p>
                </Modal >
                <SideContent>
                    <DelApplyForm>
                        <OrderTitle>云资源销毁</OrderTitle>
                        <OrderRow>
                            <DelDiv>
                                <DelSpan>运维审批：</DelSpan>
                                <Select
                                    options={this.state.opsAuditEmails}
                                    placeholder="运维审批人员"
                                    onChange={this.handleOpsAuditPeopleChange}
                                    value={this.state.opsAuditEmailSelected}
                                />
                            </DelDiv>
                            <DelDivRight>
                                <DelSpan>审批人员：</DelSpan>
                                <Tag color="#108ee9" icon={<UserOutlined />}>
                                    {this.state.leaderEmails}，{this.state.opsLeaderEmail}
                                </Tag>
                            </DelDivRight>
                        </OrderRow>
                        <OrderRow>
                            <DelDiv>
                                <DelSpan>厂商：</DelSpan>
                                <DelSelect
                                    options={this.state.suppliers}
                                    value={this.state.supplier}
                                    onChange={this.handleSupplierChange}
                                />
                            </DelDiv>
                        </OrderRow>
                        <OrderRow>
                            <DelDiv>
                                <DelSpan>资源类型：</DelSpan>
                                <DelSelect
                                    options={this.state.resourceTypes}
                                    value={this.state.resourceType}
                                    onChange={this.handleResourceTypeChange}
                                />
                            </DelDiv>
                        </OrderRow>
                        <OrderRow>
                            <DelDiv>
                                <DelSpan>地区：</DelSpan>
                                <DelSelect
                                    options={this.state.regions}
                                    value={this.state.region}
                                    onChange={this.handleRegionChange}
                                />
                            </DelDiv>
                        </OrderRow>
                        <OrderRow>
                            <DelDiv>
                                <DelSpan>资源名称：</DelSpan>
                                <DelSelect
                                    showSearch
                                    onSearch={this.onResourceNameSearch}
                                    filterOption={this.filterResourceNameOption}
                                    options={this.state.resourceNames}
                                    value={this.state.resourceName}
                                    onChange={this.handleResourceNameChange}
                                />
                            </DelDiv>
                        </OrderRow>
                        <OrderRow>
                            <DelDiv>
                                <DelSpan>资源ID：</DelSpan>
                                <RedSpan>{this.state.resourceId}</RedSpan>
                            </DelDiv>
                        </OrderRow>
                        <OrderSubmit>
                            <Button type="primary" onClick={this.handleSubmit}>
                                提交
                            </Button>
                        </OrderSubmit>
                    </DelApplyForm>
                </SideContent>
            </>
        )
    }
}

export default withRouter(ResourceDel)