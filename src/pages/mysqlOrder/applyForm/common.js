import { Component } from "react";
import { Select, Button, Input, message, Tag, Slider, InputNumber, TreeSelect } from "antd";
import styled from "styled-components";
import { MoneyCollectOutlined, UserOutlined } from "@ant-design/icons";
import withRouter from "@/util/withRouter";
import { MysqlApllyForm } from "../style"
import {
    OrderCompactRow,
    OrderTitle,
    SideContent,
    OrderSubmit,
    OrderCol,
    OrderCenterCol,
    OrderStartCol,
    OrderEndCol,
    OrderTextRow
} from "@/common/styleLayout";
import {
    postModelTree,
    postOpsLeader,
    requestLeaderEmail,
    requestOpsMember,
    postCloudMysqlSellConf,
    postCloudMysqlPriceOrder,
    postBuyCloudMysql
} from "@/request/api";
import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";
import PropTypes from 'prop-types';


const { TextArea } = Input;

// css-js
export const FeeSpan = styled.span`
  color: red;
  font-size: large;
`

class MysqlApplyForm extends Component {
    // 字符集
    characterSets = [
        { label: "LATIN1", value: "LATIN1" },
        { label: "UTF8", value: "UTF8" },
        { label: "GBK", value: "GBK" },
        { label: "UTF8MB4", value: "UTF8MB4" }
    ]
    // 排序规则
    collationMap = {
        "LATIN1":
            [
                { label: "LATIN1_SWEDISH_CI", value: "LATIN1_SWEDISH_CI" },
                { label: "LATIN1_BIN", value: "LATIN1_BIN" },
                { label: "LATIN1_DANISH_CI", value: "LATIN1_DANISH_CI" },
                { label: "LATIN1_GENERAL_CI", value: "LATIN1_GENERAL_CI" },
                { label: "LATIN1_GENERAL_CS", value: "LATIN1_GENERAL_CS" },
                { label: "LATIN1_GERMAN1_CI", value: "LATIN1_GERMAN1_CI" },
                { label: "LATIN1_GERMAN2_CI", value: "LATIN1_GERMAN2_CI" },
                { label: "LATIN1_SPANISH_CI", value: "LATIN1_SPANISH_CI" }
            ],
        "GBK":
            [
                { label: "GBK_CHINESE_CI", value: "GBK_CHINESE_CI" },
                { label: "GBK_BIN", value: "GBK_BIN" }
            ],
        "UTF8":
            [
                { label: "UTF8_GENERAL_CI", value: "UTF8_GENERAL_CI" },
                { label: "UTF8_BIN", value: "UTF8_BIN" },
                { label: "UTF8_CROATIAN_CI", value: "UTF8_CROATIAN_CI" },
                { label: "UTF8_CZECH_CI", value: "UTF8_CZECH_CI" },
                { label: "UTF8_DANISH_CI", value: "UTF8_DANISH_CI" },
                { label: "UTF8_ESPERANTO_CI", value: "UTF8_ESPERANTO_CI" },
                { label: "UTF8_ESTONIAN_CI", value: "UTF8_ESTONIAN_CI" },
                { label: "UTF8_GENERAL_MYSQL500_CI", value: "UTF8_GENERAL_MYSQL500_CI" },
                { label: "UTF8_GERMAN2_CI", value: "UTF8_GERMAN2_CI" },
                { label: "UTF8_HUNGARIAN_CI", value: "UTF8_HUNGARIAN_CI" },
                { label: "UTF8_ICELANDIC_CI", value: "UTF8_ICELANDIC_CI" },
                { label: "UTF8_LATVIAN_CI", value: "UTF8_LATVIAN_CI" },
                { label: "UTF8_LITHUANIAN_CI", value: "UTF8_LITHUANIAN_CI" },
                { label: "UTF8_PERSIAN_CI", value: "UTF8_PERSIAN_CI" },
                { label: "UTF8_POLISH_CI", value: "UTF8_POLISH_CI" },
                { label: "UTF8_ROMAN_CI", value: "UTF8_ROMAN_CI" },
                { label: "UTF8_ROMANIAN_CI", value: "UTF8_ROMANIAN_CI" },
                { label: "UTF8_SINHALA_CI", value: "UTF8_SINHALA_CI" },
                { label: "UTF8_SLOVAK_CI", value: "UTF8_SLOVAK_CI" },
                { label: "UTF8_SLOVENIAN_CI", value: "UTF8_SLOVENIAN_CI" },
                { label: "UTF8_SPANISH2_CI", value: "UTF8_SPANISH2_CI" },
                { label: "UTF8_SPANISH_CI", value: "UTF8_SPANISH_CI" },
                { label: "UTF8_SWEDISH_CI", value: "UTF8_SWEDISH_CI" },
                { label: "UTF8_TOLOWER_CI", value: "UTF8_TOLOWER_CI" },
                { label: "UTF8_TURKISH_CI", value: "UTF8_TURKISH_CI" },
                { label: "UTF8_UNICODE_520_CI", value: "UTF8_UNICODE_520_CI" },
                { label: "UTF8_UNICODE_CI", value: "UTF8_UNICODE_CI" },
                { label: "UTF8_VIETNAMESE_CI", value: "UTF8_VIETNAMESE_CI" }
            ],
        "UTF8MB4": [
            { label: "UTF8MB4_GENERAL_CI", value: "UTF8MB4_GENERAL_CI" },
            { label: "UTF8MB4_0900_AI_CI", value: "UTF8MB4_0900_AI_CI" },
            { label: "UTF8MB4_0900_AS_CI", value: "UTF8MB4_0900_AS_CI" },
            { label: "UTF8MB4_0900_AS_CS", value: "UTF8MB4_0900_AS_CS" },
            { label: "UTF8MB4_0900_BIN", value: "UTF8MB4_0900_BIN" },
            { label: "UTF8MB4_BIN", value: "UTF8MB4_BIN" },
            { label: "UTF8MB4_CROATIAN_CI", value: "UTF8MB4_CROATIAN_CI" },
            { label: "UTF8MB4_CS_0900_AI_CI", value: "UTF8MB4_CS_0900_AI_CI" },
            { label: "UTF8MB4_CS_0900_AS_CS", value: "UTF8MB4_CS_0900_AS_CS" },
            { label: "UTF8MB4_CZECH_CI", value: "UTF8MB4_CZECH_CI" },
            { label: "UTF8MB4_DA_0900_AI_CI", value: "UTF8MB4_DA_0900_AI_CI" },
            { label: "UTF8MB4_DA_0900_AS_CS", value: "UTF8MB4_DA_0900_AS_CS" },
            { label: "UTF8MB4_DANISH_CI", value: "UTF8MB4_DANISH_CI" },
            { label: "UTF8MB4_DE_PB_0900_AI_CI", value: "UTF8MB4_DE_PB_0900_AI_CI" },
            { label: "UTF8MB4_DE_PB_0900_AS_CS", value: "UTF8MB4_DE_PB_0900_AS_CS" },
            { label: "UTF8MB4_EO_0900_AI_CI", value: "UTF8MB4_EO_0900_AI_CI" },
            { label: "UTF8MB4_EO_0900_AS_CS", value: "UTF8MB4_EO_0900_AS_CS" },
            { label: "UTF8MB4_ES_0900_AI_CI", value: "UTF8MB4_ES_0900_AI_CI" },
            { label: "UTF8MB4_ES_0900_AS_CS", value: "UTF8MB4_ES_0900_AS_CS" },
            { label: "UTF8MB4_ES_TRAD_0900_AI_CI", value: "UTF8MB4_ES_TRAD_0900_AI_CI" },
            { label: "UTF8MB4_ES_TRAD_0900_AS_CS", value: "UTF8MB4_ES_TRAD_0900_AS_CS" },
            { label: "UTF8MB4_ESPERANTO_CI", value: "UTF8MB4_ESPERANTO_CI" },
            { label: "UTF8MB4_ESTONIAN_CI", value: "UTF8MB4_ESTONIAN_CI" },
            { label: "UTF8MB4_ET_0900_AI_CI", value: "UTF8MB4_ET_0900_AI_CI" },
            { label: "UTF8MB4_ET_0900_AS_CS", value: "UTF8MB4_ET_0900_AS_CS" },
            { label: "UTF8MB4_GERMAN2_CI", value: "UTF8MB4_GERMAN2_CI" },
            { label: "UTF8MB4_HR_0900_AI_CI", value: "UTF8MB4_HR_0900_AI_CI" },
            { label: "UTF8MB4_HR_0900_AS_CS", value: "UTF8MB4_HR_0900_AS_CS" },
            { label: "UTF8MB4_HU_0900_AI_CI", value: "UTF8MB4_HU_0900_AI_CI" },
            { label: "UTF8MB4_HU_0900_AS_CS", value: "UTF8MB4_HU_0900_AS_CS" },
            { label: "UTF8MB4_HUNGARIAN_CI", value: "UTF8MB4_HUNGARIAN_CI" },
            { label: "UTF8MB4_ICELANDIC_CI", value: "UTF8MB4_ICELANDIC_CI" },
            { label: "UTF8MB4_IS_0900_AI_CI", value: "UTF8MB4_IS_0900_AI_CI" },
            { label: "UTF8MB4_IS_0900_AS_CS", value: "UTF8MB4_IS_0900_AS_CS" },
            { label: "UTF8MB4_JA_0900_AS_CS", value: "UTF8MB4_JA_0900_AS_CS" },
            { label: "UTF8MB4_JA_0900_AS_CS_KS", value: "UTF8MB4_JA_0900_AS_CS_KS" },
            { label: "UTF8MB4_LA_0900_AI_CI", value: "UTF8MB4_LA_0900_AI_CI" },
            { label: "UTF8MB4_LA_0900_AI_CI", value: "UTF8MB4_LA_0900_AI_CI" },
            { label: "UTF8MB4_LATVIAN_CI", value: "UTF8MB4_LATVIAN_CI" },
            { label: "UTF8MB4_LITHUANIAN_CI", value: "UTF8MB4_LITHUANIAN_CI" },
            { label: "UTF8MB4_LT_0900_AI_CI", value: "UTF8MB4_LT_0900_AI_CI" },
            { label: "UTF8MB4_LT_0900_AS_CS", value: "UTF8MB4_LT_0900_AS_CS" },
            { label: "UTF8MB4_LV_0900_AI_CI", value: "UTF8MB4_LV_0900_AI_CI" },
            { label: "UTF8MB4_LV_0900_AS_CS", value: "UTF8MB4_LV_0900_AS_CS" },
            { label: "UTF8MB4_PERSIAN_CI", value: "UTF8MB4_PERSIAN_CI" },
            { label: "UTF8MB4_PL_0900_AI_CI", value: "UTF8MB4_PL_0900_AI_CI" },
            { label: "UTF8MB4_PL_0900_AS_CS", value: "UTF8MB4_PL_0900_AS_CS" },
            { label: "UTF8MB4_POLISH_CI", value: "UTF8MB4_POLISH_CI" },
            { label: "UTF8MB4_RO_0900_AI_CI", value: "UTF8MB4_RO_0900_AI_CI" },
            { label: "UTF8MB4_RO_0900_AS_CS", value: "UTF8MB4_RO_0900_AS_CS" },
            { label: "UTF8MB4_ROMAN_CI", value: "UTF8MB4_ROMAN_CI" },
            { label: "UTF8MB4_ROMANIAN_CI", value: "UTF8MB4_ROMANIAN_CI" },
            { label: "UTF8MB4_RU_0900_AI_CI", value: "UTF8MB4_RU_0900_AI_CI" },
            { label: "UTF8MB4_RU_0900_AS_CS", value: "UTF8MB4_RU_0900_AS_CS" },
            { label: "UTF8MB4_SINHALA_CI", value: "UTF8MB4_SINHALA_CI" },
            { label: "UTF8MB4_SK_0900_AI_CI", value: "UTF8MB4_SK_0900_AI_CI" },
            { label: "UTF8MB4_SK_0900_AS_CS", value: "UTF8MB4_SK_0900_AS_CS" },
            { label: "UTF8MB4_SL_0900_AI_CI", value: "UTF8MB4_SL_0900_AI_CI" },
            { label: "UTF8MB4_SL_0900_AS_CS", value: "UTF8MB4_SL_0900_AS_CS" },
            { label: "UTF8MB4_SLOVAK_CI", value: "UTF8MB4_SLOVAK_CI" },
            { label: "UTF8MB4_SLOVENIAN_CI", value: "UTF8MB4_SLOVENIAN_CI" },
            { label: "UTF8MB4_SPANISH2_CI", value: "UTF8MB4_SPANISH2_CI" },
            { label: "UTF8MB4_SPANISH_CI", value: "UTF8MB4_SPANISH_CI" },
            { label: "UTF8MB4_SV_0900_AI_CI", value: "UTF8MB4_SV_0900_AI_CI" },
            { label: "UTF8MB4_SV_0900_AS_CS", value: "UTF8MB4_SV_0900_AS_CS" },
            { label: "UTF8MB4_SWEDISH_CI", value: "UTF8MB4_SWEDISH_CI" },
            { label: "UTF8MB4_TR_0900_AI_CI", value: "UTF8MB4_TR_0900_AI_CI" },
            { label: "UTF8MB4_TR_0900_AS_CS", value: "UTF8MB4_TR_0900_AS_CS" },
            { label: "UTF8MB4_TURKISH_CI", value: "UTF8MB4_TURKISH_CI" },
            { label: "UTF8MB4_UNICODE_520_CI", value: "UTF8MB4_UNICODE_520_CI" },
            { label: "UTF8MB4_UNICODE_CI", value: "UTF8MB4_UNICODE_CI" },
            { label: "UTF8MB4_VI_0900_AI_CI", value: "UTF8MB4_VI_0900_AI_CI" },
            { label: "UTF8MB4_VI_0900_AS_CS", value: "UTF8MB4_VI_0900_AS_CS" },
            { label: "UTF8MB4_VIETNAMESE_CI", value: "UTF8MB4_VIETNAMESE_CI" },
            { label: "UTF8MB4_ZH_0900_AS_CS", value: "UTF8MB4_ZH_0900_AS_CS" }
        ]
    }

    state = {
        // 审批
        opsAuditEmails: [],
        opsAuditEmailSelected: undefined,
        leaderEmails: undefined,
        opsLeaderEmail: undefined,
        // 业务树
        costTreeData: [],
        costNodeSelected: undefined,

        // 售卖规格配置
        sellConf: undefined,
        regions: undefined,
        regionIDSelected: undefined,
        zones: undefined,
        zoneIDSelected: undefined,
        versions: undefined,
        versionSelected: undefined,
        replicaNums: undefined,
        replicaNumSelected: undefined,
        // 副本可用区
        backupOneZoneIDSelected: undefined,
        backupTwoZoneIDSelected: undefined,

        instanceTypes: undefined,
        instanceTypeIDSelected: undefined,

        // 硬盘
        volumeMinSize: undefined,
        volumeMaxSize: undefined,
        volumeStep: undefined,
        volumeSizeSelected: undefined,
        // 字符集和校验规格
        characterSetSelected: undefined,
        collations: [],
        collationSelected: undefined,
        // 大小写敏感
        // lowerCaseTableNames: true,
        // 服务器名称
        instanceNameIndex: undefined,
        instanceName: undefined,
        // 申请陈述
        applyMsg: undefined,

        // 费用信息
        tradePrice: undefined,
    }

    // 初始化
    componentDidMount() {
        // 获取mysql售卖规格
        postCloudMysqlSellConf({ supplier: this.props.supplier }).then((resp) => {
            console.log(resp)
            let regionIDs = Object.keys(resp.data)
            this.setState({
                sellConf: resp.data,
                regions: regionIDs.map(regionID => { return { label: regionID, value: regionID } })
            })
        })
        // 获取成本模型树
        postModelTree({ "model_name": "cost", "attach_path_id": true }).then((resp) => {
            let treeData = resp.data
            this.bfsAddProps(treeData)
            this.setState({
                costTreeData: treeData
            })
        })
        // 获取运维审批
        requestOpsMember({}).then((resp) => {
            this.setState({
                opsAuditEmails: resp.members.map(item => {
                    return { label: item.substring(0, item.lastIndexOf("@")), value: item }
                })
            });
        });
        // 获取领导
        requestLeaderEmail({}).then(resp => {
            if (resp !== null) {
                this.setState({
                    leaderEmails: resp.email
                })
            }
        })
        // 获取运维领导邮箱
        postOpsLeader({}).then(resp => {
            if (resp !== null) {
                this.setState({
                    opsLeaderEmail: resp.ops_leader
                })
            }
        })
    }

    // 地区变更
    handleRegionChange = (regionID) => {
        let zoneIDs = Object.keys(this.state.sellConf[regionID])
        this.setState({
            regionIDSelected: regionID,
            zones: zoneIDs.map(zoneID => { return { label: zoneID, value: zoneID } }),
            // 级联影响
            backupOneZoneIDSelected: undefined,
            backupTwoZoneIDSelected: undefined,
            zoneIDSelected: undefined,
            versions: undefined,
            versionSelected: undefined,
            replicaNums: undefined,
            replicaNumSelected: undefined,
            instanceTypes: undefined,
            instanceTypeIDSelected: undefined,
            volumeMaxSize: undefined,
            volumeMinSize: undefined,
            volumeStep: undefined,
            volumeSizeSelected: undefined,
        })
    }
    // 可用区变更
    handleZoneChange = (zoneID) => {
        let regionID = this.state.regionIDSelected
        let versions = Object.keys(this.state.sellConf[regionID][zoneID])
        this.setState({
            zoneIDSelected: zoneID,
            versions: versions.map(version => { return { label: version, value: version } }),
            // 级联影响
            versionSelected: undefined,
            replicaNums: undefined,
            replicaNumSelected: undefined,
            instanceTypes: undefined,
            instanceTypeIDSelected: undefined,
            volumeMaxSize: undefined,
            volumeMinSize: undefined,
            volumeStep: undefined,
            volumeSizeSelected: undefined,
        })
    }
    // 版本变更
    handleVersionChange = (version) => {
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        let nodeNums = Object.keys(this.state.sellConf[regionID][zoneID][version])
        this.setState({
            versionSelected: version,
            replicaNums: nodeNums.map(nodeNum => { return { label: nodeNum - 1, value: nodeNum - 1 } }),
            // 级联影响
            replicaNumSelected: undefined,
            instanceTypes: undefined,
            instanceTypeIDSelected: undefined,
            volumeMaxSize: undefined,
            volumeMinSize: undefined,
            volumeStep: undefined,
            volumeSizeSelected: undefined,
        })
    }
    // 副本数变更
    handleReplicaNumChange = (replicaNum) => {
        let nodeNum = replicaNum + 1
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        let version = this.state.versionSelected
        let instanceTypes = this.state.sellConf[regionID][zoneID][version][nodeNum]
        let instanceTypeIDs = Object.keys(instanceTypes)
        this.setState({
            replicaNumSelected: replicaNum,
            instanceTypes: instanceTypeIDs.map(instanceTypeID => {
                const instanceType = instanceTypes[instanceTypeID];
                const { CPU, MemoryGB } = instanceType;
                const desc = `核心数:${CPU} | 内存: ${MemoryGB}GB | 规格：${instanceTypeID}`;
                return { label: desc, value: instanceTypeID, coreCount: CPU, memorySizeGB: MemoryGB };
            }).sort((a, b) => {
                if (a.coreCount === b.coreCount) {
                    return a.memorySizeGB - b.memorySizeGB;
                }
                return a.coreCount - b.coreCount;
            }),
            // 级联影响
            instanceTypeIDSelected: undefined,
            volumeMaxSize: undefined,
            volumeMinSize: undefined,
            volumeStep: undefined,
            volumeSizeSelected: undefined,
        })
    }
    // 副本1可用区变更
    handleBackupOneZoneChange = (zoneID) => {
        this.setState({
            backupOneZoneIDSelected: zoneID
        })

    }
    // 副本2可用区变更
    handleBackupTwoZoneChange = (zoneID) => {
        this.setState({
            backupTwoZoneIDSelected: zoneID
        })
    }

    // 规格变更
    handleInstanceTypeChange = (instanceTypeID) => {
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        let version = this.state.versionSelected
        let replicaNum = this.state.replicaNumSelected
        let nodeNum = replicaNum + 1
        let instanceType = this.state.sellConf[regionID][zoneID][version][nodeNum][instanceTypeID]
        let instanceNameIndex = `${this.props.supplier}_mysql_${version}_${zoneID}_${instanceType.MemoryGB}gb-`.replaceAll(".","_")
        this.setState({
            instanceTypeIDSelected: instanceTypeID,
            volumeMaxSize: instanceType.Volume.MaxSizeGB,
            volumeMinSize: instanceType.Volume.MinSizeGB,
            volumeStep: instanceType.Volume.SizeStepGb,
            volumeSizeSelected: instanceType.Volume.MinSizeGB,
            instanceNameIndex: instanceNameIndex,
        }, this.costEstimate)
    }
    // 硬盘变更
    handleDiskSizeChange = (value) => {
        this.setState({
            volumeSizeSelected: value
        }, this.costEstimate)
    }
    // 运维审批人
    handleOpsAuditPeopleChange = (value) => {
        this.setState({
            opsAuditEmailSelected: value
        })
    }
    // // 字符集变更
    // handleCharacterSetChange = (value) => {
    //     this.setState({
    //         characterSetSelected: value,
    //         collations: this.collationMap[value]
    //     })
    // }
    // // 字符校验规则变更
    // handleCollationChange = (value) => {
    //     this.setState({
    //         collationSelected: value
    //     })
    // }
    // // 允许表格名称小写
    // handleLowerCaseTabeNamesChange = (value, e) => {
    //     this.setState({
    //         lowerCaseTableNames: value
    //     })
    // }
    // 成本模型挂载
    handleCostTreeSelectChange = (value, lable, e) => {
        let path = value.split(".")
        if (path.length !== 3) {
            message.warn("请选择叶子节点！")
            this.setState({
                costNodeSelected: ""
            })
            return
        }
        this.setState({
            costNodeSelected: value
        })
    }
    // 实例名称变更
    handleInstanceNameChange = (e) => {
        this.setState({
            instanceName: e.target.value
        })
    }
    // 申请理由
    handleApplyMsgChange = (e) => {
        this.setState({
            applyMsg: e.target.value
        })
    }
    handleSubmit = () => {
        let opsAuditEmail = this.state.opsAuditEmailSelected
        if (opsAuditEmail === undefined) {
            message.warn("请先选择运维审批人！")
            return
        }
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        if (regionID === undefined || zoneID === undefined) {
            message.warn("请选择地区信息！")
            return
        }
        let version = this.state.versionSelected
        if (version === undefined) {
            message.warn("请选择数据库版本！")
            return
        }
        let replicaNum = this.state.replicaNumSelected
        if (replicaNum === undefined) {
            message.warn("请选择副本数量")
            return
        }
        let nodeNum = replicaNum + 1
        let instanceTypeID = this.state.instanceTypeIDSelected
        if (instanceTypeID === undefined) {
            message.warn("请选择规格信息！")
            return
        }
        let instanceType = this.state.sellConf[regionID][zoneID][version][nodeNum][instanceTypeID]
        let volumeSize = this.state.volumeSizeSelected
        if (volumeSize === undefined) {
            message.warn("请选择磁盘大小！")
            return
        }

        // 处理业务树节点id
        let costNodeSelected = this.state.costNodeSelected
        if (costNodeSelected === undefined) {
            message.warn("请选择业务树相关挂载！")
            return
        }
        let nodes = costNodeSelected.split(".")
        let nodeID = parseInt(nodes[nodes.length - 1])

        // 处理实例名称
        let instanceName = `${this.state.instanceNameIndex}${this.state.instanceName}`

        // 处理申请信息
        let apply_msg =
    `申请购买${this.props.supplierName}Mysql，预估费用为 ${this.state.tradePrice} 元/年。
数据库版本：${version}		
副本数：${replicaNum} 
CPU核心数：${instanceType.CPU} 
数据盘：${instanceType.Volume.Category} ${volumeSize}G
规格ID：${instanceTypeID}
Mysql名称：${instanceName}
可用区：${zoneID}
申请理由：${this.state.applyMsg}`
        apply_msg = apply_msg.replaceAll("\t", "")


        let orderArg = {
            "dry_run": false,
            "ops_audit_email": opsAuditEmail,
            "apply_msg": apply_msg,
            "cost_model_node_id": nodeID,

            "supplier": this.props.supplier,
            "region_id": this.state.regionIDSelected,
            "zone_id": this.state.zoneIDSelected,
            "instance_type_id": this.state.instanceTypeIDSelected,
            "instance_name": instanceName,
            "volume": {
                "category": instanceType.Volume.Category,
                "size_gb": volumeSize
            },
            "version": version,
            "replica_num": Number(replicaNum),
            // "ignore_case": this.state.lowerCaseTableNames,
            "instance_type": {
                "instance_type_group": instanceType.InstanceTypeGroup,
                "Instance_type_id": instanceType.InstanceTypeID,
                "cpu": instanceType.CPU,
                "memory_gb": instanceType.MemoryGB,
            }
        }

        console.log(orderArg)
        postBuyCloudMysql(orderArg).then((resp) => {
            if (resp !== null) {
                navigateModal(this.props.navigate, DoingOrderUrlSuffix, 5)
            }
        })

    }
    // 询价
    costEstimate = () => {
        let regionID = this.state.regionIDSelected
        let zoneID = this.state.zoneIDSelected
        let version = this.state.versionSelected
        let replicaNum = this.state.replicaNumSelected
        let nodeNum = replicaNum + 1
        let instanceTypeID = this.state.instanceTypeIDSelected
        let volumeSizeSelected = this.state.volumeSizeSelected

        // 检查
        if (regionID === undefined || zoneID === undefined) {
            return
        }
        if (version === undefined || replicaNum === undefined || instanceTypeID === undefined || volumeSizeSelected === undefined) {
            return
        }

        let instanceType = this.state.sellConf[regionID][zoneID][version][nodeNum][instanceTypeID]
        let arg = {
            supplier: this.props.supplier,
            regionID: regionID,
            zoneID: zoneID,
            instanceTypeID: instanceTypeID,
            version: version,
            memoryGB: instanceType.MemoryGB,
            Volume: {
                Category: instanceType.Category,
                SizeGB: volumeSizeSelected,
            },
            replicaNum: Number(replicaNum),
            instanceType: instanceType,
        }
        postCloudMysqlPriceOrder(arg).then(resp => {
            this.setState({
                tradePrice: resp.data.TradePrice.toFixed(2)
            })
        })
    }
    // ### 树
    // 优先遍历树，增加节点title和key属性
    bfsAddProps = (treeData) => {
        let queue = [...treeData]
        while (queue.length > 0) {
            let node = queue.shift()
            node.title = node.name
            node.label = node.name
            node.value = node.path_id
            if (node.children !== undefined && node.children !== null) {
                queue.push(...node.children)
            }
        }
    }
    // 组件销毁前调用，清除一些事件(比如定时事件)
    componentWillUnmount() {
        this.setState = (state, callback) => {
            return
        }
    }
    render() {
        return (
            <SideContent>
                <MysqlApllyForm>
                    <OrderTitle>{this.props.supplierName}Mysql申请</OrderTitle>
                    <OrderCompactRow>
                        <OrderCol>
                            <span>运维审批：</span>
                            <Select
                                options={this.state.opsAuditEmails}
                                placeholder="运维审批人"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                onChange={this.handleOpsAuditPeopleChange}
                                value={this.state.opsAuditEmailSelected}
                            />
                        </OrderCol>
                        <OrderCenterCol>
                            <span>审批人员：</span>
                            <Tag color="#108ee9" icon={<UserOutlined />}>
                                {this.state.leaderEmails}, {this.state.opsLeaderEmail}
                            </Tag>
                        </OrderCenterCol>
                    </OrderCompactRow>
                    {/* 区域地区 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>地区城市：</span>
                            <Select
                                placeholder="请选择地区"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.regions}
                                value={this.state.regionIDSelected}
                                onChange={this.handleRegionChange}
                            />
                        </OrderCol>
                        <OrderCol>
                            <span>可用区域：</span>
                            <Select

                                placeholder="请选择可用区"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.zones}
                                value={this.state.zoneIDSelected}
                                onChange={this.handleZoneChange}

                            />
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 版本 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>版&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;本：</span>
                            <Select
                                placeholder="请选择数据库版本"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.versions}
                                value={this.state.versionSelected}
                                onChange={this.handleVersionChange}
                            />
                        </OrderCol>
                        <OrderCol>
                            <span>副本数量：</span>
                            <Select
                                placeholder="副本数量"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.replicaNums}
                                value={this.state.replicaNumSelected}
                                onChange={this.handleReplicaNumChange}
                            />
                        </OrderCol>

                    </OrderCompactRow>
                    {/* 副本可用区 */}
                    <OrderCompactRow className={this.state.replicaNumSelected <= 0 ? "hide" : ""}>
                        <OrderCol className={this.state.replicaNumSelected <= 0 ? "hide" : ""}>
                            <span>副本 1 可用区：</span>
                            <Select
                                placeholder="请选择副本1可用区"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.zones}
                                value={this.state.backupOneZoneIDSelected}
                                onChange={this.handleBackupOneZoneChange}
                            />
                        </OrderCol>
                        <OrderCol className={this.state.replicaNumSelected < 2 ? "hide" : ""}>
                            <span>副本 2 可用区：</span>
                            <Select

                                placeholder="请选择副本2可用区"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.zones}
                                value={this.state.backupTwoZoneIDSelected}
                                onChange={this.handleBackupTwoZoneChange}
                            />
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 实例规格 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>实例规格：</span>
                            <Select
                                placeholder="请选择mysql实例规格"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.instanceTypes}
                                value={this.state.instanceTypeIDSelected}
                                onChange={this.handleInstanceTypeChange}
                            />
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 硬盘规格 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>硬盘容量：</span>
                            <Slider
                                min={this.state.volumeMinSize}
                                max={this.state.volumeMaxSize}
                                step={this.state.volumeStep}
                                style={{ width: "100%" }}
                                value={this.state.volumeSizeSelected}
                                onChange={this.handleDiskSizeChange}
                            />
                            <InputNumber
                                min={this.state.volumeMinSize}
                                max={this.state.volumeMaxSize}
                                step={this.state.volumeStep}
                                value={this.state.volumeSizeSelected}
                                onChange={this.handleDiskSizeChange}
                            />&ensp;G
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 字符设置 */}
                    {/* <OrderCompactRow>
                        <OrderCol>
                            <span>字符集&emsp;：</span>
                            <Select
                                placeholder="请选择字符集"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.characterSets}
                                value={this.state.characterSetSelected}
                                onChange={this.handleCharacterSetChange}

                            />
                        </OrderCol>
                        <OrderCol>
                            <span>排序规则：</span>
                            <Select
                                placeholder="请选择排序规则"
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                options={this.state.collations}
                                value={this.state.collationSelected}
                                onChange={this.handleCollationChange}
                            />
                        </OrderCol>
                        <OrderCenterCol>
                            <span>表名大小写敏感：</span>
                            <Switch
                                checkedChildren="开启"
                                unCheckedChildren="关闭"
                                onClick={this.handleLowerCaseTabeNamesChange}
                                value={this.state.lowerCaseTableNames}
                            />
                        </OrderCenterCol>
                    </OrderCompactRow> */}
                    {/* 节点挂载 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>业务树相关节点：</span>
                            <TreeSelect
                                placeholder="请选择业务树相关节点"
                                treeData={this.state.costTreeData}
                                onChange={this.handleCostTreeSelectChange}
                                value={this.state.costNodeSelected}
                            />
                        </OrderCol>
                        {/* <OrderCol>
                            <span>监控挂载：</span>
                            <TreeSelect
                                disabled={true}
                                placeholder="请选择监控挂载节点"
                            // treeData={this.state.costTreeData}
                            // onChange={this.handleMonitorTreeSelectChange}
                            // value={this.state.monitorNodeSelected}
                            />
                        </OrderCol> */}
                    </OrderCompactRow>
                    {/* 实例名称 */}
                    <OrderCompactRow>
                        <OrderCol>
                            <span>实例名称：</span>
                            <span>{this.state.instanceNameIndex}</span>
                            <Input
                                placeholder="请填写实例名称"
                                value={this.state.instanceName}
                                onChange={this.handleInstanceNameChange}
                            />
                        </OrderCol>
                    </OrderCompactRow>
                    {/* 申请陈述 */}
                    <OrderTextRow>
                        <OrderStartCol>
                            <span>申请陈述：</span>
                            <TextArea
                                placeholder="请填写申请理由，不少于5个字符"
                                style={{ height: "5vw" }}
                                value={this.state.applyMsg}
                                onChange={this.handleApplyMsgChange}
                            />
                        </OrderStartCol>
                    </OrderTextRow>
                    <OrderCompactRow>
                        <OrderEndCol>
                            <FeeSpan>
                                {/* 折扣：{this.state.discount}，折后 */}
                                费用预估：{this.state.tradePrice} 元/年&nbsp;
                                <MoneyCollectOutlined />
                            </FeeSpan>
                        </OrderEndCol>
                    </OrderCompactRow>
                    {/* 费用预估 */}
                    <OrderSubmit>
                        <Button type="primary" onClick={this.handleSubmit}>
                            提交
                        </Button>
                        <Button type="dashed" onClick={this.handleCancel}>
                            取消
                        </Button>
                    </OrderSubmit>
                </MysqlApllyForm>
            </SideContent>
        );
    }
}

MysqlApplyForm.propTypes = {
    supplier: PropTypes.string.isRequired,
    supplierName: PropTypes.string.isRequired,
}

export default withRouter(MysqlApplyForm);