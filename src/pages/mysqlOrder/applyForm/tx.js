import { Component } from "react";
import { Select, Button, Input, message, Tag, Slider, InputNumber, Switch, TreeSelect } from "antd";
import styled from "styled-components";
import { MoneyCollectOutlined, UserOutlined } from "@ant-design/icons";
import withRouter from "@/util/withRouter";
import { MysqlApllyForm } from "../style"
import {
	OrderCompactRow,
	OrderTitle,
	SideContent,
	OrderSubmit,
	OrderCol,
	OrderCenterCol,
	OrderStartCol,
	OrderEndCol,
	OrderTextRow
} from "@/common/styleLayout";
import {
	postModelTree,
	postOpsLeader,
	postTxMysqlConfs,
	postTxMysqlPrice,
	requestCommonOrder,
	requestLeaderEmail,
	requestOpsMember,
	// requestSuperLeaderEmails,
	requestTxRegion,
	requestTxZone
} from "@/request/api";
import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";


const { TextArea } = Input;

// css-js
export const FeeSpan = styled.span`
  color: red;
  font-size: large;
`
class TxMysqlApplyForm extends Component {
	schemaNameMap = {
		TKE: "基础单节点",
		Z3: "高可用"
	}
	characterSets = [
		{ label: "LATIN1", value: "LATIN1" },
		{ label: "UTF8", value: "UTF8" },
		{ label: "GBK", value: "GBK" },
		{ label: "UTF8MB4", value: "UTF8MB4" }
	]
	collationMap = {
		"LATIN1":
			[
				{ label: "LATIN1_SWEDISH_CI", value: "LATIN1_SWEDISH_CI" },
				{ label: "LATIN1_BIN", value: "LATIN1_BIN" },
				{ label: "LATIN1_DANISH_CI", value: "LATIN1_DANISH_CI" },
				{ label: "LATIN1_GENERAL_CI", value: "LATIN1_GENERAL_CI" },
				{ label: "LATIN1_GENERAL_CS", value: "LATIN1_GENERAL_CS" },
				{ label: "LATIN1_GERMAN1_CI", value: "LATIN1_GERMAN1_CI" },
				{ label: "LATIN1_GERMAN2_CI", value: "LATIN1_GERMAN2_CI" },
				{ label: "LATIN1_SPANISH_CI", value: "LATIN1_SPANISH_CI" }
			],
		"GBK":
			[
				{ label: "GBK_CHINESE_CI", value: "GBK_CHINESE_CI" },
				{ label: "GBK_BIN", value: "GBK_BIN" }
			],
		"UTF8":
			[
				{ label: "UTF8_GENERAL_CI", value: "UTF8_GENERAL_CI" },
				{ label: "UTF8_BIN", value: "UTF8_BIN" },
				{ label: "UTF8_CROATIAN_CI", value: "UTF8_CROATIAN_CI" },
				{ label: "UTF8_CZECH_CI", value: "UTF8_CZECH_CI" },
				{ label: "UTF8_DANISH_CI", value: "UTF8_DANISH_CI" },
				{ label: "UTF8_ESPERANTO_CI", value: "UTF8_ESPERANTO_CI" },
				{ label: "UTF8_ESTONIAN_CI", value: "UTF8_ESTONIAN_CI" },
				{ label: "UTF8_GENERAL_MYSQL500_CI", value: "UTF8_GENERAL_MYSQL500_CI" },
				{ label: "UTF8_GERMAN2_CI", value: "UTF8_GERMAN2_CI" },
				{ label: "UTF8_HUNGARIAN_CI", value: "UTF8_HUNGARIAN_CI" },
				{ label: "UTF8_ICELANDIC_CI", value: "UTF8_ICELANDIC_CI" },
				{ label: "UTF8_LATVIAN_CI", value: "UTF8_LATVIAN_CI" },
				{ label: "UTF8_LITHUANIAN_CI", value: "UTF8_LITHUANIAN_CI" },
				{ label: "UTF8_PERSIAN_CI", value: "UTF8_PERSIAN_CI" },
				{ label: "UTF8_POLISH_CI", value: "UTF8_POLISH_CI" },
				{ label: "UTF8_ROMAN_CI", value: "UTF8_ROMAN_CI" },
				{ label: "UTF8_ROMANIAN_CI", value: "UTF8_ROMANIAN_CI" },
				{ label: "UTF8_SINHALA_CI", value: "UTF8_SINHALA_CI" },
				{ label: "UTF8_SLOVAK_CI", value: "UTF8_SLOVAK_CI" },
				{ label: "UTF8_SLOVENIAN_CI", value: "UTF8_SLOVENIAN_CI" },
				{ label: "UTF8_SPANISH2_CI", value: "UTF8_SPANISH2_CI" },
				{ label: "UTF8_SPANISH_CI", value: "UTF8_SPANISH_CI" },
				{ label: "UTF8_SWEDISH_CI", value: "UTF8_SWEDISH_CI" },
				{ label: "UTF8_TOLOWER_CI", value: "UTF8_TOLOWER_CI" },
				{ label: "UTF8_TURKISH_CI", value: "UTF8_TURKISH_CI" },
				{ label: "UTF8_UNICODE_520_CI", value: "UTF8_UNICODE_520_CI" },
				{ label: "UTF8_UNICODE_CI", value: "UTF8_UNICODE_CI" },
				{ label: "UTF8_VIETNAMESE_CI", value: "UTF8_VIETNAMESE_CI" }
			],
		"UTF8MB4": [
			{ label: "UTF8MB4_GENERAL_CI", value: "UTF8MB4_GENERAL_CI" },
			{ label: "UTF8MB4_0900_AI_CI", value: "UTF8MB4_0900_AI_CI" },
			{ label: "UTF8MB4_0900_AS_CI", value: "UTF8MB4_0900_AS_CI" },
			{ label: "UTF8MB4_0900_AS_CS", value: "UTF8MB4_0900_AS_CS" },
			{ label: "UTF8MB4_0900_BIN", value: "UTF8MB4_0900_BIN" },
			{ label: "UTF8MB4_BIN", value: "UTF8MB4_BIN" },
			{ label: "UTF8MB4_CROATIAN_CI", value: "UTF8MB4_CROATIAN_CI" },
			{ label: "UTF8MB4_CS_0900_AI_CI", value: "UTF8MB4_CS_0900_AI_CI" },
			{ label: "UTF8MB4_CS_0900_AS_CS", value: "UTF8MB4_CS_0900_AS_CS" },
			{ label: "UTF8MB4_CZECH_CI", value: "UTF8MB4_CZECH_CI" },
			{ label: "UTF8MB4_DA_0900_AI_CI", value: "UTF8MB4_DA_0900_AI_CI" },
			{ label: "UTF8MB4_DA_0900_AS_CS", value: "UTF8MB4_DA_0900_AS_CS" },
			{ label: "UTF8MB4_DANISH_CI", value: "UTF8MB4_DANISH_CI" },
			{ label: "UTF8MB4_DE_PB_0900_AI_CI", value: "UTF8MB4_DE_PB_0900_AI_CI" },
			{ label: "UTF8MB4_DE_PB_0900_AS_CS", value: "UTF8MB4_DE_PB_0900_AS_CS" },
			{ label: "UTF8MB4_EO_0900_AI_CI", value: "UTF8MB4_EO_0900_AI_CI" },
			{ label: "UTF8MB4_EO_0900_AS_CS", value: "UTF8MB4_EO_0900_AS_CS" },
			{ label: "UTF8MB4_ES_0900_AI_CI", value: "UTF8MB4_ES_0900_AI_CI" },
			{ label: "UTF8MB4_ES_0900_AS_CS", value: "UTF8MB4_ES_0900_AS_CS" },
			{ label: "UTF8MB4_ES_TRAD_0900_AI_CI", value: "UTF8MB4_ES_TRAD_0900_AI_CI" },
			{ label: "UTF8MB4_ES_TRAD_0900_AS_CS", value: "UTF8MB4_ES_TRAD_0900_AS_CS" },
			{ label: "UTF8MB4_ESPERANTO_CI", value: "UTF8MB4_ESPERANTO_CI" },
			{ label: "UTF8MB4_ESTONIAN_CI", value: "UTF8MB4_ESTONIAN_CI" },
			{ label: "UTF8MB4_ET_0900_AI_CI", value: "UTF8MB4_ET_0900_AI_CI" },
			{ label: "UTF8MB4_ET_0900_AS_CS", value: "UTF8MB4_ET_0900_AS_CS" },
			{ label: "UTF8MB4_GERMAN2_CI", value: "UTF8MB4_GERMAN2_CI" },
			{ label: "UTF8MB4_HR_0900_AI_CI", value: "UTF8MB4_HR_0900_AI_CI" },
			{ label: "UTF8MB4_HR_0900_AS_CS", value: "UTF8MB4_HR_0900_AS_CS" },
			{ label: "UTF8MB4_HU_0900_AI_CI", value: "UTF8MB4_HU_0900_AI_CI" },
			{ label: "UTF8MB4_HU_0900_AS_CS", value: "UTF8MB4_HU_0900_AS_CS" },
			{ label: "UTF8MB4_HUNGARIAN_CI", value: "UTF8MB4_HUNGARIAN_CI" },
			{ label: "UTF8MB4_ICELANDIC_CI", value: "UTF8MB4_ICELANDIC_CI" },
			{ label: "UTF8MB4_IS_0900_AI_CI", value: "UTF8MB4_IS_0900_AI_CI" },
			{ label: "UTF8MB4_IS_0900_AS_CS", value: "UTF8MB4_IS_0900_AS_CS" },
			{ label: "UTF8MB4_JA_0900_AS_CS", value: "UTF8MB4_JA_0900_AS_CS" },
			{ label: "UTF8MB4_JA_0900_AS_CS_KS", value: "UTF8MB4_JA_0900_AS_CS_KS" },
			{ label: "UTF8MB4_LA_0900_AI_CI", value: "UTF8MB4_LA_0900_AI_CI" },
			{ label: "UTF8MB4_LA_0900_AI_CI", value: "UTF8MB4_LA_0900_AI_CI" },
			{ label: "UTF8MB4_LATVIAN_CI", value: "UTF8MB4_LATVIAN_CI" },
			{ label: "UTF8MB4_LITHUANIAN_CI", value: "UTF8MB4_LITHUANIAN_CI" },
			{ label: "UTF8MB4_LT_0900_AI_CI", value: "UTF8MB4_LT_0900_AI_CI" },
			{ label: "UTF8MB4_LT_0900_AS_CS", value: "UTF8MB4_LT_0900_AS_CS" },
			{ label: "UTF8MB4_LV_0900_AI_CI", value: "UTF8MB4_LV_0900_AI_CI" },
			{ label: "UTF8MB4_LV_0900_AS_CS", value: "UTF8MB4_LV_0900_AS_CS" },
			{ label: "UTF8MB4_PERSIAN_CI", value: "UTF8MB4_PERSIAN_CI" },
			{ label: "UTF8MB4_PL_0900_AI_CI", value: "UTF8MB4_PL_0900_AI_CI" },
			{ label: "UTF8MB4_PL_0900_AS_CS", value: "UTF8MB4_PL_0900_AS_CS" },
			{ label: "UTF8MB4_POLISH_CI", value: "UTF8MB4_POLISH_CI" },
			{ label: "UTF8MB4_RO_0900_AI_CI", value: "UTF8MB4_RO_0900_AI_CI" },
			{ label: "UTF8MB4_RO_0900_AS_CS", value: "UTF8MB4_RO_0900_AS_CS" },
			{ label: "UTF8MB4_ROMAN_CI", value: "UTF8MB4_ROMAN_CI" },
			{ label: "UTF8MB4_ROMANIAN_CI", value: "UTF8MB4_ROMANIAN_CI" },
			{ label: "UTF8MB4_RU_0900_AI_CI", value: "UTF8MB4_RU_0900_AI_CI" },
			{ label: "UTF8MB4_RU_0900_AS_CS", value: "UTF8MB4_RU_0900_AS_CS" },
			{ label: "UTF8MB4_SINHALA_CI", value: "UTF8MB4_SINHALA_CI" },
			{ label: "UTF8MB4_SK_0900_AI_CI", value: "UTF8MB4_SK_0900_AI_CI" },
			{ label: "UTF8MB4_SK_0900_AS_CS", value: "UTF8MB4_SK_0900_AS_CS" },
			{ label: "UTF8MB4_SL_0900_AI_CI", value: "UTF8MB4_SL_0900_AI_CI" },
			{ label: "UTF8MB4_SL_0900_AS_CS", value: "UTF8MB4_SL_0900_AS_CS" },
			{ label: "UTF8MB4_SLOVAK_CI", value: "UTF8MB4_SLOVAK_CI" },
			{ label: "UTF8MB4_SLOVENIAN_CI", value: "UTF8MB4_SLOVENIAN_CI" },
			{ label: "UTF8MB4_SPANISH2_CI", value: "UTF8MB4_SPANISH2_CI" },
			{ label: "UTF8MB4_SPANISH_CI", value: "UTF8MB4_SPANISH_CI" },
			{ label: "UTF8MB4_SV_0900_AI_CI", value: "UTF8MB4_SV_0900_AI_CI" },
			{ label: "UTF8MB4_SV_0900_AS_CS", value: "UTF8MB4_SV_0900_AS_CS" },
			{ label: "UTF8MB4_SWEDISH_CI", value: "UTF8MB4_SWEDISH_CI" },
			{ label: "UTF8MB4_TR_0900_AI_CI", value: "UTF8MB4_TR_0900_AI_CI" },
			{ label: "UTF8MB4_TR_0900_AS_CS", value: "UTF8MB4_TR_0900_AS_CS" },
			{ label: "UTF8MB4_TURKISH_CI", value: "UTF8MB4_TURKISH_CI" },
			{ label: "UTF8MB4_UNICODE_520_CI", value: "UTF8MB4_UNICODE_520_CI" },
			{ label: "UTF8MB4_UNICODE_CI", value: "UTF8MB4_UNICODE_CI" },
			{ label: "UTF8MB4_VI_0900_AI_CI", value: "UTF8MB4_VI_0900_AI_CI" },
			{ label: "UTF8MB4_VI_0900_AS_CS", value: "UTF8MB4_VI_0900_AS_CS" },
			{ label: "UTF8MB4_VIETNAMESE_CI", value: "UTF8MB4_VIETNAMESE_CI" },
			{ label: "UTF8MB4_ZH_0900_AS_CS", value: "UTF8MB4_ZH_0900_AS_CS" }
		]
	}

	state = {
		opsAuditEmails: [],
		opsAuditEmailSelected: undefined,
		leaderEmails: undefined,
		opsLeaderEmail: undefined,
		regions: [],
		regionSelected: undefined,
		zones: [],
		zoneSelected: undefined,
		mysqlConfsMap: undefined,
		schema: [],
		schemaSelected: undefined,
		engineVersion: [],
		engineVersionSelected: undefined,
		backupZoneHide: true,
		slaveZone: [],
		slaveZoneSelected: undefined,
		backupZone: [],
		backupZoneSelected: undefined,
		mysqlConfs: [],
		mysqlConfSelected: undefined,
		volumeSize: undefined,
		volumeMinSize: undefined,
		volumeMaxSize: undefined,
		volumeStep: undefined,
		characterSetSelected: undefined,
		collations: [],
		collationSelected: undefined,
		lowerCaseTableNames: true,
		costTreeData: [],
		costNodeSelected: undefined,
		monitorNodeSelected: undefined,
		mysqlNameIndex: undefined,
		mysqlName: "",
		applyMsg: "",
		instanceDiscountPrice: undefined,
		discount: undefined
	}
	// 运维审批人
	handleOpsAuditPeopleChange = (value) => {
		this.setState({
			opsAuditEmailSelected: value
		})
	}
	// 地区
	handleRegionChange = (value) => {
		this.setState({
			regionSelected: value
		})
		let arg = {
			product: "cvm",
			region: value

		}
		requestTxZone(arg).then((resp) => {
			this.setState({
				zones: resp.zones.map(item => { return { label: item.zone_name, value: item.zone } }),
				zoneSelected: undefined,
				schemaSelected: undefined,
				engineVersionSelected: undefined,
				mysqlConfSelected: undefined,
				volumeSize: undefined,
				slaveZoneSelected: undefined,
				backupZoneSelected: undefined
			})
		})
	}
	// 可用区
	handleZoneChange = (value) => {
		this.setState({
			zoneSelected: value
		})
		let arg = {
			region: this.state.regionSelected,
			zone: value
		}
		// 获取mysql 规格
		postTxMysqlConfs(arg).then((resp) => {
			if (resp === null || resp.zoneMysqlConf === null) {
				message.warn(`${this.state.zoneSelected}没有可售规格，请选择其他可用区！`)
				return
			}
			let mysqlConfsMap = {}
			let schema = []
			let slaveZone = resp.zoneMysqlConf.ZoneConf.SlaveZone.map(item => {
				return { label: item, value: item }
			})
			let backupZone = resp.zoneMysqlConf.ZoneConf.BackupZone.map(item => {
				return { label: item, value: item }
			})
			resp.zoneMysqlConf.ZoneConf.SlaveZone.map(item => {
				return { label: item, value: item }
			})
			resp.zoneMysqlConf.SellType.forEach(element => {
				mysqlConfsMap[element.TypeName] = {
					EngineVersion: element.EngineVersion,
					Configs: element.Configs
				}
				schema.push({
					label: this.schemaNameMap[element.TypeName],
					value: element.TypeName
				})
			});
			this.setState({
				schema: schema,
				mysqlConfsMap: mysqlConfsMap,
				slaveZone: slaveZone,
				backupZone: backupZone,
				schemaSelected: undefined,
				engineVersionSelected: undefined,
				mysqlConfSelected: undefined,
				volumeSize: undefined,
				slaveZoneSelected: undefined,
				backupZoneSelected: undefined
			})
		})
	}
	// 架构
	handleSchemaChange = (value) => {
		let engineVersion = []
		let backupZoneHide = true
		let mysqlConfs = []
		// 处理数据库版本
		this.state.mysqlConfsMap[value].EngineVersion.forEach(element => {
			engineVersion.push({
				label: element,
				value: element
			})
		});
		// 处理规格
		this.state.mysqlConfsMap[value].Configs.forEach(element => {
			mysqlConfs.push({
				label: `cpu:${element.Cpu} | memory:${element.Memory} | engineType:${element.EngineType} | qps:${element.Qps} | iops:${element.Iops} | connection:${element.Connection}`,
				value: `${element.VolumeMin}-${element.VolumeMax}-${element.VolumeStep}-${element.Cpu}-${element.Memory}-${element.DeviceType}`
			})
		})
		// 处理从备可用区
		if (value === Object.keys(this.schemaNameMap)[1]) {
			backupZoneHide = false
		}
		this.setState({
			schemaSelected: value,
			backupZoneHide: backupZoneHide,
			engineVersion: engineVersion,
			mysqlConfs: mysqlConfs,
			engineVersionSelected: undefined,
			mysqlConfSelected: undefined,
			volumeSize: undefined
		})
	}
	// 处理数据库版本变更
	handleEngineVersionChange = (value) => {
		this.setState({
			engineVersionSelected: value
		})
	}
	// 从节点可用区
	handleSlaveZoneChange = (value) => {
		this.setState({
			slaveZoneSelected: value
		})
	}
	// 备节点可用区
	handleBackupChange = (value) => {
		this.setState({
			backupZoneSelected: value
		})
	}
	// 规格变更
	handleMysqlConfChange = (value) => {
		let confs = value.split("-")
		this.setState({
			mysqlConfSelected: value,
			volumeSize: parseInt(confs[0]),
			volumeMinSize: parseInt(confs[0]),
			volumeMaxSize: parseInt(confs[1]),
			volumeStep: parseInt(confs[2]),
		}, this.costEstimate)
	}
	// 数据盘大小变更
	handleDiskSizeChange = (value) => {
		this.setState({
			volumeSize: value
		}, this.costEstimate)
	}
	// 字符集变更
	handleCharacterSetChange = (value) => {
		this.setState({
			characterSetSelected: value,
			collations: this.collationMap[value]
		})
	}
	// 字符校验规则变更
	handleCollationChange = (value) => {
		this.setState({
			collationSelected: value
		})
	}
	// 允许表格名称小写
	handleLowerCaseTabeNamesChange = (value, e) => {
		this.setState({
			lowerCaseTableNames: value
		})
	}
	// 成本模型挂载
	handleCostTreeSelectChange = (value, lable, e) => {
		let path = value.split(".")
		if (path.length !== 3) {
			message.warn("请选择叶子节点！")
			this.setState({
				costNodeSelected: ""
			})
			return
		}
		this.setState({
			costNodeSelected: value
		})
	}
	// 监控模型挂载
	// handleMonitorTreeSelectChange = (value) => {
	// }
	// 实例名称
	handleMysqlNameChange = (e) => {
		this.setState({
			mysqlName: e.target.value
		})
	}
	// 申请理由
	handleApplyMsgChange = (e) => {
		this.setState({
			applyMsg: e.target.value
		})
	}
	handleSubmit = () => {
		if (this.state.opsAuditEmailSelected === undefined || this.state.opsAuditEmailSelected.length === 0) {
			message.error("请先选择运维审批人！")
			return
		}
		if (this.state.regionSelected === undefined || this.state.regionSelected.length === 0) {
			message.warn("请先选择地区！")
			return
		}
		if (this.state.zoneSelected === undefined || this.state.zoneSelected.length === 0) {
			message.warn("请先选择可用区！")
			return
		}
		if (this.state.schemaSelected === undefined || this.state.schemaSelected.length === 0) {
			message.warn("请先选择节点架构！")
			return
		}
		if (this.state.engineVersionSelected === undefined || this.state.engineVersionSelected.length === 0) {
			message.warn("请先选择数据库引擎版本！")
			return
		}
		if (this.state.mysqlConfSelected === undefined || this.state.mysqlConfSelected.length === 0) {
			message.warn("请先选择实例规格！")
			return
		}
		if (this.state.characterSetSelected === undefined || this.state.characterSetSelected.length === 0) {
			message.warn("请先选择字符集！")
			return
		}
		if (this.state.collationSelected === undefined || this.state.collationSelected.length === 0) {
			message.warn("请先选择字符排序校验规则！")
			return
		}
		if (this.state.costNodeSelected === undefined || this.state.costNodeSelected.length === 0) {
			message.warn("请先选择业务树相关节点！")
			return
		}
		let modelNodes = this.state.costNodeSelected.split(".")
		// 处理节点数
		let nodeNum = 1
		if (this.state.schemaSelected === "Z3") {
			nodeNum = 3
		}
		// 处理硬件规格
		let [, , , cpu, memory, deviceType] = this.state.mysqlConfSelected.split("-")
		let apply_info = {
			productType: "cdb",
			ops_audit_email: this.state.opsAuditEmailSelected,
			region: this.state.regionSelected,
			zone: this.state.zoneSelected,
			chargeType: "PREPAID",
			period: 1,
			count: 1,
			instanceNodes: nodeNum,
			version: this.state.engineVersionSelected,
			cpu: parseInt(cpu),
			memory: parseInt(memory),
			volume: this.state.volumeSize,
			deviceType: deviceType,
			name: this.state.mysqlNameIndex + this.state.mysqlName,
			apply_msg: this.state.applyMsg,
			instanceRole: "master",
			protectMode: 0,
			unit: parseInt(modelNodes[modelNodes.length - 1]),
			dryRun: false,
			paramList: [
				{ name: "character_set_server", value: this.state.characterSetSelected },
				{ name: "collation_server", value: this.state.collationSelected },
				{ name: "lower_case_table_names", value: this.state.lowerCaseTableNames.toString() }
			]
		}
		let arg = {
			order_type: "txy_db_apply",
			apply_info: JSON.stringify(apply_info),
			apply_msg: this.state.applyMsg,
			exigency: 1
		}
		arg.apply_msg =
			`申请购买腾讯云mysql，预估费用为 ${this.state.instanceDiscountPrice} 元/年
		mysql配置：${apply_info.cpu}核，${apply_info.memory}M 内存，${apply_info.volume}G 硬盘
		地区：${apply_info.region}，可用区：${apply_info.zone}
		mysql名称：${apply_info.name}
		申请理由：${arg.apply_msg}`.replaceAll("\t", "")
		console.log(arg)
		requestCommonOrder(arg).then(resp => {
			console.log(resp)
			if (resp !== null) {
				navigateModal(this.props.navigate, DoingOrderUrlSuffix, 5)
			}
		})
	}
	handelMysqlNameIndex = () => {
		if (this.state.mysqlConfSelected === undefined || this.state.mysqlConfSelected.length === 0) {
			return
		}
		let [, , , cpu, memory,] = this.state.mysqlConfSelected.split("-")
		this.setState({
			mysqlNameIndex: `tx_mysql_${this.state.zoneSelected}_${cpu}Core_${memory}M-`
		})
	}
	costEstimate = () => {
		this.handelMysqlNameIndex()
		// ${element.VolumeMin}-${element.VolumeMax}-${element.VolumeStep}-${element.Cpu}-${element.Memory}-${element.DeviceType}
		if (this.state.mysqlConfSelected === undefined || this.state.mysqlConfSelected.length === 0) {
			return
		}
		let [, , , cpu, memory, deviceType] = this.state.mysqlConfSelected.split("-")
		let nodeNum = 1
		if (this.state.schemaSelected === "Z3") {
			nodeNum = 3
		}
		// let mysql_name_index = 
		let arg = {
			region: this.state.regionSelected,
			zone: this.state.zoneSelected,
			chargeType: "PREPAID",
			period: 12,
			count: 1,
			cpu: cpu,
			memory: memory,
			volume: this.state.volumeSize,
			"version": this.state.engineVersionSelected,
			deviceType: deviceType,
			instanceNodes: nodeNum,
			instanceRole: "master",
			"protectMode": 0,
		}
		console.log(arg)
		postTxMysqlPrice(arg).then((resp) => {
			this.setState({
				instanceDiscountPrice: resp.price / 100
			})
		})

	}
	// ### 树
	// 优先遍历树，增加节点title和key属性
	bfsAddProps = (treeData) => {
		let queue = [...treeData]
		while (queue.length > 0) {
			let node = queue.shift()
			node.title = node.name
			node.label = node.name
			node.value = node.path_id
			if (node.children !== undefined && node.children !== null) {
				queue.push(...node.children)
			}
		}
	}
	componentDidMount() {
		// 获取运维审批
		requestOpsMember({}).then((resp) => {
			this.setState({
				opsAuditEmails: resp.members.map(item => {
					return { label: item.substring(0, item.lastIndexOf("@")), value: item }
				})
			});
		});
		// 获取领导
		// requestSuperLeaderEmails({}).then((resp) => {
		// 	this.setState({
		// 		leaderEmails: resp.emails.join(" ")
		// 	})
		// })
		requestLeaderEmail({}).then(resp => {
			if (resp !== null) {
				this.setState({
					leaderEmails: resp.email
				})
			}
		})
		// 获取运维领导邮箱
		postOpsLeader({}).then(resp => {
			if (resp !== null) {
				this.setState({
					opsLeaderEmail: resp.ops_leader
				})
			}
		})
		// 获取地区
		let arg = {
			product: "cdb"
		}
		requestTxRegion(arg).then((resp) => {
			this.setState({
				regions: resp.regions.map(item => { return { label: item.region_name, value: item.region } })
			})
		})
		// 获取成本模型树
		postModelTree({ "model_name": "cost", "attach_path_id": true }).then((resp) => {
			let treeData = resp.data
			this.bfsAddProps(treeData)
			console.log(treeData)
			this.setState({
				costTreeData: treeData
			})
		})

	}
	// 组件销毁前调用，清除一些事件(比如定时事件)
	componentWillUnmount() {
		this.setState = (state, callback) => {
			return
		}
	}
	render() {
		return (
			<SideContent>
				<MysqlApllyForm>
					<OrderTitle>腾讯云Mysql申请</OrderTitle>
					<OrderCompactRow>
						<OrderCol>
							<span>运维审批：</span>
							<Select
								options={this.state.opsAuditEmails}
								placeholder="运维审批人"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleOpsAuditPeopleChange}
								value={this.state.opsAuditEmailSelected}
							/>
						</OrderCol>
						<OrderCenterCol>
							<span>审批人员：</span>
							<Tag color="#108ee9" icon={<UserOutlined />}>
								{this.state.leaderEmails}，{this.state.opsLeaderEmail}
							</Tag>
						</OrderCenterCol>
					</OrderCompactRow>
					{/* 区域地区 */}
					<OrderCompactRow>
						<OrderCol>
							<span>地区城市：</span>
							<Select
								options={this.state.regions}
								placeholder="请选择地区"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleRegionChange}
							/>
						</OrderCol>
						<OrderCol>
							<span>可用区域：</span>
							<Select
								options={this.state.zones}
								placeholder="请选择可用区"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								value={this.state.zoneSelected}
								onChange={this.handleZoneChange}
							/>
						</OrderCol>
					</OrderCompactRow>
					{/* 架构，版本 */}
					<OrderCompactRow>
						<OrderCol>
							<span>节点架构：</span>
							<Select
								options={this.state.schema}
								placeholder="请选择节点架构"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleSchemaChange}
								value={this.state.schemaSelected}
							/>
						</OrderCol>
						<OrderCol>
							<span>引擎版本：</span>
							<Select
								options={this.state.engineVersion}
								placeholder="请选择数据库版本"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleEngineVersionChange}
								value={this.state.engineVersionSelected}
							/>
						</OrderCol>
					</OrderCompactRow>
					{/* 可用区 */}
					<OrderCompactRow className={this.state.backupZoneHide ? "hide" : ""}>
						<OrderCol>
							<span>从可用区：</span>
							<Select
								options={this.state.slaveZone}
								placeholder="请选择从节点可用区"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleSlaveZoneChange}
								value={this.state.slaveZoneSelected}
							/>
						</OrderCol>
						<OrderCol>
							<span>备可用区：</span>
							<Select
								options={this.state.backupZone}
								placeholder="请选择备节点可用区"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleBackupChange}
								value={this.state.backupZoneSelected}
							/>
						</OrderCol>
					</OrderCompactRow>
					{/* 实例规格 */}
					<OrderCompactRow>
						<OrderCol>
							<span>实例规格：</span>
							<Select
								options={this.state.mysqlConfs}
								placeholder="请选择mysql实例规格"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								onChange={this.handleMysqlConfChange}
								value={this.state.mysqlConfSelected}
							/>
						</OrderCol>
					</OrderCompactRow>
					{/* 硬盘规格 */}
					<OrderCompactRow>
						<OrderCol>
							<span>硬盘容量：</span>
							<Slider
								min={this.state.volumeMinSize}
								max={this.state.volumeMaxSize}
								step={this.state.volumeStep}
								style={{ width: "100%" }}
								value={this.state.volumeSize}
								onChange={this.handleDiskSizeChange}
							/>
							<InputNumber
								min={this.state.volumeMinSize}
								max={this.state.volumeMaxSize}
								step={this.state.volumeStep}
								value={this.state.volumeSize}
								onChange={this.handleDiskSizeChange}
							/>&ensp;G
						</OrderCol>
					</OrderCompactRow>
					{/* 字符设置 */}
					<OrderCompactRow>
						<OrderCol>
							<span>字符集&emsp;：</span>
							<Select
								options={this.characterSets}
								placeholder="请选择字符集"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								value={this.state.characterSetSelected}
								onChange={this.handleCharacterSetChange}

							/>
						</OrderCol>
						<OrderCol>
							<span>排序规则：</span>
							<Select
								options={this.state.collations}
								placeholder="请选择排序规则"
								showSearch
								filterOption={(input, option) =>
									(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
								}
								value={this.state.collationSelected}
								onChange={this.handleCollationChange}
							/>
						</OrderCol>
						<OrderCenterCol>
							<span>表名大小写敏感：</span>
							<Switch
								checkedChildren="开启"
								unCheckedChildren="关闭"
								onClick={this.handleLowerCaseTabeNamesChange}
								value={this.state.lowerCaseTableNames}
							/>
						</OrderCenterCol>
					</OrderCompactRow>
					{/* 节点挂载 */}
					<OrderCompactRow>
						<OrderCol>
							<span>业务树相关节点：</span>
							<TreeSelect
								placeholder="请选择业务树相关节点"
								treeData={this.state.costTreeData}
								onChange={this.handleCostTreeSelectChange}
								value={this.state.costNodeSelected}
							/>
						</OrderCol>
						{/* <OrderCol>
							<span>监控挂载：</span>
							<TreeSelect
								disabled={true}
								placeholder="请选择监控挂载节点"
								treeData={this.state.costTreeData}
								onChange={this.handleMonitorTreeSelectChange}
								value={this.state.monitorNodeSelected}
							/>
						</OrderCol> */}
					</OrderCompactRow>
					{/* 名称 */}
					<OrderCompactRow>
						<OrderCol>
							<span>实例名称：</span>
							<span>{this.state.mysqlNameIndex}</span>
							<Input
								placeholder="请填写实例名称"
								value={this.state.mysqlName}
								onChange={this.handleMysqlNameChange}
							/>
						</OrderCol>
					</OrderCompactRow>
					{/* 申请陈述 */}
					<OrderTextRow>
						<OrderStartCol>
							<span>申请陈述：</span>
							<TextArea
								placeholder="请填写申请理由，不少于5个字符"
								style={{ height: "5vw" }}
								value={this.state.applyMsg}
								onChange={this.handleApplyMsgChange}
							/>
						</OrderStartCol>
					</OrderTextRow>
					<OrderCompactRow>
						<OrderEndCol>
							<FeeSpan>
								{/* 折扣：{this.state.discount}，折后 */}
								费用预估：{this.state.instanceDiscountPrice} 元/年&nbsp;
								<MoneyCollectOutlined />
							</FeeSpan>
						</OrderEndCol>
					</OrderCompactRow>
					{/* 费用预估 */}
					<OrderSubmit>
						<Button type="primary" onClick={this.handleSubmit}>
							提交
						</Button>
						<Button type="dashed" onClick={this.handleCancel}>
							取消
						</Button>
					</OrderSubmit>
				</MysqlApllyForm>
			</SideContent>
		);
	}
}

export default withRouter(TxMysqlApplyForm);