import { Component } from "react";
import { Input, Tree, Tabs, Drawer, Button, Space, message, Radio, Tag, TreeSelect } from "antd";
import styled from "styled-components";
import { PlusCircleOutlined,DeleteOutlined,EditOutlined,DragOutlined } from '@ant-design/icons';
import { UserOutlined } from "@ant-design/icons";
import { OrderCol, OrderHalfCol, OrderRow, OrderTitle, OrderWideRow, ShadeForm, SideContent } from "@/common/styleLayout";
import { SpaceButton } from "@/common/styleAnt";
import { deepClone } from "@/util/objHelper";
import {
    // postModelParentPath,
    postModelTree2,
    postNewModelChildNode,
    postNewModelNode,
    postOpsLeader,
    requestLeaderEmail,
    postModifyModelChildNode,
    postDeleteModelChildNode,
    postDragModelChildNode
} from "@/request/api";
import withRouter from "@/util/withRouter";
import navigateModal from "@/util/navigateModal";
import { DoingOrderUrlSuffix } from "@/global";


const ModelTabs = styled(Tabs)`
    width: 100%;
    height: 100%;
`
const ModelSearch = styled(Input.Search)`
    margin-bottom: 0.65vw;
`
const OrderHalfCenterCol = styled(OrderHalfCol)`
    justify-content: center;
`
const RedSpan = styled.span`
    color: red;
`
// const BlueFillSpan = styled.span`
//     color: #fff;
//     background: #1890ff;
//     border-color: #1890ff;
//     height: 1.5vw;
//     line-height: 1.5vw;
//     padding: 0 0.5vw;
//     text-align: center;
// `
// const BlueLine = styled.hr`
//     width: 5vw;
//     size: 1px;
//     color: #1890ff;
//     background: #1890ff;
//     border-color: #1890ff;
//     margin: 0 0.25vw;
// `
const TitleSpan = styled.span`
    font-size: large;
    font-weight: bold;
`
// const BoldSpan = styled.span`
//     font-weight: bold;
// `
// const DescP = styled.p`
//     line-height: 1.5vw;
// `
const ModelTree = styled(Tree)`
    max-height: 28.5vw;
    overflow-y: scroll;
    overflow-anchor: none;
`

class CmdbModelManger extends Component {
    // apportionWays = [
    //     {
    //         label: '人工比例',
    //         value: 0
    //     },
    //     {
    //         label: '自动均分',
    //         value: 1
    //     }
    // ]
    state = {
        // 审批人
        approvalEmail: undefined,
        opsLeaderEmail: undefined,
        // 树
        srcTreeData: [],
        treeData: [],
        treeExpandedKeys: [],
        autoExpandParent: false,
        selectedTreeNode: undefined,
        // selectedSupperNodeList: [],
        // 搜索框
        searchStr: "",
        // 抽屉
        newNodeDrawerOpen: false,
        modifyNodeDrawerOpen: false,
        dragNodeDrawerOpen: false,
        deleteNodeDrawerOpen: false,
        // apportionDrawerOpen: false,
        // apportionInfo: {}, // parent_id:{parentName:*,auto_weight: *,value: *,change: *} 
        // apportionApllyMsg: "",
        nodeTypes: [
            {
                label: '子级节点',
                value: 'sub',
                disabled: false
            },
            {
                label: '同级节点',
                value: 'same',
                disabled: false
            }
        ],
        newNodeTypeSelect: undefined,
        newNodeName: "",
        newNodeApplyMsg: "",
        newNodeProductOwner:"",
        newNodeDevelopOwner:"",
        newNodeBusinessOwner:"",
        dragNewNode:"",
        dragNewNodeSelect:undefined,
    }

    // ### 搜索框
    handleSearchChange = (e) => {
        let searchStr = e.target.value
        this.setState({
            searchStr: searchStr
        })

    }
    handleSearch = () => {
        let searchStr = this.state.searchStr
        let treeData = deepClone(this.state.srcTreeData)
        let targetSet = new Set()
        if (searchStr.length > 0) {
            treeData.forEach(childNode => {
                this.dfsTitle(childNode, searchStr, targetSet)
            })
        }
        this.setState({
            searchStr: searchStr,
            treeData: treeData,
            treeExpandedKeys: Array.from(targetSet),
            autoExpandParent: true
        })
    }

    // ### 树
    // 优先遍历树，增加节点title和key属性
    bfsAddProps = (treeData) => {
        let queue = [...treeData]
        while (queue.length > 0) {
            let node = queue.shift()
            const BusinessOwner = node.BusinessOwner ? `业务负责人:${node.BusinessOwner}` : '';
            const ProductOwner = node.ProductOwner ? `产品负责人:${node.ProductOwner}` : '';
            const DevelopOwner = node.DevelopOwner ? `开发负责人:${node.DevelopOwner}` : '';
    
            let ownerInfo = node.NodeName;
    
            const owners = [BusinessOwner, ProductOwner, DevelopOwner].filter(Boolean);
            if (owners.length > 0) {
                ownerInfo += ` [${owners.join('，')}]`;
            }
            node.title = `${ownerInfo}`
            node.label = node.NodeName
            node.value = node.Layer + "-" + node.ID
            node.children = node.Children
            if (node.children !== undefined && node.children !== null) {
                queue.push(...node.children)
            }
        }
    }

    // 递归深度优先遍历树，收集模糊搜索节点key值，并修改节点颜色
    dfsTitle = (node, searchStr, targetSet) => {
        let index = node.title.indexOf(searchStr)
        if (index > -1) {
            targetSet.add(node.path_id)
            let beforeStr = node.title.substring(0, index);
            let afterStr = node.title.slice(index + searchStr.length);
            node.title =
                <>
                    <span>{beforeStr}</span>
                    <RedSpan>{searchStr}</RedSpan>
                    <span>{afterStr}</span>
                </>
        }
        if (node.children === undefined || node.children === null || node.length === 0) {
            return
        }
        node.children.forEach(childNode => this.dfsTitle(childNode, searchStr, targetSet))
    }
    // 递归深度优先遍历树，获取指定节点所有父节点列表
    dfsSuperNodeList = (node, searchID, targetList, targetIDSet, path) => {
        if (node.ID === searchID) {
            let targetNode = path[path.length - 1]
            if (!targetIDSet.has(targetNode.id)) {
                targetList.push(targetNode)
                targetIDSet.add(targetNode.id)
            }
            return
        }
        if (node.children === undefined || node.children === null || node.length === 0) {
            return
        }
        path.push(node)
        node.children.forEach((childNode) => this.dfsSuperNodeList(childNode, searchID, targetList, targetIDSet, [...path]))
    }
    handleExpand = (expandedKeys) => {
        this.setState({
            treeExpandedKeys: expandedKeys,
            autoExpandParent: false
        })
    }
    handleTreeSelect = (selectedKeys, e) => {
        console.log(selectedKeys)
        console.log(e)
        if (selectedKeys.length > 0) {
            this.setState({
                selectedTreeNode: e.node
            })
        } else {
            this.setState({
                selectedTreeNode: undefined
            })
        }
    }


    // ### 抽屉
    // 新建节点抽屉
    handleNewModeApplyMsgChange = (e) => {
        let txt = e.target.value
        this.setState({
            newNodeApplyMsg: txt
        })
    }
    handleNewNodeDrawerClose = () => {
        this.setState({
            newNodeDrawerOpen: false,
            approvalEmail: undefined,
            newNodeName:"",
            newNodeProductOwner: "",
            newNodeDevelopOwner: "",
            newNodeBusinessOwner: "",
            newNodeApplyMsg: ""
        })
    }
    handleModifyNodeDrawerClose = () => {
        this.setState({
            modifyNodeDrawerOpen: false,
            newNodeName: "",
            newNodeProductOwner: "",
            newNodeDevelopOwner: "",
            newNodeBusinessOwner: "",
            approvalEmail: undefined,
            newNodeApplyMsg: ""
        })
    }
    handleDragNodeDrawerClose = () => {
        this.setState({
            dragNodeDrawerOpen: false,
            approvalEmail: undefined,
            newNodeApplyMsg: "",
            dragNewNodeSelect: undefined,
            dragNewNode:""
        })  
    }
    handleDeleteNodeDrawerClose = () => {
        this.setState({
            deleteNodeDrawerOpen: false,
            approvalEmail: undefined,
            newNodeApplyMsg: ""
        })
    }
    handleNodeTypeSelect = (e) => {
        this.setState({
            newNodeTypeSelect: e.target.value
        })
    }
    handleNewNodeNameChange = (e) => {
        this.setState({
            newNodeName: e.target.value
        })
    }
    handleNewNodeCancel = () => {
        this.handleNewNodeDrawerClose()
    }
    handleModifyNodeCancel = () => {
        this.handleModifyNodeDrawerClose()
    }
    handleDragNodeCancel = () => {
        this.handleDragNodeDrawerClose()
    }
    handleDeleteNodeCancel = () => {
        this.handleDeleteNodeDrawerClose()
    }
    handleNewNodeBusinessOwnerChange = (e) => {
        this.setState({
            newNodeBusinessOwner: e.target.value
        })
    }
    handleNewNodeProductOwnerChange = (e) => {
        this.setState({
            newNodeProductOwner: e.target.value
        })
    }
    handleNewNodeDevelopOwnerChange = (e) => {
        this.setState({
            newNodeDevelopOwner: e.target.value
        })
    }
    dragNodeChange = (newValue) => {
        const nodeInfo = this.state.treeData.find((node) => node.value === newValue);

        if (nodeInfo && nodeInfo.BusinessOwner !== undefined && nodeInfo.BusinessOwner !== "") {
            this.setState({
                approvalEmail: nodeInfo.BusinessOwner
            })
        } else {
            message.warn("该节点没有可用的审批人，请联系运维处理！")
            return
        }
       

        this.setState({
            dragNewNode: newValue,
            dragNewNodeSelect:nodeInfo
        })
    }
    handleNewNodeSubmit = () => {
        if (this.state.newNodeTypeSelect === undefined) {
            message.warn("请先选择节点类型！")
            return
        }
        if (this.state.newNodeName.length < 2) {
            message.warn("节点名称不能小于两个字符！")
            return
        }
        if (this.state.newNodeApplyMsg.length <= 5) {
            message.warn("请完善申请理由，不能少于5个字符。")
            return
        }

        let args = {}
        // 创建子节点
        if (this.state.newNodeTypeSelect === this.state.nodeTypes[0].value) {
            let auditOwner = this.state.selectedTreeNode.DevelopOwner

            if (this.state.selectedTreeNode.Layer === 0) {
                if (this.state.newNodeProductOwner === "") {
                    message.warn("请输入产品负责人邮箱")
                    return
                }
                if (this.state.newNodeDevelopOwner === "") {
                    message.warn("请输入开发负责人邮箱")
                    return
                }
                auditOwner = this.state.selectedTreeNode.BusinessOwner
            }

            let apply_msg = `申请创建 ${this.state.selectedTreeNode.label} 的子节点： ${this.state.newNodeName}。`
            apply_msg = apply_msg + "\n" + this.state.newNodeApplyMsg
            args = {
                parent_id: this.state.selectedTreeNode.ID,
                node_name: this.state.newNodeName,
                apply_msg: apply_msg,
                product_owner:this.state.newNodeProductOwner,
                develop_owner:this.state.newNodeDevelopOwner,
                audit_owner:auditOwner,
            }
            postNewModelChildNode(args).then((resp) => {
                if (resp !== null) {
                    navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
                }
            })
            // 创建同级节点
        } else {
            if (this.state.newNodeBusinessOwner === "") {
                message.warn("请输入业务负责人邮箱")
                return
            }
            let auditOwner = this.state.selectedTreeNode.BusinessOwner

            let apply_msg = `申请创建的业务线节点： ${this.state.newNodeName}。\n`
            apply_msg = this.state.newNodeApplyMsg + "\n" + apply_msg
            args = {
                node_layer: this.state.selectedTreeNode.Layer,
                node_name: this.state.newNodeName,
                apply_msg: apply_msg,
                business_owner:this.state.newNodeBusinessOwner,
                audit_owner:auditOwner,
            }
            postNewModelNode(args).then((resp) => {
                if (resp !== null) {
                    navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
                }
            })
        }
        this.handleNewNodeDrawerClose()
    }

    handleModifyNodeSubmit = () => {
        if (this.state.newNodeName.length < 2) {
            message.warn("节点名称不能小于两个字符！")
            return
        }
        if (this.state.newNodeApplyMsg.length <= 5) {
            message.warn("请完善申请理由，不能少于5个字符。")
            return
        }

        let apply_msg = ``

        if (this.state.selectedTreeNode.Layer === 0) {
            if (this.state.newNodeBusinessOwner === "") {
                message.warn("请输入业务负责人邮箱")
                return
            }
            apply_msg = `申请修改业务层： ${this.state.selectedTreeNode.NodeName} 的节点信息：`
        }
        if (this.state.selectedTreeNode.Layer === 1) {
            if (this.state.newNodeProductOwner === "") {
                message.warn("请输入产品负责人邮箱")
                return
            }
            if (this.state.newNodeDevelopOwner === "") {
                message.warn("请输入开发负责人邮箱")
                return
            }
            apply_msg = `申请修改产品层： ${this.state.selectedTreeNode.NodeName} 的节点信息：`
        }
        if (this.state.selectedTreeNode.Layer === 2) {
            if (this.state.newNodeProductOwner === "") {
                message.warn("请输入产品负责人邮箱")
                return
            }
            if (this.state.newNodeDevelopOwner === "") {
                message.warn("请输入开发负责人邮箱")
                return
            }
            apply_msg = `申请修改最小单元： ${this.state.selectedTreeNode.NodeName} 的节点信息：`
        }

       
       
        if (this.state.selectedTreeNode.NodeName !== this.state.newNodeName){
            apply_msg += `\n节点名称从 ${this.state.selectedTreeNode.NodeName} 修改为 ${this.state.newNodeName}`
        }
        if (this.state.selectedTreeNode.BusinessOwner !== this.state.newNodeBusinessOwner){
            apply_msg += `\n业务负责人从 ${this.state.selectedTreeNode.BusinessOwner} 修改为 ${this.state.newNodeBusinessOwner}`
        }
        if (this.state.selectedTreeNode.ProductOwner !== this.state.newNodeProductOwner){
            apply_msg += `\n产品负责人从 ${this.state.selectedTreeNode.ProductOwner} 修改为 ${this.state.newNodeProductOwner}`
        }
        if (this.state.selectedTreeNode.DevelopOwner !== this.state.newNodeDevelopOwner){
            apply_msg += `\n开发负责人从 ${this.state.selectedTreeNode.DevelopOwner} 修改为 ${this.state.newNodeDevelopOwner}`
        }
        apply_msg += "\n理由：" + this.state.newNodeApplyMsg

        let args = {
            node_id: this.state.selectedTreeNode.ID,
            node_name: this.state.newNodeName,
            apply_msg: apply_msg,
            product_owner:this.state.newNodeProductOwner,
            develop_owner:this.state.newNodeDevelopOwner,
            business_owner:this.state.newNodeBusinessOwner,
            audit_owner:this.state.approvalEmail,
        }
        postModifyModelChildNode(args).then((resp) => {
            if (resp !== null) {
                navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
            }
        })
        this.handleModifyNodeDrawerClose()
    }

    handleDragNodeSubmit = () => {
        if (this.state.newNodeApplyMsg.length <= 5) {
            message.warn("请完善申请理由，不能少于5个字符。")
            return
        }

        if (this.state.dragNewNodeSelect === undefined) {
            message.warn("请选择业务节点")
            return
        }

        let apply_msg =`移动节点：${this.state.selectedTreeNode.NodeName} 至：${this.state.dragNewNodeSelect.NodeName}\n理由：${this.state.newNodeApplyMsg}`

        let args = {
            apply_msg: apply_msg,
            node_id: this.state.selectedTreeNode.ID,
            new_parent_node_id:this.state.dragNewNodeSelect.ID,
            audit_owner:this.state.approvalEmail,
        }
        postDragModelChildNode(args).then((resp) => {
            if (resp !== null) {
                navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
            }
        })
        this.handleDragNodeDrawerClose()
    }

    handleDeleteNodeSubmit = () => {
        if (this.state.newNodeApplyMsg.length <= 5) {
            message.warn("请完善申请理由，不能少于5个字符。")
            return
        }

        let apply_msg =`删除节点：${this.state.selectedTreeNode.NodeName}\n理由：${this.state.newNodeApplyMsg}`

        let args = {
            node_id: this.state.selectedTreeNode.ID,
            apply_msg: apply_msg,
            audit_owner:this.state.approvalEmail,
        }
        postDeleteModelChildNode(args).then((resp) => {
            if (resp !== null) {
                navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
            }
        })
        this.handleDeleteNodeDrawerClose()
    }

    // // 分摊抽屉
    // handleApportionApplyMsgChange = (e) => {
    //     let txt = e.target.value
    //     this.setState({
    //         apportionApllyMsg: txt
    //     })
    // }
    // // 关闭抽屉
    // handelApportionDrawerClose = () => {
    //     this.setState({
    //         apportionDrawerOpen: false
    //     })
    // }
    // 取消抽屉
    // handleApportionCancel = () => {
    //     this.handelApportionDrawerClose()
    // }
    // 提交分摊设置
    // handleApportionSubmit = () => {
    //     if (this.state.apportionApllyMsg.length <= 5) {
    //         message.warn("请完善申请理由，不能少于5个字符。")
    //         return
    //     }
    //     let arg = {}
    //     let changedPath = []
    //     let apply_msg = `申请设置 ${this.state.selectedTreeNode.label} 的分摊比例为：\n`
    //     Object.keys(this.state.apportionInfo).forEach((key) => {
    //         let item = this.state.apportionInfo[key]
    //         if (item.change) {

    //             if (item.auto_weight > 0) {
    //                 changedPath.push({
    //                     parent_id: key,
    //                     child_id: this.state.selectedTreeNode.ID,
    //                     weight: 0,
    //                     auto_weight: item.value
    //                 })
    //                 apply_msg = apply_msg + `${this.state.selectedTreeNode.label} —— ${item.parentName}\t分摊方式：自动分摊\t分摊权重：${item.value}\n`
    //             } else {
    //                 changedPath.push({
    //                     parent_id: key,
    //                     child_id: this.state.selectedTreeNode.ID,
    //                     weight: item.value * 100,
    //                     auto_weight: item.auto_weight
    //                 })
    //                 apply_msg = apply_msg + `${this.state.selectedTreeNode.label} —— ${item.parentName}\t分摊方式：人工指定\t分摊权重：${item.value}\n`
    //             }
    //         }
    //     })
    //     arg["apply_msg"] = this.state.apportionApllyMsg + "\n" + apply_msg
    //     arg["path_list"] = changedPath
    //     postConfApportion(arg).then((resp) => {
    //         if (resp !== null) {
    //             navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
    //         }
    //     })
    //     this.handelApportionDrawerClose()
    // }
    // 处理分摊方式变更
    // handleApportionWay(node, e) {
    //     let apportionInfo = this.state.apportionInfo
    //     apportionInfo[node.id].auto_weight = e.target.value
    //     apportionInfo[node.id].change = true
    //     this.setState({
    //         apportionInfo: apportionInfo
    //     })
    // }
    // 处理分摊比例变更
    // handleApportionRatio(node, value) {
    //     let apportionInfo = this.state.apportionInfo
    //     apportionInfo[node.id].value = value
    //     apportionInfo[node.id].change = true
    //     this.setState({
    //         apportionInfo: apportionInfo
    //     })
    // }
    // ApportionFormater = (node, value) => {
    //     if (parseInt(this.state.apportionInfo[node.id].auto_weight) === 0) {
    //         return `${value}%`
    //     } else {
    //         return value
    //     }
    // }
    // ApportionParser = (node, value) => {
    //     if (parseInt(this.state.apportionInfo[node.id].auto_weight) === 0) {
    //         return value.replace('%', '')
    //     } else {
    //         return value
    //     }
    // }


    // ### 操作按钮
    checkTreeNodeSelect = () => {
        if (this.state.selectedTreeNode === undefined) {
            message.warn("请选择一个节点！")
            return false
        }
        return true
    }
    // 打开新建节点抽屉
    handleNewNodeDrawerClick = () => {
        if (!this.checkTreeNodeSelect()) {
            return
        }
        if (this.state.selectedTreeNode.Layer >= 2) {
            message.warn("无法创建子节点，业务树只有三层！", 3)
            return
        }
        if (this.state.selectedTreeNode.Layer > 0) {
            let nodeTypes = this.state.nodeTypes
            // 设置节点选择子节点为可选，同级节点为不可选
            nodeTypes[0].disabled = false
            nodeTypes[1].disabled = true
            // 设置默认选择子节点
            this.setState({
                newNodeTypeSelect: this.state.nodeTypes[0].value,
                nodeTypes: nodeTypes
            })
        } else {
            let nodeTypes = this.state.nodeTypes
            // 设置节点选择子节点为可选，同级节点为不可选
            nodeTypes[0].disabled = false
            nodeTypes[1].disabled = false
            // 设置默认选择子节点
            this.setState({
                newNodeTypeSelect: undefined,
                nodeTypes: nodeTypes
            })
        }

        // 检查选中节点的审批人
        let approvalEmail;
        if (this.state.selectedTreeNode.Layer === 0) {
            approvalEmail = this.state.selectedTreeNode.BusinessOwner;
        } else if (this.state.selectedTreeNode.Layer === 1) {
            approvalEmail = this.state.selectedTreeNode.DevelopOwner;
        }

        if (!approvalEmail) {
            message.warn("该节点没有可用的审批人，请联系运维处理！");
            return;
        }

        this.setState({
            newNodeDrawerOpen: true,
            approvalEmail: approvalEmail
        })
    }

    // 打开移动节点抽屉
    handleDragNodeDrawerClick = () => {
        if (!this.checkTreeNodeSelect()) {
            return
        }
        if (this.state.selectedTreeNode.Layer !== 1) {
            message.warn("只容许移动产品层级", 3)
            return
        }

        this.setState({
            dragNodeDrawerOpen: true,
        })
    }

    // 打开修改节点抽屉
    handleModifyNodeDrawerClick = () => {
        if (!this.checkTreeNodeSelect()) {
            return
        }
        // 检查选中节点的审批人
        let approvalEmail;
        if (this.state.selectedTreeNode.Layer === 0) {
            approvalEmail = this.state.selectedTreeNode.BusinessOwner;
        } else if (this.state.selectedTreeNode.Layer === 1) {
            approvalEmail = this.state.selectedTreeNode.DevelopOwner;
        } else if (this.state.selectedTreeNode.Layer === 2) {
            // 如果是第三层，找到第二层的开发负责人
            let parentNode = this.findParentNode(this.state.selectedTreeNode);
            approvalEmail = parentNode.DevelopOwner;
        }
        if (!approvalEmail) {
            message.warn("该节点没有可用的审批人，请联系运维处理！");
            return;
        }

        let selectedNode = this.state.selectedTreeNode;
        this.setState({
            modifyNodeDrawerOpen: true,
            newNodeName: selectedNode.NodeName,
            newNodeProductOwner: selectedNode.ProductOwner,
            newNodeDevelopOwner: selectedNode.DevelopOwner,
            newNodeBusinessOwner: selectedNode.BusinessOwner,
        })

        this.setState({
            modifyNodeDrawerOpen: true,
            approvalEmail: approvalEmail
        })
    }

     // 打开删除节点抽屉
     handleDeleteNodeDrawerClick = () => {
        if (!this.checkTreeNodeSelect()) {
            return
        }
        // 检查选中节点的审批人
        let approvalEmail;
        if (this.state.selectedTreeNode.Layer === 0) {
            approvalEmail = this.state.selectedTreeNode.BusinessOwner;
        } else if (this.state.selectedTreeNode.Layer === 1) {
            approvalEmail = this.state.selectedTreeNode.DevelopOwner;
        } else if (this.state.selectedTreeNode.Layer === 2) {
            // 如果是第三层，找到第二层的开发负责人
            let parentNode = this.findParentNode(this.state.selectedTreeNode);
            approvalEmail = parentNode.DevelopOwner;
        }
        if (!approvalEmail) {
            message.warn("该节点没有可用的审批人，请联系运维处理！");
            return;
        }

        this.setState({
            deleteNodeDrawerOpen: true,
            approvalEmail: approvalEmail
        })
    }

    // 用于找到选中节点的父节点
    findParentNode = (node) => {
        let treeData = this.state.srcTreeData;
        for (let i = 0; i < treeData.length; i++) {
            let parentNode = treeData[i];
            if (parentNode.Children) {
                for (let j = 0; j < parentNode.Children.length; j++) {
                    let childNode = parentNode.Children[j];
                    if (childNode.ID === node.ParentID) {
                        return childNode; // 返回直接父节点
                    }
                }
            }
        }
        return null;
    }
    // 打开分摊设置抽屉
    // handleApportionDrawerClick = () => {
    //     if (!this.checkTreeNodeSelect()) {
    //         return
    //     }
    //     if (this.state.selectedTreeNode.Layer <= 0) {
    //         message.warn("顶层节点不需要设置分摊！", 3)
    //         return
    //     }
    //     // 获取其所有父节点
    //     let targetList = []
    //     let targetIDSet = new Set()
    //     this.state.srcTreeData.forEach(
    //         (childNode) => this.dfsSuperNodeList(childNode, this.state.selectedTreeNode.ID, targetList, targetIDSet, [])
    //     )
    //     // 设置的分摊方式
    //     let apportionInfo = this.state.apportionInfo
    //     targetList.forEach((item) => {
    //         apportionInfo[item.id] = {
    //             auto_weight: this.apportionWays[1].value,
    //             value: 100,
    //             change: false,
    //             parentName: item.label
    //         }
    //     })
    //     // 获取其所有父路径
    //     postModelParentPath({ child_id: this.state.selectedTreeNode.ID }).then((resp) => {
    //         resp.parent_paths.forEach((item) => {
    //             if (item.auto_weight > 0) {
    //                 apportionInfo[item.parent_id].auto_weight = this.apportionWays[1].value
    //                 apportionInfo[item.parent_id].value = item.auto_weight
    //             } else {
    //                 apportionInfo[item.parent_id].auto_weight = this.apportionWays[0].value
    //                 apportionInfo[item.parent_id].value = item.weight / 100
    //             }
    //         })
    //         this.setState({
    //             selectedSupperNodeList: targetList,
    //             apportionInfo: apportionInfo,
    //             apportionDrawerOpen: true
    //         })
    //     })
    // }

    componentDidMount = () => {
        // 获取领导邮箱
        // requestLeaderEmail({}).then(resp => {
        //     if (resp !== null) {
        //         this.setState({
        //             leaderEmail: resp.email
        //         })
        //     }
        // })
        // 获取运维领导邮箱
        postOpsLeader({}).then(resp => {
            if (resp !== null) {
                this.setState({
                    opsLeaderEmail: resp.ops_leader
                })
            }
        })

        // // 获取业务树
        // postModelTree({ "model_name": "cost", "attach_path_id": true }).then((resp) => {
        //     let srcTreeData = resp.data
        //     this.bfsAddProps(srcTreeData)
        //     let treeData = JSON.parse(JSON.stringify(srcTreeData))
        //     // this.bfsAddProps(treeData)
        //     this.setState({
        //         srcTreeData: srcTreeData,
        //         treeData: treeData,
        //     })
        // })
         // 成本模型树
         postModelTree2({ "model_name": "businessModel" }).then((resp) => {
            let srcTreeData = resp.data
            this.bfsAddProps(srcTreeData)
            let treeData = JSON.parse(JSON.stringify(srcTreeData))
            this.setState({
                srcTreeData: srcTreeData,
                treeData: treeData
            })
        })
    }

    render() {
        return (
            <SideContent>
                <ShadeForm>
                    <OrderTitle>
                        业务树管理
                    </OrderTitle>
                    <ModelTabs
                        defaultActiveKey="costModel"
                        tabBarExtraContent=
                        {<OrderRow>
                            <SpaceButton type="primary" onClick={this.handleNewNodeDrawerClick} >
                                <PlusCircleOutlined />
                                新增节点
                            </SpaceButton>
                            <SpaceButton type="primary" onClick={this.handleModifyNodeDrawerClick}>
                                <EditOutlined />
                                修改节点
                            </SpaceButton>
                            <SpaceButton type="primary" onClick={this.handleDragNodeDrawerClick} >
                                <DragOutlined />
                                移动产品
                            </SpaceButton>
                            <SpaceButton type="primary" danger onClick={this.handleDeleteNodeDrawerClick}>
                                <DeleteOutlined />
                                删除节点
                            </SpaceButton>
                            {/* <SpaceButton type="primary" onClick={this.handleApportionDrawerClick}>
                                分摊
                                <MenuFoldOutlined />
                            </SpaceButton> */}
                            {/* <SpaceButton type="primary" disabled={true} >挂载<MenuFoldOutlined /></SpaceButton> */}
                        </OrderRow>}
                    >
                        <Tabs.TabPane tab="业务树" key="costModel">
                            <ModelSearch
                                placeholder="回车键可模糊搜索节点。选择相应节点后，点击页面右上角相应操作按钮进行节点新增、分摊设置和资源挂载等操作。"
                                value={this.state.searchStr}
                                onChange={this.handleSearchChange}
                                onSearch={this.handleSearch}
                            />
                            <ModelTree
                                treeData={this.state.treeData}
                                onExpand={this.handleExpand}
                                expandedKeys={this.state.treeExpandedKeys}
                                autoExpandParent={this.state.autoExpandParent}
                                onSelect={this.handleTreeSelect}
                            />
                        </Tabs.TabPane>
                    </ModelTabs>
                    {/* 新增节点抽屉 */}
                    <Drawer
                        title={
                            <TitleSpan>
                                新增节点（当前节点：{this.state.selectedTreeNode === undefined ? "" : this.state.selectedTreeNode.label}）
                            </TitleSpan>
                        }
                        width={800}
                        onClose={this.handleNewNodeDrawerClose}
                        open={this.state.newNodeDrawerOpen}
                        bodyStyle={{ paddingBottom: 80 }}
                        extra={
                            <Space>
                                <Button onClick={this.handleNewNodeCancel}>取消</Button>
                                <Button onClick={this.handleNewNodeSubmit} type="primary">
                                    提交
                                </Button>
                            </Space>
                        }
                    >
                        <OrderWideRow>
                            <OrderCol>
                                <span>审批人员：</span>
                                <Tag color="#108ee9" icon={<UserOutlined />}>
                                    {this.state.approvalEmail}，{this.state.opsLeaderEmail}
                                </Tag>
                            </OrderCol>
                        </OrderWideRow>
                        <OrderWideRow>
                            <OrderHalfCol>
                                <span>节点名称：</span>
                                <Input value={this.state.newNodeName} onChange={this.handleNewNodeNameChange} />
                            </OrderHalfCol>
                            <OrderHalfCenterCol>
                                <span>节点类型：</span>
                                <Radio.Group
                                    options={this.state.nodeTypes}
                                    value={this.state.newNodeTypeSelect}
                                    optionType="button"
                                    buttonStyle="solid"
                                    onChange={this.handleNodeTypeSelect}
                                />
                            </OrderHalfCenterCol>
                        </OrderWideRow>
                        {this.state.newNodeTypeSelect === "sub" && this.state.selectedTreeNode?.Layer === 0 && (
                            <>
                                <OrderWideRow>
                                    <OrderCol>
                                        <span>产品负责人：</span>
                                        <Input value={this.state.newNodeProductOwner} placeholder="请输入产品负责人" onChange={this.handleNewNodeProductOwnerChange} />
                                    </OrderCol>
                                    <OrderCol>
                                        <span>开发负责人：</span>
                                        <Input value={this.state.newNodeDevelopOwner} placeholder="请输入开发负责人" onChange={this.handleNewNodeDevelopOwnerChange} />
                                    </OrderCol>
                                </OrderWideRow>
                            </>
                        )}
                        {this.state.newNodeTypeSelect === "same" && this.state.selectedTreeNode?.Layer === 0 && (
                            <>
                                <OrderWideRow>
                                    <OrderCol>
                                        <span>业务负责人：</span>
                                        <Input value={this.state.newNodeBusinessOwner} placeholder="请输入业务负责人" onChange={this.handleNewNodeBusinessOwnerChange} />
                                    </OrderCol>
                                </OrderWideRow>
                            </>
                        )}
                        <OrderWideRow >
                            <OrderCol style={{ alignItems: "flex-start" }}>
                                申请理由：
                                <Input.TextArea
                                    value={this.state.newNodeApplyMsg}
                                    onChange={this.handleNewModeApplyMsgChange}
                                    style={{ minHeight: "8vw" }}
                                />
                            </OrderCol>
                        </OrderWideRow>
                        {/* <Divider><BoldSpan>说明</BoldSpan></Divider>
                        <DescP>
                            &emsp;&emsp;
                            成本模型主要用于财务记账，通过将服务器等资源挂载到成本模型节点上实现。该模型的节点最多只有三级。第一级为业务线，第二级为产品，第三级为财务记账单元。<br />
                            &emsp;&emsp;
                            节点名称若包含英文字母会自动转换成小写字母，不允许使用连接符 · 和 - 。
                        </DescP> */}
                    </Drawer>
                    <Drawer
                        title={
                            <TitleSpan>
                                修改节点（当前节点：{this.state.selectedTreeNode === undefined ? "" : this.state.selectedTreeNode.label}）
                            </TitleSpan>
                        }
                        width={800}
                        onClose={this.handleModifyNodeDrawerClose}
                        open={this.state.modifyNodeDrawerOpen}
                        bodyStyle={{ paddingBottom: 80 }}
                        extra={
                            <Space>
                                <Button onClick={this.handleModifyNodeCancel}>取消</Button>
                                <Button onClick={this.handleModifyNodeSubmit} type="primary">
                                    提交
                                </Button>
                            </Space>
                        }
                    >
                        <OrderWideRow>
                            <OrderCol>
                                <span>审批人员：</span>
                                <Tag color="#108ee9" icon={<UserOutlined />}>
                                    {this.state.approvalEmail}，{this.state.opsLeaderEmail}
                                </Tag>
                            </OrderCol>
                        </OrderWideRow>
                        <OrderWideRow>
                            <OrderHalfCol>
                                <span>节点名称：</span>
                                <Input value={this.state.newNodeName} onChange={this.handleNewNodeNameChange} />
                            </OrderHalfCol>
                        </OrderWideRow>
                        {this.state.selectedTreeNode?.Layer === 0 && (
                             <>
                                <OrderWideRow>
                                    <OrderHalfCol>
                                        <span>业务负责人：</span>
                                        <Input value={this.state.newNodeBusinessOwner} placeholder="请输入业务负责人" onChange={this.handleNewNodeBusinessOwnerChange} />
                                    </OrderHalfCol>
                                </OrderWideRow>
                            </>
                        )}
                        {this.state.selectedTreeNode?.Layer === 1 && (
                            <>
                                <OrderWideRow>
                                    <OrderCol>
                                        <span>产品负责人：</span>
                                        <Input value={this.state.newNodeProductOwner} placeholder="请输入产品负责人" onChange={this.handleNewNodeProductOwnerChange} />
                                    </OrderCol>
                                    <OrderCol>
                                        <span>开发负责人：</span>
                                        <Input value={this.state.newNodeDevelopOwner} placeholder="请输入开发负责人" onChange={this.handleNewNodeDevelopOwnerChange} />
                                    </OrderCol>
                                </OrderWideRow>
                            </>
                        )}
                        <OrderWideRow >
                            <OrderCol style={{ alignItems: "flex-start" }}>
                                申请理由：
                                <Input.TextArea
                                    value={this.state.newNodeApplyMsg}
                                    onChange={this.handleNewModeApplyMsgChange}
                                    style={{ minHeight: "8vw" }}
                                />
                            </OrderCol>
                        </OrderWideRow>
                    </Drawer>
                    <Drawer
                        title={
                            <TitleSpan>
                                移动节点（当前选中节点：{this.state.selectedTreeNode === undefined ? "" : this.state.selectedTreeNode.label}）
                            </TitleSpan>
                        }
                        width={800}
                        onClose={this.handleDragNodeDrawerClose}
                        open={this.state.dragNodeDrawerOpen}
                        bodyStyle={{ paddingBottom: 80 }}
                        extra={
                            <Space>
                                <Button onClick={this.handleDragNodeCancel}>取消</Button>
                                <Button onClick={this.handleDragNodeSubmit} type="primary">
                                    提交
                                </Button>
                            </Space>
                        }
                    >
                        <OrderWideRow>
                            <OrderCol>
                                <span>审批人员：</span>
                                <Tag color="#108ee9" icon={<UserOutlined />}>
                                    {this.state.approvalEmail}，{this.state.opsLeaderEmail}
                                </Tag>
                            </OrderCol>
                        </OrderWideRow>
                        <OrderWideRow>
                            <OrderCol>
                                移至业务：
                                <TreeSelect
                                    showSearch
                                    style={{
                                        width: '100%',
                                    }}
                                    value={this.state.dragNewNode}
                                    dropdownStyle={{
                                        maxHeight: 400,
                                        overflow: 'auto',
                                    }}
                                    placeholder="请输入或选择业务"
                                    allowClear
                                    onChange={this.dragNodeChange}
                                    treeData={this.state.treeData.map(item => ({ ...item, children: null }))}
                                    />
                                </OrderCol>
                        </OrderWideRow>
                        <OrderWideRow >
                            <OrderCol style={{ alignItems: "flex-start" }}>
                                申请理由：
                                <Input.TextArea
                                    value={this.state.newNodeApplyMsg}
                                    onChange={this.handleNewModeApplyMsgChange}
                                    style={{ minHeight: "8vw" }}
                                />
                            </OrderCol>
                        </OrderWideRow>
                    </Drawer>
                    <Drawer
                        title={
                            <TitleSpan>
                                删除节点（当前节点：{this.state.selectedTreeNode === undefined ? "" : this.state.selectedTreeNode.label}）
                            </TitleSpan>
                        }
                        width={800}
                        onClose={this.handleDeleteNodeDrawerClose}
                        open={this.state.deleteNodeDrawerOpen}
                        bodyStyle={{ paddingBottom: 80 }}
                        extra={
                            <Space>
                                <Button onClick={this.handleDeleteNodeCancel}>取消</Button>
                                <Button onClick={this.handleDeleteNodeSubmit} type="primary">
                                    提交
                                </Button>
                            </Space>
                        }
                    >
                        <OrderWideRow>
                            <OrderCol>
                                <span>审批人员：</span>
                                <Tag color="#108ee9" icon={<UserOutlined />}>
                                    {this.state.approvalEmail}，{this.state.opsLeaderEmail}
                                </Tag>
                            </OrderCol>
                        </OrderWideRow>
                        <OrderWideRow >
                            <OrderCol style={{ alignItems: "flex-start" }}>
                                申请理由：
                                <Input.TextArea
                                    value={this.state.newNodeApplyMsg}
                                    onChange={this.handleNewModeApplyMsgChange}
                                    style={{ minHeight: "8vw" }}
                                />
                            </OrderCol>
                        </OrderWideRow>
                    </Drawer>
                    {/* 分摊操作抽屉 */}
                    {/* <Drawer
                        title={
                            <TitleSpan>
                                分摊设置（当前节点：{this.state.selectedTreeNode === undefined ? "" : this.state.selectedTreeNode.label}）
                            </TitleSpan>
                        }
                        width={800}
                        onClose={this.handelApportionDrawerClose}
                        open={this.state.apportionDrawerOpen}
                        bodyStyle={{ paddingBottom: 80 }}
                        extra={
                            <Space>
                                <Button onClick={this.handleApportionCancel}>
                                    取消
                                </Button>
                                <Button onClick={this.handleApportionSubmit} type="primary">
                                    提交
                                </Button>
                            </Space>
                        }
                    >
                        <OrderWideRow>
                            <OrderCol>
                                <span>审批人员：</span>
                                <Tag color="#108ee9" icon={<UserOutlined />}>
                                    {this.state.leaderEmail}，{this.state.opsLeaderEmail}
                                </Tag>
                            </OrderCol>
                        </OrderWideRow>
                        <OrderWideRow >
                            <OrderCol style={{ alignItems: "flex-start" }}>
                                申请理由：
                                <Input.TextArea
                                    value={this.state.apportionApllyMsg}
                                    onChange={this.handleApportionApplyMsgChange}
                                    style={{ minHeight: "8vw" }}
                                />
                            </OrderCol>
                        </OrderWideRow>
                        <Divider />
                        {
                            this.state.selectedSupperNodeList.map((supperNode) =>
                                <OrderRow key={supperNode.key}>
                                    <OrderCol>
                                        <BlueFillSpan>{this.state.selectedTreeNode.label}</BlueFillSpan>
                                        <BlueLine />
                                        <BlueFillSpan>{supperNode.label}</BlueFillSpan>
                                    </OrderCol>
                                    <OrderCol>
                                        <Space>
                                            <div>
                                                <span>分摊方式：</span>
                                                <Radio.Group key={supperNode.key}
                                                    options={this.apportionWays}
                                                    value={this.state.apportionInfo[supperNode.id].auto_weight}
                                                    optionType="button"
                                                    buttonStyle="solid"
                                                    onChange={this.handleApportionWay.bind(this, supperNode)}
                                                />
                                            </div>
                                            <InputNumber
                                                value={this.state.apportionInfo[supperNode.id].value}
                                                min={0}
                                                max={100}
                                                formatter={this.ApportionFormater.bind(this, supperNode)}
                                                parser={this.ApportionParser.bind(this, supperNode)}
                                                key={supperNode.key}
                                                onChange={this.handleApportionRatio.bind(this, supperNode)}
                                            />
                                        </Space>
                                    </OrderCol>
                                </OrderRow>
                            )
                        }

                        <Divider><BoldSpan>相关概念说明</BoldSpan></Divider>
                        <DescP>
                            &emsp;&emsp;
                            设置分摊比例前，需要了解成本模型的分摊逻辑，请点击
                            <a href="https://cheetahfun.feishu.cn/docx/LwCCdey2Kobq0axgQl6cEaYXnYg" target="_blank" rel="noreferrer">&nbsp;🔗&nbsp;</a>
                            了解具体的分摊计算逻辑。<br />
                            &emsp;&emsp;
                            此页面用来设置节点间路径的分摊权重，分摊方式有两种，分别描述如下：<br />
                            &emsp;&emsp;
                            <RedSpan>人工比例</RedSpan>：指定一个百分比，小数位最多两位。如输入框填入30.25%，则表示该路径的分摊权重为30.25%。<br />
                            &emsp;&emsp;
                            <RedSpan>自动均分</RedSpan>：这是一种自动计算分摊权重的分摊方式，系统会自动计算出该路径的分摊权重。<br />
                            &emsp;&emsp;
                            举例：节点 a 有 A、B、C 3个父节点，a-A 路径分摊方式设置为 <RedSpan>人工比例</RedSpan> ，数值为 <RedSpan>40%</RedSpan>。
                            a-B、a-C 路径分摊方式都为 <RedSpan>自动均分</RedSpan> ，自动分摊权重分别设置为 <RedSpan>1，2</RedSpan> 。
                            则 a-B 自动分摊后的权重为 <RedSpan>(1-40%)*1/(1+2)=20%</RedSpan> ，a-B 自动分摊后的权重为 <RedSpan>(1-40%)*2/(1+2)=40%</RedSpan> 。
                        </DescP>
                    </Drawer> */}
                </ShadeForm>
            </SideContent>
        )
    }
}
export default withRouter(CmdbModelManger);