import React, { Component } from 'react';
import { Table, message, Input, Button, Form, DatePicker, Select } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import OrderPagination from '../../../components/OrderPagination';
import { requestCdnFlushHistory } from '../../../request/api';
import styled from 'styled-components';
import moment from 'moment';

const { Option } = Select;

// 样式组件
const PageContainer = styled.div`
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
`;

const SearchFormWrapper = styled.div`
  margin-bottom: 16px;
`;

const TableSection = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1px solid rgba(229,231,235,0.6);
  margin: 0 16px;

  .ant-table {
    border-radius: 12px;
  }

  .ant-table-thead > tr > th {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 600;
    color: #262626;
    padding: 12px 16px;
  }

  .ant-table-tbody > tr > td {
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;
  }

  .ant-table-tbody > tr:hover > td {
    background: #fafafa;
  }

  .ant-table-tbody > tr:last-child > td {
    border-bottom: none;
  }
`;

const TableHeader = styled.div`
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;



const DomainCode = styled.code`
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #666;
`;

const ResourceUrl = styled.div`
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #666;
  cursor: pointer;

  &:hover {
    color: #1890ff;
  }
`;

const FailureReason = styled.span`
  background: #fff2f0;
  color: #ff4d4f;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
`;

// CDN搜索表单组件 - 使用与工单搜索相同的样式
const CdnSearchFormContainer = styled.div`
  padding: 16px 20px 12px 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1px solid rgba(229,231,235,0.6);
  margin: 16px 16px 12px 16px;

  .header-layout {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .search-layout {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
  }

  .filters-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-width: 0;
  }

  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px 24px;
    align-items: center;
  }

  .actions-section {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    flex-shrink: 0;
    margin-left: 32px;
  }

  .ant-form-item {
    margin: 0;
    display: flex;
    align-items: center;
  }

  .ant-form-item-label {
    white-space: nowrap;
    label {
      color: #374151;
      font-weight: 600;
    }
  }

  .ant-input,
  .ant-picker,
  .ant-select-selector {
    border-radius: 10px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }

  .ant-input:focus,
  .ant-input-focused,
  .ant-picker-focused,
  .ant-select-focused .ant-select-selector {
    border-color: #2563eb !important;
    box-shadow: none !important;
  }

  .reset-btn {
    background: white !important;
    border: 1px solid #d1d5db !important;
    color: #374151 !important;
    border-radius: 8px;
    height: 40px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    &:hover, &:focus, &:active {
      border-color: #9ca3af !important;
      color: #374151 !important;
      background: #f9fafb !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .search-btn {
    background: #2563eb !important;
    border-color: #2563eb !important;
    color: white !important;
    border-radius: 8px;
    height: 40px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    &:hover, &:focus, &:active {
      background: #1d4ed8 !important;
      border-color: #1d4ed8 !important;
      color: white !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
`;

// CDN搜索表单组件
class CdnSearchForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      domain: '',
      user: '',
      flush_type: '',
      start_time: null,
      end_time: null,
    };
  }

  // 处理字段变化
  handleFieldChange = (field, value) => {
    this.setState({ [field]: value }, () => {
      // 通知父组件表单变化
      if (this.props.onFormChange) {
        this.props.onFormChange(this.state);
      }
    });
  };

  // 处理日期变化
  handleDateChange = (field, date) => {
    const dateStr = date ? date.format('YYYY-MM-DD HH:mm:ss') : '';
    this.setState({ [field]: date }, () => {
      // 通知父组件表单变化，传递格式化的日期字符串
      const formData = {
        ...this.state,
        [`${field}_str`]: dateStr
      };
      if (this.props.onFormChange) {
        this.props.onFormChange(formData);
      }
    });
  };

  // 处理搜索
  handleSearch = () => {
    if (this.props.onSearch) {
      this.props.onSearch();
    }
  };

  // 处理重置
  handleReset = () => {
    this.setState({
      domain: '',
      user: '',
      flush_type: '',
      start_time: null,
      end_time: null,
    }, () => {
      if (this.props.onFormChange) {
        this.props.onFormChange(this.state);
      }
      if (this.props.onReset) {
        this.props.onReset();
      }
    });
  };

  render() {
    const { loading } = this.props;
    const { domain, user, flush_type, start_time, end_time } = this.state;

    return (
      <CdnSearchFormContainer>
        {/* 标题栏 */}
        <div className="header-layout">
          <div style={{ fontSize: 18, fontWeight: 700, color: '#111827' }}>CDN刷新查询</div>
        </div>

        {/* 搜索布局 */}
        <div className="search-layout">
          {/* 左侧筛选条件区域 */}
          <div className="filters-section">
            {/* 第一行：域名前缀、刷新用户、刷新类型 */}
            <div className="filter-row">
              <Form.Item label="域名前缀">
                <Input
                  placeholder="请输入域名前缀"
                  value={domain}
                  onChange={(e) => this.handleFieldChange('domain', e.target.value)}
                  style={{ width: 200 }}
                  allowClear
                />
              </Form.Item>

              <Form.Item label="刷新用户">
                <Input
                  placeholder="请输入用户名"
                  value={user}
                  onChange={(e) => this.handleFieldChange('user', e.target.value)}
                  style={{ width: 200 }}
                  allowClear
                />
              </Form.Item>

              <Form.Item label="刷新类型">
                <Select
                  placeholder="选择刷新类型"
                  value={flush_type}
                  onChange={(value) => this.handleFieldChange('flush_type', value)}
                  style={{ width: 200 }}
                  allowClear
                >
                  <Option value="file">文件刷新</Option>
                  <Option value="directory">目录刷新</Option>
                </Select>
              </Form.Item>
            </div>

            {/* 第二行：开始时间、结束时间 */}
            <div className="filter-row">
              <Form.Item label="开始时间">
                <DatePicker
                  showTime
                  placeholder="选择开始时间"
                  value={start_time}
                  onChange={(date) => this.handleDateChange('start_time', date)}
                  style={{ width: 200 }}
                />
              </Form.Item>

              <Form.Item label="结束时间">
                <DatePicker
                  showTime
                  placeholder="选择结束时间"
                  value={end_time}
                  onChange={(date) => this.handleDateChange('end_time', date)}
                  style={{ width: 200 }}
                />
              </Form.Item>
            </div>
          </div>

          {/* 右侧操作按钮区域 */}
          <div className="actions-section">
            <Button
              className="search-btn"
              onClick={this.handleSearch}
              loading={loading}
              icon={<SearchOutlined />}
            >
              搜索
            </Button>

            <Button
              className="reset-btn"
              onClick={this.handleReset}
              icon={<ReloadOutlined />}
            >
              重置
            </Button>
          </div>
        </div>
      </CdnSearchFormContainer>
    );
  }
}

class CdnFlushHistory extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // 搜索参数
      pendingFilters: {},
      appliedFilters: {},

      // 数据状态
      loading: false,
      dataSource: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },

      // 统计数据
      statistics: {
        total: 0,
        success: 0,
        failure: 0,
        successRate: 0,
      },
    };
  }

  componentDidMount() {
    this.fetchCdnFlushHistory();
  }

  // 获取CDN刷新历史数据
  fetchCdnFlushHistory = async (params = {}) => {
    this.setState({ loading: true });

    try {
      const { appliedFilters, pagination } = this.state;

      // 构建请求参数
      const requestParams = {
        common: {
          user_email: "<EMAIL>",
          user_name: "谭明宇"
        },
        pageNumber: params.page || pagination.current,
        pageSize: params.page_size || pagination.pageSize,
        domain: appliedFilters.domain || '',
        user: appliedFilters.user || '',
        flush_type: appliedFilters.flush_type || 0, // 0表示不过滤
        start_time: appliedFilters.start_time || '',
        end_time: appliedFilters.end_time || '',
        ...params
      };

      console.log('CDN刷新历史查询参数:', requestParams);

      const response = await requestCdnFlushHistory(requestParams);

      if (response && response.resp_common.ret === 0) {
        const { vo, total } = response;

        // 计算统计数据
        const statistics = this.calculateStatistics(vo || [], total || 0);

        this.setState({
          dataSource: vo || [],
          pagination: {
            ...this.state.pagination,
            current: requestParams.pageNumber,
            pageSize: requestParams.pageSize,
            total: total || 0,
          },
          statistics,
        });
      } else {
        message.error(response?.resp_common?.msg || '获取CDN刷新历史失败');
        this.setState({
          dataSource: [],
          pagination: { ...this.state.pagination, total: 0 },
          statistics: { total: 0, success: 0, failure: 0, successRate: 0 },
        });
      }
    } catch (error) {
      console.error('获取CDN刷新历史失败:', error);
      message.error('获取CDN刷新历史失败');
      this.setState({
        dataSource: [],
        pagination: { ...this.state.pagination, total: 0 },
        statistics: { total: 0, success: 0, failure: 0, successRate: 0 },
      });
    } finally {
      this.setState({ loading: false });
    }
  };

  // 计算统计数据
  calculateStatistics = (data, total) => {
    const success = data.filter(item => item.status === 1).length;
    const failure = data.filter(item => item.status === 0).length;
    const successRate = total > 0 ? Math.round((success / total) * 100) : 0;

    return {
      total,
      success,
      failure,
      successRate,
    };
  };

  // 处理搜索表单变化
  handleSearchFormChange = (filters) => {
    // 转换日期格式
    const processedFilters = {
      domain: filters.domain || '',
      user: filters.user || '',
      flush_type: filters.flush_type || '',
      start_time: filters.start_time_str || '',
      end_time: filters.end_time_str || '',
    };
    this.setState({ pendingFilters: processedFilters });
  };

  // 处理搜索
  handleSearch = (additionalParams = {}) => {
    const { pendingFilters, pagination } = this.state;

    // 转换搜索参数格式
    const searchParams = {
      domain: pendingFilters.domain || '',
      user: pendingFilters.user || '',
      flush_type: this.convertFlushType(pendingFilters.flush_type),
      start_time: pendingFilters.start_time || '',
      end_time: pendingFilters.end_time || '',
      page: 1, // 搜索时重置到第一页
      page_size: pagination.pageSize,
      ...additionalParams
    };

    // 更新应用的筛选条件
    this.setState({
      appliedFilters: { ...pendingFilters, ...additionalParams },
      pendingFilters: { ...pendingFilters, ...additionalParams },
      pagination: { ...pagination, current: 1 }
    });

    // 执行搜索
    this.fetchCdnFlushHistory(searchParams);
  };

  // 处理重置
  handleReset = () => {
    this.setState({
      pendingFilters: {},
      appliedFilters: {},
      pagination: { ...this.state.pagination, current: 1 }
    });

    // 重新获取数据
    this.fetchCdnFlushHistory({ page: 1, page_size: this.state.pagination.pageSize });
  };

  // 转换刷新类型
  convertFlushType = (type) => {
    if (!type) return 0; // 不过滤
    if (type === 'file') return 1; // 文件
    if (type === 'directory') return 2; // 目录
    return 0;
  };

  // 处理表格变化（分页）
  handleTableChange = (pagination) => {
    const { appliedFilters } = this.state;

    const searchParams = {
      domain: appliedFilters.domain || '',
      user: appliedFilters.user || '',
      flush_type: this.convertFlushType(appliedFilters.flush_type),
      start_time: appliedFilters.start_time || '',
      end_time: appliedFilters.end_time || '',
      page: pagination.current,
      page_size: pagination.pageSize,
    };

    this.setState({
      pagination: {
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: this.state.pagination.total,
      }
    });

    this.fetchCdnFlushHistory(searchParams);
  };

  // 处理分页变化
  handlePageChange = (page) => {
    this.handleTableChange({
      current: page,
      pageSize: this.state.pagination.pageSize
    });
  };

  // 处理页面大小变化
  handlePageSizeChange = (current, size) => {
    this.handleTableChange({
      current: 1,
      pageSize: size
    });
  };

  // 渲染用户信息
  renderUser = (user) => {
    return <span>{user}</span>;
  };

  // 渲染状态
  renderStatus = (status, record) => {
    if (status === 1) {
      return (
        <span style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: '4px',
          padding: '2px 8px',
          borderRadius: '12px',
          fontSize: '12px',
          fontWeight: '500',
          color: '#065F46',
          backgroundColor: '#D1FAE5',
          border: '1px solid #A7F3D0'
        }}>
          <CheckCircleOutlined style={{ fontSize: '12px' }} />
          成功
        </span>
      );
    } else {
      return (
        <span style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: '4px',
          padding: '2px 8px',
          borderRadius: '12px',
          fontSize: '12px',
          fontWeight: '500',
          color: '#DC2626',
          backgroundColor: '#FEE2E2',
          border: '1px solid #FECACA'
        }}>
          <CloseCircleOutlined style={{ fontSize: '12px' }} />
          失败
        </span>
      );
    }
  };

  // 渲染刷新类型
  renderFlushType = (type) => {
    const isFile = type === '文件';

    return (
      <span style={{
        display: 'inline-flex',
        alignItems: 'center',
        padding: '2px 8px',
        borderRadius: '12px',
        fontSize: '12px',
        fontWeight: '500',
        color: isFile ? '#1D4ED8' : '#7C3AED',
        backgroundColor: isFile ? '#DBEAFE' : '#EDE9FE',
        border: isFile ? '1px solid #93C5FD' : '1px solid #C4B5FD'
      }}>
        {type}
      </span>
    );
  };

  // 渲染失败原因
  renderFailureReason = (reason) => {
    if (!reason) {
      return <span style={{ color: '#999' }}>-</span>;
    }
    return <FailureReason>{reason}</FailureReason>;
  };

  // 表格列定义
  getColumns = () => {
    return [
      {
        title: '用户',
        dataIndex: 'user',
        key: 'user',
        width: 120,
        render: this.renderUser,
      },
      {
        title: '域名',
        dataIndex: 'domain',
        key: 'domain',
        width: 150,
        render: (domain) => <DomainCode>{domain}</DomainCode>,
      },
      {
        title: '刷新资源',
        dataIndex: 'flush_resource',
        key: 'flush_resource',
        width: 300,
        render: (url) => (
          <ResourceUrl title={url} onClick={() => window.open(url, '_blank')}>
            {url}
          </ResourceUrl>
        ),
      },
      {
        title: '类型',
        dataIndex: 'flush_type',
        key: 'flush_type',
        width: 80,
        render: this.renderFlushType,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        render: this.renderStatus,
      },
      {
        title: '创建时间',
        dataIndex: 'ctime',
        key: 'ctime',
        width: 160,
        render: (time) => (
          <span style={{ fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace', fontSize: '12px', color: '#666' }}>
            {time}
          </span>
        ),
      },
      {
        title: '失败原因',
        dataIndex: 'reason',
        key: 'reason',
        width: 150,
        render: this.renderFailureReason,
      },
    ];
  };

  render() {
    const { pendingFilters, appliedFilters, loading, dataSource, pagination, statistics } = this.state;
    const columns = this.getColumns();

    return (
      <PageContainer>
        {/* 搜索表单 - 使用共享的SearchForm组件 */}
        <SearchFormWrapper>
          <CdnSearchForm
            pendingFilters={pendingFilters}
            appliedFilters={appliedFilters}
            onFormChange={this.handleSearchFormChange}
            onSearch={this.handleSearch}
            onReset={this.handleReset}
            loading={loading}
          />
        </SearchFormWrapper>

        {/* CDN刷新历史表格 */}
        <TableSection>
          <TableHeader>
            <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
              {/* 标题和统计数据 */}
              <span style={{ fontSize: 16, fontWeight: 600, color: '#111827' }}>CDN刷新历史</span>
              <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                <span style={{ fontSize: 12, color: '#6B7280' }}>总记录数:</span>
                <span style={{ fontSize: 12, fontWeight: 700, color: '#111827', background: '#F3F4F6', padding: '2px 8px', borderRadius: 9999 }}>
                  {statistics.total}
                </span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                <span style={{ fontSize: 12, color: '#6B7280' }}>刷新成功:</span>
                <span style={{ fontSize: 12, fontWeight: 700, color: '#065F46', background: '#D1FAE5', padding: '2px 8px', borderRadius: 9999 }}>
                  {statistics.success}
                </span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                <span style={{ fontSize: 12, color: '#6B7280' }}>刷新失败:</span>
                <span style={{ fontSize: 12, fontWeight: 700, color: '#DC2626', background: '#FEE2E2', padding: '2px 8px', borderRadius: 9999 }}>
                  {statistics.failure}
                </span>
              </div>
              {/* 暂时注释掉成功率显示 */}
              {/* <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                <span style={{ fontSize: 12, color: '#6B7280' }}>成功率:</span>
                <span style={{ fontSize: 12, fontWeight: 700, color: '#1D4ED8', background: '#DBEAFE', padding: '2px 8px', borderRadius: 9999 }}>
                  {statistics.successRate}%
                </span>
              </div> */}
            </div>
          </TableHeader>

          <Table
            columns={columns}
            dataSource={dataSource}
            rowKey={(record, index) => record.id || index}
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
            size="middle"
            footer={() => (
              <OrderPagination
                total={pagination.total}
                current={pagination.current}
                pageSize={pagination.pageSize}
                pageSizeOptions={['10', '20', '30', '50', '100']}
                onPageChange={this.handlePageChange}
                onPageSizeChange={this.handlePageSizeChange}
                showTotal={false}
              />
            )}
          />
        </TableSection>
      </PageContainer>
    );
  }
}

export default CdnFlushHistory;


