import { Table, Card, Empty } from "antd";

export default function CdnFlushHistory() {
  const columns = [
    { title: "任务ID", dataIndex: "id", key: "id" },
    { title: "类型", dataIndex: "type", key: "type" },
    { title: "状态", dataIndex: "status", key: "status" },
    { title: "时间", dataIndex: "time", key: "time" },
  ];

  const dataSource = [];

  return (
    <Card title="CDN刷新历史">
      {dataSource.length === 0 ? (
        <Empty description="暂无数据，后续将接入后端接口" />
      ) : (
        <Table rowKey="id" columns={columns} dataSource={dataSource} pagination={{ pageSize: 10 }} />
      )}
    </Card>
  );
}


