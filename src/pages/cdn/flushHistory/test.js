// 简单的功能测试文件
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import CdnFlushHistory from './index';

// Mock antd components
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    info: jest.fn(),
    error: jest.fn(),
  },
}));

describe('CdnFlushHistory', () => {
  test('renders search form with correct fields', () => {
    render(<CdnFlushHistory />);
    
    // 检查搜索表单字段
    expect(screen.getByPlaceholderText('请输入域名前缀')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('请输入用户名')).toBeInTheDocument();
    expect(screen.getByText('选择刷新类型')).toBeInTheDocument();
    
    // 检查按钮
    expect(screen.getByText('重置')).toBeInTheDocument();
    expect(screen.getByText('搜索')).toBeInTheDocument();
  });

  test('renders table with mock data', () => {
    render(<CdnFlushHistory />);
    
    // 检查表格标题
    expect(screen.getByText('CDN刷新历史')).toBeInTheDocument();
    
    // 检查模拟数据
    expect(screen.getByText('张三')).toBeInTheDocument();
    expect(screen.getByText('example.com')).toBeInTheDocument();
    expect(screen.getByText('成功')).toBeInTheDocument();
  });

  test('reset button clears form fields', () => {
    render(<CdnFlushHistory />);
    
    const domainInput = screen.getByPlaceholderText('请输入域名前缀');
    const userInput = screen.getByPlaceholderText('请输入用户名');
    const resetButton = screen.getByText('重置');
    
    // 输入一些值
    fireEvent.change(domainInput, { target: { value: 'test.com' } });
    fireEvent.change(userInput, { target: { value: '测试用户' } });
    
    // 点击重置
    fireEvent.click(resetButton);
    
    // 验证字段被清空
    expect(domainInput.value).toBe('');
    expect(userInput.value).toBe('');
  });

  test('search button triggers search function', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    render(<CdnFlushHistory />);
    
    const searchButton = screen.getByText('搜索');
    fireEvent.click(searchButton);
    
    expect(consoleSpy).toHaveBeenCalledWith('搜索参数:', expect.any(Object));
    
    consoleSpy.mockRestore();
  });

  test('displays correct statistics', () => {
    render(<CdnFlushHistory />);
    
    // 检查统计信息
    expect(screen.getByText(/总记录数: 5/)).toBeInTheDocument();
    expect(screen.getByText(/刷新成功: 4/)).toBeInTheDocument();
    expect(screen.getByText(/刷新失败: 1/)).toBeInTheDocument();
    expect(screen.getByText(/成功率: 80%/)).toBeInTheDocument();
  });
});
