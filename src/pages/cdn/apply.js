import { Component } from "react";
import { Input, Segmented, Button, Popover, Select, Tag } from "antd";
import { QuestionCircleOutlined, UserOutlined } from "@ant-design/icons";
import styled from "styled-components";

import withRouter from "@/util/withRouter";

import { OrderEndCol, OrderRow, OrderSubmit, OrderTitle, ShadeForm, SideContent } from "@/common/styleLayout";
import { requestHwCdnApply, requestLeaderEmail, requestOpsMemberDutySpectrum } from "@/request/api";

import navigateModal from "@/util/navigateModal"
import { DoingOrderUrlSuffix } from "@/global"

const { TextArea } = Input;

// css-js start ↓↓↓
const CdnApplyForm = styled(ShadeForm)`
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
`
const OriginIPRow = styled(OrderRow)`
    align-items: flex-start;
`
const CdnSegmented = styled(Segmented)`
    width: 100%;
    height: 100%;
`
const CdnSpan = styled.span`
    min-width: 100px;
`
const CdnOpsAuditDiv = styled.div`
    width: 55%;
    display:flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
`
// css-js end   ↑↑↑

class Cdn extends Component {
    bussinessTypeBook = (
        < div >
            <p>
                网站加速：
                网站加速文件下载加速点播加速全站加速适用于有加速需求的网站，包括门户网站、电商平台、资讯APP、UGC应用（User Generated Content用户原创内容）等。
            </p>
            <p>
                文件加速：
                适用于使用http/https文件下载业务的网站、下载工具、游戏客户端、APP商店等。
            </p>
            <p>
                点播加速：
                网站加速文件下载加速点播加速全站加速适用于需要加速音频或视频的网站。例如在线教育类网站、在线视频分享网站、互联网电视点播平台、音乐视频点播APP等。
            </p>
            <p>
                全站加速：
                适用于各行业动静态内容混合，含较多动态资源请求（如asp、jsp、php等格式的文件）的网站。。
            </p>
            <p>
                <a href="https://support.huaweicloud.com/productdesc-cdn/cdn_01_0067.html">如何选择业务类型</a>
            </p>
        </div>
    )
    originTypeBook = (
        < div >
            <p>
                OBS桶域名为华为云的OBS存储桶服务所提供的域名。
            </p>
        </div>
    )
    bussinessType = [
        { label: "网站加速", value: "web" },
        { label: "文件加速", value: "download" },
        { label: "点播加速", value: "video" },
        { label: "全站加速", value: "wholeSite" }
    ]
    serviceArea = [
        { label: "中国大陆", value: "mainland_china" },
        { label: "大陆境外", value: "outside_mainland_china" },
        { label: "全球", value: "global" }
    ]
    originType = [
        { label: "源站IP", value: "ipaddr" },
        { label: "源站域名", value: "domain" },
        { label: "OBS桶域名", value: "obs_bucket" }
    ]
    state = {
        bussinessTypeSelected: this.bussinessType[0].value,
        serviceAreaSelected: this.serviceArea[0].value,
        originTypeSelected: this.originType[0].value,
        originTypeSelectedName: this.originType[0].label,
        targetSpeedUpDomain: "",
        targetOriginIP: "",
        leaderEmail: undefined,
        opsAuditEmail: [],
        opsAuditEmailSelected: undefined,
        applyMsg: ""
    }
    componentDidMount() {
        requestLeaderEmail({}).then((data) => {
            this.setState({
                leaderEmail: data.email,
            });
        })
        requestOpsMemberDutySpectrum({}).then((data) => {
            this.setState({
                opsAuditEmail: data.duty_member_map["域名与DNS"].map((item) => { return { label: item, value: item } }),
            });
        })
    }
    handleSpeedUpDomainChange = (e) => {
        this.setState({
            targetSpeedUpDomain: e.target.value
        })
    }
    handleOriginIPChange = (e) => {
        this.setState({
            targetOriginIP: e.target.value
        })
    }
    handleApplyMsgChange = (e) => {
        this.setState({
            applyMsg: e.target.value
        })
    }
    handleBusinessTypeChange = (value) => {
        this.setState({
            bussinessTypeSelected: value
        })
    }
    handleServiceAreaChange = (value) => {
        this.setState({
            serviceAreaSelected: value
        })
    }
    handleOriginTypeChange = (value) => {
        let originTypeSelectedName
        // eslint-disable-next-line
        this.originType.map((item) => {
            if (item.value === value) {
                originTypeSelectedName = item.label
            }
        })
        this.setState({
            originTypeSelected: value,
            originTypeSelectedName: originTypeSelectedName
        })
    }
    handleOpsAuditEmailChange = (value) => {
        this.setState({
            opsAuditEmailSelected: value
        })
    }
    handleReset = () => {
        this.setState({
            bussinessTypeSelected: this.bussinessType[0].value,
            serviceAreaSelected: this.serviceArea[0].value,
            originTypeSelected: this.originType[0].value,
            originTypeSelectedName: this.originType[0].label,
            targetSpeedUpDomain: "",
            targetOriginIP: "",
            opsAuditEmailSelected: undefined,
            applyMsg: ""
        })
    }
    handleSubmit = () => {
        let args = {
            domain_name: this.state.targetSpeedUpDomain,
            business_type: this.state.bussinessTypeSelected,
            ip_or_domain: this.state.targetOriginIP,
            domain_service_area: this.state.serviceAreaSelected,
            origin_type: this.state.originTypeSelected,
            ops_audit_email: this.state.opsAuditEmailSelected,
            apply_msg: this.state.applyMsg

        }
        requestHwCdnApply(args).then((data) => {
            if (data !== null) {
                navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
            }
        })
    }
    render() {
        return (
            <SideContent>
                <CdnApplyForm>
                    <OrderTitle>CDN 申请</OrderTitle>
                    <OrderRow>
                        <CdnOpsAuditDiv>
                            <CdnSpan>运维审批：</CdnSpan>
                            <Select
                                placeholder="运维审批人员"
                                options={this.state.opsAuditEmail}
                                value={this.state.opsAuditEmailSelected}
                                onChange={this.handleOpsAuditEmailChange}
                            />
                        </CdnOpsAuditDiv>
                        <OrderEndCol>
                            <span>领导审批：</span><Tag color="#108ee9" icon={<UserOutlined />}>{this.state.leaderEmail}</Tag>
                        </OrderEndCol>
                    </OrderRow>
                    <OrderRow>
                        <CdnSpan>加速域名：</CdnSpan>
                        <Input
                            placeholder="请输入要加速的域名，如 www.example.com"
                            value={this.state.targetSpeedUpDomain}
                            onChange={this.handleSpeedUpDomainChange}
                        />
                    </OrderRow>
                    <OrderRow>
                        <CdnSpan>
                            <Popover
                                placement="bottomRight"
                                content={this.bussinessTypeBook}
                            >
                                业务类型&nbsp;<QuestionCircleOutlined />：
                            </Popover>
                        </CdnSpan>
                        <CdnSegmented
                            size="middle"
                            block
                            options={this.bussinessType}
                            defaultValue={this.bussinessType[0].value}
                            value={this.state.bussinessTypeSelected}
                            onChange={this.handleBusinessTypeChange}
                        />
                    </OrderRow>
                    <OrderRow>
                        <CdnSpan>服务范围：</CdnSpan>
                        <CdnSegmented
                            block
                            size="middle"
                            options={this.serviceArea}
                            defaultValue={this.serviceArea[0].value}
                            value={this.state.serviceAreaSelected}
                            onChange={this.handleServiceAreaChange}
                        />
                    </OrderRow>
                    <OrderRow>
                        <CdnSpan>
                            <Popover
                                placement="right"
                                content={this.originTypeBook}>
                                源站类型&nbsp;<QuestionCircleOutlined />：
                            </Popover>
                        </CdnSpan>
                        <CdnSegmented
                            block
                            size="middle"
                            options={this.originType}
                            defaultValue={this.originType[0].value}
                            value={this.state.originTypeSelected}
                            onChange={this.handleOriginTypeChange}
                        />
                    </OrderRow>
                    <OriginIPRow>
                        <CdnSpan>
                            {this.state.originTypeSelectedName}：
                        </CdnSpan>
                        <TextArea
                            placeholder={"请输入" + this.state.originTypeSelectedName + "，多个" + this.state.originTypeSelectedName + "请使用英文逗号,分割。"}
                            style={{ minHeight: "3vw" }}
                            value={this.state.targetOriginIP}
                            onChange={this.handleOriginIPChange}
                        />
                    </OriginIPRow>
                    <OriginIPRow>
                        <CdnSpan>申请理由：</CdnSpan>
                        <TextArea
                            placeholder="请简单描述申请CDN理由"
                            style={{ minHeight: "8vw" }}
                            value={this.state.applyMsg}
                            onChange={this.handleApplyMsgChange}
                        />
                    </OriginIPRow>
                    <OrderSubmit>
                        <Button type="primary" onClick={this.handleSubmit}>
                            提交
                        </Button>
                        <Button type="dashed" onClick={this.handleReset}>
                            重置
                        </Button>
                    </OrderSubmit>
                </CdnApplyForm>
            </SideContent >
        )
    }
}


export default withRouter(Cdn)