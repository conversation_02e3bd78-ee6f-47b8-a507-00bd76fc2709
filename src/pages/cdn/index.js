import { Button, Input, message, Select } from "antd";
import { Component } from "react";
import styled from "styled-components";
import { requestCdnFlush } from "@/request/api";
import { SideContent, ShadeForm, OrderTitle, OrderRow } from "@/common/styleLayout";


const { Option } = Select;
const { TextArea } = Input;


// css-js start ↓↓↓
const CDNFlushForm = styled(ShadeForm)`
    display: flex;
    width: 55%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    justify-content: flex-start;
`
const CDNFlushRow = styled(OrderRow)`
    align-items: flex-start;   
`
const CDNFlushSelect = styled(Select)`
    height: 32px;  
    flex-basis: 180px;
`
const CDNFlushText = styled(TextArea)`
    flex-grow: 1;
    margin: 0px 5px 0px 5px;
`
const CDNFlushButton = styled(Button)`
    flex-basis: 100px;
`
// css-js end   ↑↑↑

export default class CdnFlush extends Component {
    state = {
        selectType: undefined,
        urls: undefined,
        disable:false,
    }
    handleTypeChange = (value) => {
        this.setState({
            selectType: value
        })
    }
    handleUrlsChange = (e) => {
        let urlStr = e.target.value
        let urls = urlStr.split("\n")
        this.setState({
            urls: urls
        })
    }
    handleSubmit = () => {
        if (this.state.selectType === undefined) {
            message.error("cdn url 类型不能为空", 1.5)
            return
        }
        if (this.state.urls === undefined) {
            message.error("cdn url 不能为空", 1.5)
            return
        }
        let args = {
            type: this.state.selectType,
            urls: this.state.urls
        }
        this.setState({
            disable:true
        })
        requestCdnFlush(args).then((data) => {
            if (data !== null) {
                message.info("刷新成功", 1.5)
            } else {
                message.error(data.resp_common.msg, 1.5)
            }
        })
        setTimeout(()=>{
            this.setState({
                disable:false
            })
        },1000);

    }
    render() {
        return (
            <SideContent>
                <CDNFlushForm>
                    <OrderTitle>CDN刷新</OrderTitle>
                    <CDNFlushRow >
                        <CDNFlushSelect
                            className="cdn-flush-option"
                            value={this.state.selectType}
                            onChange={this.handleTypeChange}
                            placeholder="请选择类型"
                        >
                            <Option value={1}>文件</Option>
                            <Option value={2}>目录</Option>
                        </CDNFlushSelect>
                        <CDNFlushText rows={15} placeholder="请输入url，多个url请使用回车换行分割" onChange={this.handleUrlsChange} />
                        <CDNFlushButton type="primary" onClick={this.handleSubmit} disabled={this.state.disable}>刷新</CDNFlushButton>
                    </CDNFlushRow>
                </CDNFlushForm>
            </SideContent>
        )
    }
}