import { Component } from "react";
import { Button, Input, Radio, InputNumber, message, Tag,Divider,Tooltip } from "antd";
import styled from "styled-components";
import {
  UserOutlined
} from "@ant-design/icons";
import {
  requestJumpServerAccountApply,
  requestServerOwnerEmail,
} from "@/request/api";
import withRouter from "@/util/withRouter";
import navigateModal from "@/util/navigateModal"
import { DoingOrderUrlSuffix } from "@/global"
import { OrderSubmit, OrderRow, OrderTitle, ShadeForm, SideContent } from "@/common/styleLayout";

const accountOption = [
  { label: "Root权限", value: 2 },
  { label: "普通权限", value: 1 }
];

const ColumnStartOrderRow = styled(OrderRow)`
  align-items: flex-start;
`
const RowStartOrderRow = styled(OrderRow)`
  justify-content: flex-start;
`

class ServerAccountOrderForm extends Component {
  state = {
    server_str_ip: "",
    server_ip: undefined,
    impower_type: 2,
    day_num: 1,
    apply_msg: "",
    owner_email: undefined,
  };
  // 处理授权天数
  handleDayNumChange = (value) => {
    this.setState({
      day_num: value,
    });
  };
  // 处理授权方式
  handleChangeImpowerType = (e) => {
    this.setState({
      impower_type: e.target.value,
    });
  };
  // 处理申请理由
  handleApplyReasonChange = (e) => {
    this.setState({
      apply_msg: e.target.value,
    });
  };
  // 处理服务器ip
  handleServerIPChange = (e) => {
    // ip输入框内容不变或为空则不处理
    if (e.target.value === "") {
      return;
    }
    if (this.state.server_str_ip === e.target.value) {
      return;
    }
    this.setState({
      server_str_ip: e.target.value,
    });
    var server_ip = e.target.value.split(",");
    var args = {
      ip: server_ip,
    };
    requestServerOwnerEmail(args).then((data) => {
      if (data !==null){
        if (data.illegal.length > 0) {
          this.setState({
            server_ip: undefined,
            owner_email: undefined,
          });
          message.error(
            data.illegal.join(",") + " 非法，未查到这些服务器的拥有人！",
            1
          );
        } else {
          this.setState({
            server_ip: server_ip,
            owner_email: data.owner_email.join(","),
          });
        }
      }
    });
  };
  // 处理表单提交
  handleSubmit = () => {
    if (this.state.owner_email === undefined) {
      message.error("请输入有效IP！！！");
      return;
    }
    const args = {
      server_ip: this.state.server_ip,
      impower_type: this.state.impower_type,
      day_num: this.state.day_num,
      apply_msg: this.state.apply_msg,
    };
    requestJumpServerAccountApply(args).then((data) => {
      if (data!==null){
        navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
      }
    });
  };
  // 处理取消跳转
  handleCancel = () => {
    this.props.navigate("/new-order");
  };
  render() {
    return (
      <SideContent>
        <ShadeForm>
          <OrderTitle>服务器账号申请</OrderTitle>
          <OrderRow>
            <div>
              账号类型：
              <Radio.Group
                className="server-account-apply-line-option"
                onChange={this.handleChangeImpowerType}
                options={accountOption}
                defaultValue={2}
                optionType="button"
              />
            </div>
            <div>
              权限时长：
              <InputNumber min={1} defaultValue={1} max={3} onChange={this.handleDayNumChange} />
              &nbsp;&nbsp;&nbsp;天
            </div>
          </OrderRow>
          <RowStartOrderRow>
            <span>审批人员：</span>
            <Tag color="#108ee9" icon={<UserOutlined />} >
              {this.state.owner_email}
            </Tag>
          </RowStartOrderRow>
          <ColumnStartOrderRow>
            <span>服务器IP：</span>
            <Input.TextArea
              style={{ minHeight: "5vw" }}
              onBlur={this.handleServerIPChange}
              placeholder="请输入服务器ip，多个ip请使用英语逗号分割，填写完将显示审批人。"
            />
          </ColumnStartOrderRow>
          <ColumnStartOrderRow>
            <span>申请理由：</span>
            <Input.TextArea style={{ minHeight: "18vw" }} onChange={this.handleApplyReasonChange} />
          </ColumnStartOrderRow>
          <OrderSubmit>
            <Button type="primary" onClick={this.handleSubmit}>
              提交
            </Button>
            <Button type="dashed" onClick={this.handleCancel}>
              取消
            </Button>
          </OrderSubmit>
          <Divider dashed />
          <Tooltip title="prompt text">
            <span style={{color: 'red'}}>审计机器只能申请一天!</span>
          </Tooltip>
        </ShadeForm>
       
      </SideContent>
    );
  }
}

export default withRouter(ServerAccountOrderForm);
