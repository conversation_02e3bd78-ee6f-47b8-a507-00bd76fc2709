//导入我们封装好的axios
// import service from "./index";
import service from "./index";
import { cmdbService } from "./index";

// 后端鉴权
export const requestAuth = (info) => service.post("/auth/oa/sid-token", info);

// ********************** 审批流转 **********************
// 页面审批
export const requestFlowApproval = (info) => service.post("/order/approval", info);
// 审批人变更

// ********************** 工单信息 **********************
export const requestFlowAuditTurn = (info) => service.post("/order/flow-audit/turn", info);
// 获取工单详情
export const requestOrderDetail = (info) => service.post("/order/detail", info);
// 获取我的历史工单
export const requestMyDoneOrder = (params) => {
	// 确保参数包含 filter_by_role 字段
	const processedParams = {
		...params,
		filter_by_role: params.filter_by_role || [], // 为迭代三预留筛选参数
	};
	return service.post("/order/my-done", processedParams);
};
// 获取正在进行中的工单
export const requestMyDoingOrder = (params) => {
	// 确保参数包含filter_by_role字段
	const processedParams = {
		...params,
		filter_by_role: params.filter_by_role || [], // 为迭代三预留筛选参数
	};
	return service.post("/order/my-doing", processedParams);
};

// 获取当前用户工单列表
export const requestMyAuditOrderWithSearch = async (params) =>
{
  try {
    // 将参数转换为后端期望的格式
    const processedParams = {
      page: Number(params.pageNum) || 1,          // 数字类型，从1开始
      page_size: Number(params.pageSize) || 10,   // 数字类型
      order_id: params.orderId || '',             // 字符串类型
      title: params.title || '',                  // 字符串类型
      applied_start_date: params.appliedStartDate || null, // 直接传递字符串格式日期
      applied_end_date: params.appliedEndDate || null,     // 直接传递字符串格式日期
      order_types: params.orderTypes || [],       // 数组类型
      filter_by_role: params.filter_by_role || [], // 工单状态标签筛选参数
    };

    const response = await service.post("/order/my-audit", processedParams);
    return response; // service拦截器已经返回了正确的数据结构，不需要再次访问.data
  } catch (error) {
    console.error('API request for my audit orders failed:', error);
    // 统一错误处理，返回一个匹配新接口结构的错误响应
    return {
      resp_common: {
        ret: -1,
        msg: error.response?.data?.message || '网络或服务器错误',
        request_id: 'error'
      },
      total: 0,
      orders: [],
    };
  }
};

/**
 * 获取工单类型列表（迭代三使用）
 * @returns {Promise<Object>} 工单类型列表
 */
export const requestOrderTypes = async (info = {}) =>
{
  try {
    const response = await service.post("/order/types", info);
    return response.data;
  } catch (error) {
    console.error('API request for order types failed:', error);
    return {
      code: error.response?.status || -1,
      message: error.response?.data?.message || '获取工单类型失败',
      data: [],
    };
  }
};

export const requestUpdateOrderRemark = (info) => service.post("/order/update/remark", info);

// ********************** 金山云接口 **********************
// 获取金山云城市
export const requestKsRegion = (info) => service.post("/kingsoft/region", info);
// 获取金山云地区
export const requestKsZone = (info) =>
	service.post("/kingsoft/region/zone", info);
// 获取金山云镜像
export const requestKsImage = (info) => service.post("/kingsoft/image", info);
// 获取金山云硬件规格配置
export const requestKsHardwareSpecType = (info) =>
	service.post("/kingsoft/server/hardware-spec-type", info);
// 获取金山云硬件规格配置
export const requestKsHardwareSpec = (info) =>
	service.post("/kingsoft/server/hardware-spec", info);
// 提交金山云服务表单
export const requestKsServerPrice = (info) =>
	service.post("/kingsoft/server/price", info);
// 提交金山云服务表单
export const requestKsServerApply = (info) =>
	service.post("/order/server/ks/apply", info);

// ********************** jumpserver **********************
// 获取非法服务器ip和拥有者email
export const requestServerOwnerEmail = (info) =>
	service.post("/jumpserver/getServerOwner", info);
// 提交跳板机权限申请表单
export const requestJumpServerAccountApply = (info) =>
	service.post("/order/server/jump/impower", info);

// 获取运维人员
export const requestOpsMember = (info) => service.post("/ops/member", info);
// 获取运维人员-职责映射关系
export const requestOpsMemberDutySpectrum = (info) => service.post("/ops/member/duty-spectrum", info);
// 获取dba
export const requestOpsDBA = (info) => service.post("/ops/dba", info);
// 领导邮箱
export const requestLeaderEmail = (info) => service.post("/commoninfo/leader-email", info);
export const requestSuperLeaderEmails = (info) => service.post("/commoninfo/super-leader-email", info);
export const requestOnlySuperLeaderEmails = (info) => service.post("/commoninfo/only-super-leader-email", info);
export const requestUserLeaderEmail = (info) => service.post("/commoninfo/user-leader-email", info);
export const requestThreeLevelLeaderEmails = (info) => service.post("/commoninfo/three-level-leader-email", info)
// 校验抄送人邮箱
export const validateUser = async (email) => {
  try {
    const response = await service.post("/order/validate-user", { email });
    if (response && response.resp_common && response.resp_common.ret === 0) {
      return Promise.resolve(response);
    }
    // 业务失败，返回具体的错误信息
    return Promise.reject(response?.resp_common?.msg || '校验失败');
  } catch (error) {
    // 网络或代码异常
    return Promise.reject('校验服务异常，请稍后重试');
  }
};

// 获取三级审批固定抄送人（后端接口 /commoninfo/three-fixed-cc）
// 与后端proto: GetThreeLevelFixedCcResp { resp_common, repeated FixedCc fixed_cc }
// 缓存三级审批固定抄送人结果，避免重复请求
let __threeFixedCcCache = null;
export const requestThreeFixedCc = async (info = {}) => {
  if (__threeFixedCcCache) {
    return Promise.resolve(__threeFixedCcCache);
  }
  const resp = await service.post("/commoninfo/three-fixed-cc", info);
  // 仅在后端返回成功时缓存
  if (resp && resp.resp_common && resp.resp_common.ret === 0) {
    __threeFixedCcCache = resp;
  }
  return resp;
};

// 提交通用工单
export const requestCommonOrder = (info) => service.post("/order/common", info);


// ********************** sql 审计 **********************
// 获取数据库实例
export const requestDbInstanceInfo = (info) => service.post("/sqlaudit/db-instance", info);
// 校验SQL语句
export const requestSqlCheck = (info) => service.post("/sqlaudit/sql/check", info);
// 校验sql附件
export const requestSqlFileCheck = (info) => service.post("/sqlaudit/sql-attachment/check", info);
// 校验sql附件
export const requestSqlFileCheckResult = (info) => service.post("/sqlaudit/sql-attachment/check/result", info);

// 获取数据库实例
export const requestAuditDbInstance = (info) => service.post("/sqlaudit/audit-db-instance", info);


// ********************** cdn **********************
export const requestCdnFlush = (info) => service.post("/order/cdn/flush", info);
export const requestCdnFlushHistory = (info) => service.post("/cdn/flush/list", info);


// ********************** 腾讯云 **********************
// 获取cos权限
export const requestTxCamCosAuth = (info) => service.post("/tengxun/cam/cos/auth", info);
export const requestTxDNSApply = (info) => service.post("/domain/resolve", info);
export const requestTxDomainCheck = (info) => service.post("/tengxun/dns/seconday/check", info);
// 地区
export const requestTxRegion = (info) => service.post("/tengxun/region", info);
// 可用区
export const requestTxZone = (info) => service.post("/tengxun/zone", info);
// 服务器可售规格
export const requestTxServerFamilyTypeConf = (info) => service.post("/tengxun/server/family-type-conf", info);
// 操作系统镜像
export const requestTxOsImage = (info) => service.post("/tengxun/os-image", info);
// 系统盘配额
export const requestTxSysDisk = (info) => service.post("/tengxun/disk/sys", info);
// 数据盘配额
export const requestTxDataDisk = (info) => service.post("/tengxun/disk/data", info);
// 服务器一年价格预估
export const requestServerOneYearPrice = (info) => service.post("/tengxun/server/one-year-price", info);
// 获取mysql规格
export const postTxMysqlConfs = (info) => service.post("/tengxun/db/product", info);
// 获取mysql价格
export const postTxMysqlPrice = (info) => service.post("/tengxun/db/price", info);

// 获取redis规格
export const postTxRedisConfs = (info) => service.post("/tengxun/rds/product", info);
// 获取redis价格
export const postTxRedisPrice = (info) => service.post("/tengxun/rds/price", info);

// ********************** 华为云 **********************
export const requestHwCdnApply = (info) => service.post("/cdn/domain/create", info);

// ********************** 多云 **********************
export const postCloudServerSellConf = (info) => service.post("/cloudvendor/server/sell-conf", info);
export const postCloudDiskSellConf = (info) => service.post("/cloudvendor/disk/sell-conf", info);
export const postCloudImageConf = (info) => service.post("/cloudvendor/image/config", info);
export const postCloudServerPriceOrder = (info) => service.post("/cloudvendor/server/year-price", info);
export const postBuyCloudServer = (info) => service.post("/order/cloud/server/buy", info);
export const postResourceDelete = (info) => service.post("/order/resouce/delete", info);
export const postResourceDeleteConfirm = (info) => service.post("/recyclebin/confirm", info);
export const postCloudMysqlSellConf = (info) => service.post("/cloudvendor/mysql/sell-conf", info);
export const postCloudRedisSellConf = (info) => service.post("/cloudvendor/redis/sell-conf", info);
export const postCloudMysqlPriceOrder = (info) => service.post("/cloudvendor/mysql/year-price", info);
export const postCloudRedisPriceOrder = (info) => service.post("/cloudvendor/redis/year-price", info);
export const postBuyCloudMysql = (info) => service.post("/order/cloud/mysql/buy", info);
export const postBuyCloudRedis = (info) => service.post("/order/cloud/redis/buy", info);

export const postTokenApply = (info) => service.post("/order/token/apply", info);

// ********************** token **********************
export const requestNewOrRefreshLongTermToken = (info) => service.post("/auth/long-term-token/new-refresh", info);
export const requestViewLongTermToken = (info) => service.post("/auth/long-term-token/view", info);

// ********************** cmdb **********************
export const postModelTree = (info) => cmdbService("/model/tree/nested-array-map", info);
export const postModelTree2 = (info) => cmdbService("/model/multi-branch-tree", info);
export const postModelParentPath = (info) => cmdbService("/model/tree/parent-path", info);
export const postNewModelNode = (info) => service.post("/order/model/new-node", info);
export const postNewModelChildNode = (info) => service.post("/order/model/new-child-node", info);
export const postModifyModelChildNode = (info) => service.post("/order/model/modify-node", info);
export const postDeleteModelChildNode = (info) => service.post("/order/model/delete-node", info);
export const postDragModelChildNode = (info) => service.post("/order/model/drag-node", info);
export const postConfApportion = (info) => service.post("/order/model/cost/apportion-conf", info);
export const postResourceCommon = (info) => cmdbService("/resource/common", info);


// ********************** 运维 **********************
export const postOpsLeader = (info) => service.post("/ops/leader", info);

// ********************** 证书 **********************
export const requestUpdateCert = (info) => service.post("/ops/upload/cert", info);
// ********************** 域名 **********************
export const postBuyDomain = (info) => service.post("/order/domain/buy", info);

// ********************** 资源变更 **********************
export const requestUpdateCMDBServiceOwner = (info) => service.post("/order/cmdb/resource/update-service-owner", info);

// ********************** 评论功能 **********************

/**
 * 根据用户姓名生成头像文字
 * @param {string} name - 用户姓名
 * @returns {string} 头像文字
 */
const generateAvatarText = (name) => {
  if (!name || !name.trim()) {
    return '未'; // 未知用户的默认头像
  }
  
  const trimmedName = name.trim();
  if (trimmedName.length === 2) {
    return trimmedName; // 两个字显示全名
  } else if (trimmedName.length >= 3) {
    return trimmedName.slice(-2); // 三个字及以上显示后两个字
  } else {
    return trimmedName; // 一个字显示全名
  }
};

/**
 * 将后端评论数据转换为前端格式
 * @param {Object} backendComment - 后端评论对象
 * @returns {Object} 前端评论对象
 */
const transformCommentData = (backendComment) => {
  // 处理时间戳转换
  let createdAt;
  if (backendComment.created_at) {
    // 如果是字符串，转为数字
    const timestamp = typeof backendComment.created_at === 'string' ? 
      parseInt(backendComment.created_at, 10) : 
      backendComment.created_at;
    
    // Unix时间戳转换为ISO字符串（时间戳单位是秒，需要乘以1000转为毫秒）
    if (timestamp && !isNaN(timestamp)) {
      createdAt = new Date(timestamp * 1000).toISOString();
    } else {
      // 如果时间戳无效，使用当前时间
      createdAt = new Date().toISOString();
      console.warn('无效的时间戳:', backendComment.created_at);
    }
  } else {
    createdAt = new Date().toISOString();
  }

  return {
    id: backendComment.comment_id || backendComment.id,
    content: backendComment.text_content || backendComment.content || '',
    created_at: createdAt,
    author: {
      id: backendComment.creator_open_id || 'unknown',
      name: backendComment.creator_name || '未知用户',
      avatar_text: generateAvatarText(backendComment.creator_name)
    }
  };
};

/**
 * 获取评论列表
 * @param {string} orderId - 工单ID
 * @param {number} cursor - 游标，上次查询结果中最后一条评论的comment_id，首次查询时传0
 * @param {number} limit - 每页数量
 * @returns {Promise} API响应
 */
export const fetchComments = async (orderId, cursor = 0, limit = 10) => {
  try {
    const response = await service.post("order/comment/list", {
      order_id: orderId,
      cursor: cursor,
      limit: limit
    });

    // 检查响应格式
    if (!response || !response.resp_common) {
      throw new Error('响应格式错误');
    }

    // 检查业务状态码
    if (response.resp_common.ret !== 0) {
      throw new Error(response.resp_common.msg || '获取评论列表失败');
    }

    // 转换数据格式
    const comments = (response.comments || [])
      .filter(comment => comment.text_content && comment.text_content.trim()) // 过滤空评论
      .map(transformCommentData);

    // 按创建时间排序（最新的在前）
    comments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    return {
      data: {
        data: comments,
        meta: {
          has_more: response.next_cursor && response.next_cursor > 0,
          next_cursor: response.next_cursor || null
        }
      }
    };
  } catch (error) {
    console.error('获取评论列表失败:', error);
    throw error;
  }
};

/**
 * 发表新评论
 * @param {string} orderId - 工单ID
 * @param {string} text - 评论内容
 * @param {number} currentStage - 当前阶段
 * @returns {Promise} API响应
 */
export const postComment = async (orderId, text, currentStage) => {
  try {
    const response = await service.post("order/comment/add", {
      order_id: orderId,
      text: text,
      currentStage: currentStage
    });

    // 检查响应格式
    if (!response || !response.resp_common) {
      throw new Error('响应格式错误');
    }

    // 检查业务状态码
    if (response.resp_common.ret !== 0) {
      throw new Error(response.resp_common.msg || '发表评论失败');
    }

    // 转换返回的评论数据格式
    let transformedComment = null;
    if (response.comment) {
      transformedComment = transformCommentData(response.comment);
    }

    // 处理兼容字段的时间戳
    let compatCreatedAt = null;
    if (response.comment && response.comment.created_at) {
      const timestamp = typeof response.comment.created_at === 'string' ? 
        parseInt(response.comment.created_at, 10) : 
        response.comment.created_at;
      
      if (timestamp && !isNaN(timestamp)) {
        compatCreatedAt = new Date(timestamp * 1000).toISOString();
      }
    }

    return {
      resp_common: response.resp_common,
      comment: transformedComment,
      // 保持与原有前端逻辑兼容的字段
      comment_id: response.comment ? response.comment.comment_id : null,
      created_at: compatCreatedAt
    };
  } catch (error) {
    console.error('发表评论失败:', error);
    throw error;
  }
};

// ********************** 工单管理模块 **********************
/**
 * 检查当前用户是否有工单管理页权限
 * @returns {Promise<Object>} 权限检查结果
 */
export const requestManagementPermission = async () => {
  try {
    const response = await service.post("/order/permissions/management-order", {});
    return response;
  } catch (error) {
    console.error('检查工单管理权限失败:', error);
    return {
      resp_common: {
        ret: -1,
        msg: error.response?.data?.message || '权限检查失败',
        request_id: 'error'
      },
      has_permission: false
    };
  }
};

/**
 * 获取工单管理列表数据，支持复杂筛选、排序和分页
 * @param {Object} params - 请求参数
 * @param {number} params.page - 页码
 * @param {number} params.page_size - 每页数量
 * @param {string} params.order_id - 工单号
 * @param {string} params.title - 工单标题
 * @param {string} params.applied_start_date - 申请开始日期
 * @param {string} params.applied_end_date - 申请结束日期
 * @param {Array<string>} params.order_types - 工单类型数组
 * @param {Array<string>} params.statuses - 工单状态数组
 * @param {Array<string>} params.applicants - 申请人邮箱数组
 * @param {Array<string>} params.ops_leads - 运维负责人邮箱数组
 * @param {Array<string>} params.cc_list - 抄送人邮箱数组
 * @param {string} params.total_duration_filter - 总耗时筛选
 * @param {string} params.node_stay_duration_filter - 当前节点停留时长筛选
 * @param {string} params.sort_by - 排序字段
 * @param {string} params.sort_order - 排序顺序
 * @returns {Promise<Object>} API响应
 */
export const requestManageOrder = async (params) => {
  try {
    // 处理参数，确保数组类型正确
    const processedParams = {
      page: Number(params.page) || 1,
      page_size: Number(params.page_size) || 10,
      order_id: params.order_id || '',
      title: params.title || '',
      applied_start_date: params.applied_start_date || null,
      applied_end_date: params.applied_end_date || null,
      order_types: Array.isArray(params.order_types) ? params.order_types : [],
      statuses: Array.isArray(params.statuses) ? params.statuses : [],
      applicants: Array.isArray(params.applicants) ? params.applicants : [],
      ops_leads: Array.isArray(params.ops_leads) ? params.ops_leads : [],
      cc_list: Array.isArray(params.cc_list) ? params.cc_list : [],
      // 未筛选时传空字符串，兼容空字符串为有效值（避免被 || 覆盖）
      total_duration_filter: (params.total_duration_filter ?? ''),
      node_stay_duration_filter: (params.node_stay_duration_filter ?? ''),
      sort_by: params.sort_by || 'apply_time',
      sort_order: params.sort_order || 'desc'
    };

    const response = await service.post("/order/management-order", processedParams);
    return response;
  } catch (error) {
    console.error('获取工单管理列表失败:', error);
    return {
      resp_common: {
        ret: -1,
        msg: error.response?.data?.message || '获取工单列表失败',
        request_id: 'error'
      },
      orders: [],
      total: 0,
      available_order_types: [],
      available_applicants: [],
      available_ops_leads: [],
      available_cc_users: []
    };
  }
};