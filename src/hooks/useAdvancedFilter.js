import { useState } from 'react';
import { cloneDeep, isEqual } from 'lodash';

/**
 * 高级筛选弹窗逻辑的自定义Hook
 * 封装弹窗状态管理、值变化判断等复杂逻辑
 */
const useAdvancedFilter = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [initialValues, setInitialValues] = useState({});

  /**
   * 打开高级筛选弹窗，并记录打开时的筛选状态
   * @param {Object} currentFilters - 当前的筛选条件值
   */
  const openModal = (currentFilters) => {
    // 使用深拷贝避免引用问题
    setInitialValues(cloneDeep(currentFilters));
    setIsModalVisible(true);
  };

  /**
   * 点击弹窗【确定】按钮时，判断值是否变化，并回调给父组件
   * @param {Object} newValues - 弹窗表单的当前值
   * @param {Function} onOk - 父组件传入的确定回调函数
   */
  const handleOk = (newValues, onOk) => {
    // 使用深度比较判断值是否变化
    const hasChanged = !isEqual(newValues, initialValues);
    
    if (hasChanged && onOk) {
      // 有变化时才调用父组件的回调
      onOk(newValues);
    }
    
    // 关闭弹窗
    setIsModalVisible(false);
  };

  /**
   * 点击弹窗【取消】按钮
   */
  const handleCancel = () => {
    setIsModalVisible(false);
  };

  // 返回弹窗相关的属性和方法
  const modalProps = {
    visible: isModalVisible,
    onCancel: handleCancel
  };

  return {
    openModal,
    handleOk,
    handleCancel,
    modalProps,
    isModalVisible
  };
};

export default useAdvancedFilter;
