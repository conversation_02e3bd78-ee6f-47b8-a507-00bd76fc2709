import { Component } from "react";
import styled from "styled-components";
import { Card } from "antd";


const { Meta } = Card;
const ResourceCard = styled(Card)`
  width: 10vw;
  margin: 0 0.5vw;
  padding: 0 0.25vw;
  text-align: center;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 10px 20px 0 rgba(153, 153, 153, 0.25);
  
`

export default class OrderCard extends Component {
  render() {
    return (
      <ResourceCard
        cover={<img alt="example" src={this.props.imgURL} />}
        hoverable={true}
      >
        <Meta title={this.props.title} style={{ "flexWrap": "nowrap", "justifyContent": "center", "alignItems": "center" }} />
      </ResourceCard>
    );
  }
}
