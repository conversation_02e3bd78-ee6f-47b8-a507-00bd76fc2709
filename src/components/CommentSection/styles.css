/* 评论区专属样式 - 匹配页面风格 */

.comment-section-container {
  display: flex;
  flex-direction: column;
  height: 500px; /* 保持原有高度 */
  background-color: #ffffff;
}

/* 评论列表容器 - 可滚动区域 */
.comment-list-wrapper {
  flex: 1 1 auto;
  overflow: hidden; /* 让内部元素处理滚动 */
  padding: 0; /* 移除内边距，让Card统一管理 */
  background-color: #ffffff;
}

/* 评论输入区容器 - 固定底部 */
.comment-input-wrapper {
  flex-shrink: 0; /* 关键：防止被压缩 */
  padding: 16px 0 0 0; /* 只保留顶部间距 */
  border-top: 1px solid #e2e8f0; /* 使用与页面一致的边框颜色 */
  background-color: #ffffff;
}