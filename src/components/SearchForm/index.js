import React, { Component } from 'react';
import { Input, Button, Form, DatePicker, message, Select, Divider } from 'antd';
import { SearchOutlined, FilterOutlined, ReloadOutlined } from '@ant-design/icons';
import { SearchFormContainer, StyledFormItem } from './styles';
import FilterTagList from './FilterTagList';
import { trimAndHandleEmpty } from '../../util/strHelper';
import { isValidDateRange } from '../../util/timeHelper';
import moment from 'moment';
import { isEqual } from 'lodash';

class SearchForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // 基础筛选条件
      orderId: props.initialSearchParams?.orderId || '',
      title: props.initialSearchParams?.title || '',
      startDate: props.initialSearchParams?.appliedStartDate ? moment(props.initialSearchParams.appliedStartDate) : null,
      endDate: props.initialSearchParams?.appliedEndDate ? moment(props.initialSearchParams.appliedEndDate) : null,
      
      // 错误信息
      dateRangeError: '',
      orderIdError: '',
      titleError: '',
      
      // 高级筛选条件（使用后端字段名）
      order_types: [],
      filter_by_role: [], // 工单状态标签筛选（审批工单页面）
      statuses: [], // 工单状态筛选（工单管理页面）
      applicants: [],
      ops_leads: [],
      cc_list: [],
      total_duration_filter: '',
      node_stay_duration_filter: '',
      advancedVisible: false,
      
      // 双状态模型：准备中的筛选条件和已生效的筛选条件
      pendingFilters: {},
      appliedFilters: {},
    };
    this.handleInputChange = this.handleInputChange.bind(this);
    this.handleDateRangeChange = this.handleDateRangeChange.bind(this);
    this.handleSearchClick = this.handleSearchClick.bind(this);
    this.validateDateRange = this.validateDateRange.bind(this);
  }

  // 统一的安全日期格式化方法（基于moment）
  formatDateSafely = (date) => {
    if (!date) return null;
    try {
      if (date && typeof date.format === 'function') {
        return date.format('YYYY-MM-DD HH:mm:ss');
      }
      // 如果不是moment对象，尝试转换
      const m = moment(date);
      if (m.isValid()) {
        return m.format('YYYY-MM-DD HH:mm:ss');
      }
    } catch (error) {
      console.error('日期格式化失败:', error, date);
    }
    return null;
  }

  componentDidUpdate(prevProps) {
    // 检查 props 中的搜索参数是否发生变化，如果变化则更新内部 state
    const { initialSearchParams } = this.props;
    
    // 防止无限循环：只有在外部传入的参数与当前内部状态不一致时才更新
    if (initialSearchParams && prevProps.initialSearchParams) {
      const { orderId, title, startDate, endDate } = this.state;
      
      // 获取当前内部状态的格式化版本，使用统一的安全格式化方法
      const currentFormattedStartDate = this.formatDateSafely(startDate);
      const currentFormattedEndDate = this.formatDateSafely(endDate);
      
      // 使用深度比较，避免字符串格式差异导致的误判
      const prevFormattedStartDate = prevProps.initialSearchParams?.appliedStartDate || null;
      const prevFormattedEndDate = prevProps.initialSearchParams?.appliedEndDate || null;
      const currentPropStartDate = initialSearchParams.appliedStartDate || null;
      const currentPropEndDate = initialSearchParams.appliedEndDate || null;
      
      // 只有在props真正变化且与内部状态不一致时才更新
      const propsChanged = (
        (initialSearchParams.orderId || '') !== (prevProps.initialSearchParams?.orderId || '') ||
        (initialSearchParams.title || '') !== (prevProps.initialSearchParams?.title || '') ||
        currentPropStartDate !== prevFormattedStartDate ||
        currentPropEndDate !== prevFormattedEndDate
      );
      
      const needUpdate = propsChanged && (
        (initialSearchParams.orderId || '') !== orderId ||
        (initialSearchParams.title || '') !== title ||
        currentPropStartDate !== currentFormattedStartDate ||
        currentPropEndDate !== currentFormattedEndDate
      );
      
      if (needUpdate) {
        // 使用标记防止在更新过程中触发回调
        this._isUpdatingFromProps = true;
        
        console.log('SearchForm props更新，同步内部状态:', {
          from: { orderId, title, startDate: currentFormattedStartDate, endDate: currentFormattedEndDate },
          to: initialSearchParams
        });
        
        // 安全处理日期值，基于moment
        const safeParseDateString = (dateLike) => {
          if (!dateLike || dateLike === 'null' || dateLike === 'undefined') {
            return null;
          }
          try {
            // 已是moment对象
            if (dateLike && typeof dateLike === 'object' && typeof dateLike.format === 'function') {
              return dateLike;
            }
            const parsed = moment(dateLike);
            return parsed.isValid() ? parsed : null;
          } catch (error) {
            console.error('日期解析失败:', error, dateLike);
            return null;
          }
        };
        
        // 仅同步props中明确提供的字段；未提供的文本字段保持现有输入，避免被重置为空
        this.setState(prev => ({
          orderId: Object.prototype.hasOwnProperty.call(initialSearchParams, 'orderId')
            ? (initialSearchParams.orderId || '')
            : prev.orderId,
          title: Object.prototype.hasOwnProperty.call(initialSearchParams, 'title')
            ? (initialSearchParams.title || '')
            : prev.title,
          startDate: safeParseDateString(initialSearchParams.appliedStartDate),
          endDate: safeParseDateString(initialSearchParams.appliedEndDate),
          dateRangeError: '', // props更新时清空错误
        }), () => {
          // 更新完成后清除标记
          this._isUpdatingFromProps = false;
        });
      }
    }
  }

  handleInputChange(name, value) {
    // 实时验证输入内容
    let error = '';

    if (name === 'orderId') {
      // 验证单号不能仅包含空格
      if (value && typeof value === 'string' && value.trim() === '' && value.length > 0) {
        error = '请输入有效单号';
      }
      this.setState({ [name]: value, orderIdError: error }, () => {
        this.updatePendingFilters();
        this.notifySearchParamsChange();
      });
    } else if (name === 'title') {
      // 验证标题不能仅包含空格
      if (value && typeof value === 'string' && value.trim() === '' && value.length > 0) {
        error = '请输入有效标题';
      }
      this.setState({ [name]: value, titleError: error }, () => {
        this.updatePendingFilters();
        this.notifySearchParamsChange();
      });
    } else {
      this.setState({ [name]: value }, () => {
        this.updatePendingFilters();
        this.notifySearchParamsChange();
      });
    }
  }

  // 获取当前搜索参数（供父组件调用）
  getCurrentSearchParams = () => {
    const { orderId, title, startDate, endDate } = this.state;
    
    // 安全的日期格式化函数
    const formatDate = (date) => {
      if (!date) return null;
      try {
        // 确保date是moment对象
        if (date && typeof date.format === 'function') {
          return date.format('YYYY-MM-DD HH:mm:ss');
        }
        // 如果不是moment对象，尝试转换
        const m = moment(date);
        if (m.isValid()) {
          return m.format('YYYY-MM-DD HH:mm:ss');
        }
        return null;
      } catch (error) {
        console.error('日期格式化错误:', error, date);
        return null;
      }
    };
    
    return {
      orderId: trimAndHandleEmpty(orderId),
      title: trimAndHandleEmpty(title),
      appliedStartDate: formatDate(startDate),
      appliedEndDate: formatDate(endDate),
    };
  };

  // 通知父组件搜索参数发生变化（用于实时同步清空操作）
  notifySearchParamsChange() {
    // 只在从外部props更新且非用户操作时阻止回调，避免无限循环
    if (this._isUpdatingFromProps && !this._isUserAction) {
      return;
    }
    
    try {
      if (this.props.onSearchParamsChange) {
        this.props.onSearchParamsChange(this.getCurrentSearchParams());
      }
      // 新增：支持工单管理页面的实时表单变化回调
      if (this.props.onFormChange) {
        const formData = this.getCurrentSearchParams();
        // 转换字段名以匹配工单管理页面的API
        this.props.onFormChange({
          order_id: formData.orderId,
          title: formData.title,
          applied_start_date: formData.appliedStartDate,
          applied_end_date: formData.appliedEndDate
        });
      }
    } catch (error) {
      console.error('通知搜索参数变化时出错:', error);
      // 防止错误传播，但不阻断用户操作
    }
  }

  validateDateRange(dates) {
    const [startDate, endDate] = dates || [null, null];
    return isValidDateRange(startDate ? startDate.toDate() : null, endDate ? endDate.toDate() : null);
  }

  handleDateRangeChange(dates) {
    const [newStartDate, newEndDate] = dates || [null, null];
    let error = '';

    // 实时校验：如果两个日期都已选择，且结束日期早于开始日期
    if (newStartDate && newEndDate && newEndDate.isBefore(newStartDate)) {
      error = '结束日期不能早于开始日期';
      // 自动将无效的结束日期置为未输入状态
      this.setState({ startDate: newStartDate, endDate: null, dateRangeError: error }, () => {
        this.updatePendingFilters();
        this.notifySearchParamsChange();
      });
    } else {
      this.setState({
        startDate: newStartDate,
        endDate: newEndDate,
        dateRangeError: '', // 清空错误
      }, () => {
        this.updatePendingFilters();
          this.notifySearchParamsChange();
      });
    }
  }

  // 处理独立日期选择器的变化（保持moment类型）
  handleSingleDateChange(type, date) {
    try {
      // 标记这是用户直接操作，不是从外部props更新
      this._isUserAction = true;
      
      const newState = {};
      
      if (type === 'start') {
        newState.startDate = date ? moment(date) : null;
      } else if (type === 'end') {
        newState.endDate = date ? moment(date) : null;
      }
      
      this.setState(newState, () => {
        try {
          // 验证日期范围（与审批工单一致的处理：无效时清空“后选择”的那个）
          const { startDate, endDate } = this.state;
          if (startDate && endDate) {
            const isValid = this.validateDateRange([startDate, endDate]);
            if (!isValid) {
              const errorMsg = '结束时间不能早于开始时间';
              this.setState(prev => ({
                // 清空后选择的那个日期
                startDate: type === 'start' ? null : prev.startDate,
                endDate: type === 'end' ? null : prev.endDate,
                dateRangeError: errorMsg
              }), () => {
                // 仅保留内联错误提示，不触发全局提示；
                // 也不向父组件回传（避免父级props回写立刻清空本地错误态）。
              });
              return; // 这里提前返回，避免再次notify
            } else {
              this.setState({ dateRangeError: '' });
            }
          } else {
            // 如果只有一个日期或都为空，清除错误
            this.setState({ dateRangeError: '' });
          }

          // 通知父组件表单变化（有效或单端存在时）
          this.updatePendingFilters();
          this.notifySearchParamsChange();
        } catch (error) {
          console.error('日期范围验证时出错:', error);
          // 清除错误状态，防止界面卡死
          this.setState({ dateRangeError: '' });
        } finally {
          // 清除用户操作标记
          this._isUserAction = false;
        }
      });
    } catch (error) {
      console.error('处理日期选择器变化时出错:', error);
      // 清除用户操作标记
      this._isUserAction = false;
    }
  }

  handleSearchClick() {
    const { orderId, title, startDate, endDate, dateRangeError, orderIdError, titleError } = this.state;

    // 迭代三：增强校验逻辑
    const validationErrors = [];
    const hasDateRangeError = !!dateRangeError;
    
    // 检查所有校验错误
    if (dateRangeError) {
      validationErrors.push(dateRangeError);
    }
    if (orderIdError) {
      validationErrors.push(orderIdError);
    }
    if (titleError) {
      validationErrors.push(titleError);
    }
    
    // 前端处理：当单号或标题输入框中仅包含空格时，置为空字符串
    const processedOrderId = trimAndHandleEmpty(orderId);
    const processedTitle = trimAndHandleEmpty(title);
    
    // 检查单号格式（如果不为空）
    if (processedOrderId && processedOrderId.length > 50) {
      validationErrors.push('单号长度不能超过50个字符');
    }
    
    // 检查标题格式（如果不为空）
    if (processedTitle && processedTitle.length > 100) {
      validationErrors.push('标题长度不能超过100个字符');
    }
    
    // 迭代三：如果存在校验错误，触发全局提示
    if (validationErrors.length > 0) {
      // 特殊处理：当包含日期范围错误时，不触发全局提示，仅保留输入框下方的错误提示
      // 这样可以避免在“我的审批工单”和“工单管理”页面出现全局 warn，保持一致的轻量体验
      if (!hasDateRangeError && this.props.onValidationError) {
        this.props.onValidationError(validationErrors.join('； '));
      }
      return; // 停止搜索，不传递给父组件
    }
    
    // 没有错误时才执行搜索
    // 构造搜索参数对象（使用后端字段名）
    const searchParams = {
      order_id: processedOrderId,
      title: processedTitle,
      applied_start_date: this.formatDateSafely(startDate),
      applied_end_date: this.formatDateSafely(endDate),
      order_types: this.state.order_types,
      applicants: this.state.applicants,
      ops_leads: this.state.ops_leads,
      cc_list: this.state.cc_list,
      total_duration_filter: this.state.total_duration_filter,
      node_stay_duration_filter: this.state.node_stay_duration_filter
    };

    // 根据页面类型添加状态字段
    if (this.props.availableOptions) {
      // 工单管理页面使用statuses字段
      if (this.state.statuses?.length) {
        searchParams.statuses = this.state.statuses;
      }
    } else {
      // 审批工单页面使用filter_by_role字段
      if (this.state.filter_by_role?.length) {
        searchParams.filter_by_role = this.state.filter_by_role;
      }
    }

    // 更新applied状态，将准备中的条件变为已生效
    const newAppliedFilters = {};
    if (processedOrderId) newAppliedFilters.order_id = processedOrderId;
    if (processedTitle) newAppliedFilters.title = processedTitle;
    if (startDate) newAppliedFilters.applied_start_date = this.formatDateSafely(startDate);
    if (endDate) newAppliedFilters.applied_end_date = this.formatDateSafely(endDate);
    if (this.state.order_types?.length) newAppliedFilters.order_types = this.state.order_types;
    
    // 根据页面类型添加状态字段到appliedFilters
    if (this.props.availableOptions) {
      // 工单管理页面使用statuses字段
      if (this.state.statuses?.length) newAppliedFilters.statuses = this.state.statuses;
    } else {
      // 审批工单页面使用filter_by_role字段
      if (this.state.filter_by_role?.length) newAppliedFilters.filter_by_role = this.state.filter_by_role;
    }
    
    if (this.state.applicants?.length) newAppliedFilters.applicants = this.state.applicants;
    if (this.state.ops_leads?.length) newAppliedFilters.ops_leads = this.state.ops_leads;
    if (this.state.cc_list?.length) newAppliedFilters.cc_list = this.state.cc_list;
    if (this.state.total_duration_filter) newAppliedFilters.total_duration_filter = this.state.total_duration_filter;
    if (this.state.node_stay_duration_filter) newAppliedFilters.node_stay_duration_filter = this.state.node_stay_duration_filter;

    this.setState({
      appliedFilters: newAppliedFilters,
      pendingFilters: newAppliedFilters,
      advancedVisible: false // 搜索后自动收起高级筛选
    });

    // 传递给父组件
    this.props.onSearch(searchParams);
  }

  // 重置所有筛选条件
  handleReset = () => {
    this.setState({
      orderId: '',
      title: '',
      startDate: null,
      endDate: null,
      order_types: [],
      filter_by_role: [],
      statuses: [],
      applicants: [],
      ops_leads: [],
      cc_list: [],
      total_duration_filter: '',
      node_stay_duration_filter: '',
      pendingFilters: {},
      appliedFilters: {},
      advancedVisible: false,
      dateRangeError: '',
      orderIdError: '',
      titleError: ''
    }, () => {
      // 如果有外部重置方法，调用它
      if (this.props.onReset) {
        this.props.onReset();
      } else {
        // 否则执行空搜索
        this.props.onSearch({});
      }
    });
  }

  // 获取当前搜索参数的方法，供外部调用
  getCurrentSearchParams = () => {
    const { 
      orderId, title, startDate, endDate,
      order_types, statuses, applicants,
      ops_leads, cc_list, total_duration_filter, node_stay_duration_filter
    } = this.state;
    const processedOrderId = trimAndHandleEmpty(orderId);
    const processedTitle = trimAndHandleEmpty(title);
    
    const params = {
      order_id: processedOrderId,
      title: processedTitle,
      applied_start_date: this.formatDateSafely(startDate),
      applied_end_date: this.formatDateSafely(endDate),
      order_types: order_types,
    };

    // 根据页面类型设置状态字段
    if (this.props.availableOptions) {
      // 工单管理页面使用statuses字段
      if (statuses?.length) params.statuses = statuses;
    } else {
      // 审批工单页面使用filter_by_role字段
      const { filter_by_role } = this.state;
      if (filter_by_role?.length) params.filter_by_role = filter_by_role;
    }

    // 工单管理页面的额外字段
    if (this.props.availableOptions) {
      if (applicants?.length) params.applicants = applicants;
      if (ops_leads?.length) params.ops_leads = ops_leads;
      if (cc_list?.length) params.cc_list = cc_list;
      if (total_duration_filter) params.total_duration_filter = total_duration_filter;
      if (node_stay_duration_filter) params.node_stay_duration_filter = node_stay_duration_filter;
    }

    return params;
  }

  // 更新准备中的筛选条件
  updatePendingFilters = () => {
    const { 
      orderId, title, startDate, endDate, 
      order_types, statuses, filter_by_role, applicants, 
      ops_leads, cc_list, total_duration_filter, node_stay_duration_filter
    } = this.state;
    
    const pendingFilters = {};
    
    // 基础筛选条件
    const processedOrderId = trimAndHandleEmpty(orderId);
    const processedTitle = trimAndHandleEmpty(title);
    
    if (processedOrderId) {
      pendingFilters.order_id = processedOrderId;
    }
    if (processedTitle) {
      pendingFilters.title = processedTitle;
    }
    if (startDate) {
      pendingFilters.applied_start_date = this.formatDateSafely(startDate);
    }
    if (endDate) {
      pendingFilters.applied_end_date = this.formatDateSafely(endDate);
    }
    
    // 高级筛选条件（使用后端字段名）
    if (order_types?.length) {
      pendingFilters.order_types = order_types;
    }
    // 根据页面类型使用不同的状态字段
    if (this.props.availableOptions) {
      // 工单管理页面使用statuses字段
      if (statuses?.length) {
        pendingFilters.statuses = statuses;
      }
    } else {
      // 审批工单页面使用filter_by_role字段
      if (filter_by_role?.length) {
        pendingFilters.filter_by_role = filter_by_role;
      }
    }
    if (applicants?.length) {
      pendingFilters.applicants = applicants;
    }
    if (ops_leads?.length) {
      pendingFilters.ops_leads = ops_leads;
    }
    if (cc_list?.length) {
      pendingFilters.cc_list = cc_list;
    }
    if (total_duration_filter) {
      pendingFilters.total_duration_filter = total_duration_filter;
    }
    if (node_stay_duration_filter) {
      pendingFilters.node_stay_duration_filter = node_stay_duration_filter;
    }
    
    // 如果pendingFilters与appliedFilters不同，则将已生效的标签变灰
    if (!isEqual(pendingFilters, this.state.appliedFilters)) {
      this.setState({ pendingFilters });
    }
  }

  // 移除筛选标签
  handleFilterTagRemove = (fieldKey) => {
    const { appliedFilters, pendingFilters } = this.state;
    
    // 判断是移除已生效的条件还是准备中的条件
    const isAppliedTag = isEqual(appliedFilters[fieldKey], pendingFilters[fieldKey]);
    
    if (isAppliedTag) {
      // 移除已生效的条件：立即重新查询
      const newAppliedFilters = { ...appliedFilters };
      const newPendingFilters = { ...pendingFilters };
      delete newAppliedFilters[fieldKey];
      delete newPendingFilters[fieldKey];
      
      // 同时更新对应的state
      this.updateStateFromFilters(newPendingFilters);
      
      this.setState({
        appliedFilters: newAppliedFilters,
        pendingFilters: newPendingFilters
      });
      
      // 立即重新查询
      if (this.props.onSearch) {
        this.props.onSearch(newAppliedFilters);
      }
    } else {
      // 移除准备中的条件：仅更新前端状态
      const newPendingFilters = { ...pendingFilters };
      delete newPendingFilters[fieldKey];
      
      // 同时更新对应的state
      this.updateStateFromFilters(newPendingFilters);
      
      this.setState({ pendingFilters: newPendingFilters });
    }
  }

  // 根据筛选条件更新表单状态
  updateStateFromFilters = (filters) => {
    const newState = {
      orderId: filters.order_id || '',
      title: filters.title || '',
      startDate: filters.applied_start_date ? moment(filters.applied_start_date) : null,
      endDate: filters.applied_end_date ? moment(filters.applied_end_date) : null,
      order_types: filters.order_types || [],
      filter_by_role: filters.filter_by_role || [],
      statuses: filters.statuses || [],
      applicants: filters.applicants || [],
      ops_leads: filters.ops_leads || [],
      cc_list: filters.cc_list || [],
      total_duration_filter: filters.total_duration_filter || '',
      node_stay_duration_filter: filters.node_stay_duration_filter || ''
    };
    this.setState(newState);
  }

  render() {
    const { orderId, title, startDate, endDate, dateRangeError, orderIdError, titleError, pendingFilters, appliedFilters } = this.state;
    const { showAdvancedFields = true } = this.props;
    
    return (
      <SearchFormContainer>
        {/* 标题栏：左侧标题，右侧高级筛选漏斗图标 */}
        <div className="header-layout">
          <div style={{ fontSize: 18, fontWeight: 700, color: '#111827' }}>工单搜索</div>
          <Button 
            className="filter-toggle-btn"
            onClick={() => this.setState({ advancedVisible: !this.state.advancedVisible })}
            icon={<FilterOutlined />}
            type="text"
            size="small"
          />
        </div>
        
        {/* 新布局：左侧筛选条件，右侧操作按钮 */}
        <div className="search-layout">
          {/* 左侧筛选条件区域 */}
          <div className="filters-section">
          <StyledFormItem
            label="单号"
            className={orderId ? 'has-value' : 'no-value'}
            validateStatus={orderIdError ? 'error' : ''}
            help={orderIdError}
          >
            <Input
              name="orderId"
              placeholder="请输入单号"
              value={orderId}
              onChange={(e) => this.handleInputChange('orderId', e.target.value)}
              allowClear
              status={orderIdError ? 'error' : ''}
                style={{ width: 200 }}
            />
          </StyledFormItem>
            
          <StyledFormItem
            label="标题"
            className={title ? 'has-value' : 'no-value'}
            validateStatus={titleError ? 'error' : ''}
            help={titleError}
          >
            <Input
              name="title"
              placeholder="请输入标题"
              value={title}
              onChange={(e) => this.handleInputChange('title', e.target.value)}
              allowClear
              status={titleError ? 'error' : ''}
                style={{ width: 200 }}
            />
          </StyledFormItem>

          <StyledFormItem
            label="申请开始日期"
            className={`date-item ${startDate ? 'has-value' : 'no-value'}`}
          >
            <DatePicker
              showTime
              value={startDate}
              onChange={(date) => this.handleSingleDateChange('start', date)}
              placeholder="选择开始日期"
                style={{ width: 200 }}
            />
          </StyledFormItem>
          
          <StyledFormItem
            label="申请结束日期"
            className={`date-item ${endDate ? 'has-value' : 'no-value'}`}
            validateStatus={dateRangeError ? 'error' : ''}
            help={dateRangeError}
          >
            <DatePicker
              showTime
              value={endDate}
              onChange={(date) => this.handleSingleDateChange('end', date)}
              placeholder="选择结束日期"
                style={{ width: 200 }}
              status={dateRangeError ? 'error' : ''}
            />
          </StyledFormItem>
          </div>

          {/* 右侧操作按钮区域 */}
          <div className="actions-section">
            {/* 审批工单页面的内置按钮 */}
          {showAdvancedFields && (
              <>
                <Button 
                  className="search-btn"
                  onClick={this.handleSearchClick}
                  icon={<SearchOutlined />}
                >
                搜索
              </Button>
                
                <Button 
                  className="reset-btn"
                  onClick={this.handleReset}
                  icon={<ReloadOutlined />}
                >
                  重置
                </Button>
              </>
            )}
            
            {/* 工单管理页面的外部按钮 */}
            {!showAdvancedFields && (
              <>
                {this.props.onSearch && (
                  <Button 
                    className="search-btn"
                  onClick={() => this.props.onSearch(this.getCurrentSearchParams())}
                  loading={this.props.loading}
                    icon={<SearchOutlined />}
                >
                  搜索
                </Button>
              )}
              {this.props.onReset && (
                <Button 
                    className="reset-btn"
                  onClick={this.props.onReset}
                    icon={<ReloadOutlined />}
                >
                  重置
                </Button>
              )}
              </>
            )}
          </div>
        </div>

        {/* 已筛选条件展示 */}
        <FilterTagList
          pendingFilters={pendingFilters}
          appliedFilters={appliedFilters}
          onRemoveTag={this.handleFilterTagRemove}
          orderTypeFilters={this.props.orderTypeFilters}
        />

        {/* 高级筛选展开区域 */}
        {this.state.advancedVisible && (
          <div className="advanced-section">
            {/* 根据页面类型显示不同的布局 */}
            {this.props.availableOptions ? (
              // 工单管理页面布局
              <>
                {/* 第一行：工单类型、申请人、总耗时、当前节点停留时长 */}
                <div className="advanced-row">
                  <StyledFormItem label="工单类型">
                    <Select
                      mode="multiple"
                      placeholder="选择工单类型"
                      value={this.state.order_types}
                      onChange={(values) => this.setState({ order_types: values }, () => {
                        this.updatePendingFilters();
                      })}
                      options={(this.props.orderTypeFilters || []).map(f => ({ label: f.text, value: f.value }))}
                      style={{ width: 200 }}
                    />
                  </StyledFormItem>

                  <StyledFormItem label="申请人">
                    <Select
                      mode="multiple"
                      placeholder="选择申请人"
                      value={this.state.applicants}
                      onChange={(values) => this.setState({ applicants: values }, () => {
                        this.updatePendingFilters();
                      })}
                      options={(this.props.availableOptions?.available_applicants || []).map(item => ({
                        label: typeof item === 'string' ? item : item.name || item.username,
                        value: typeof item === 'string' ? item : item.username || item.name
                      }))}
                      style={{ width: 200 }}
                    />
                  </StyledFormItem>

                  <StyledFormItem label="总耗时">
                    <Select
                      placeholder="选择总耗时"
                      value={this.state.total_duration_filter || undefined}
                      onChange={(value) => this.setState({ total_duration_filter: value || '' }, () => {
                        this.updatePendingFilters();
                      })}
                      allowClear
                      options={[
                        { label: '超过1天', value: 'over_1d' },
                        { label: '超过3天', value: 'over_3d' },
                        { label: '超过1周', value: 'over_1w' }
                      ]}
                      style={{ width: 200 }}
                    />
                  </StyledFormItem>

                  <StyledFormItem label="当前节点停留时长">
                    <Select
                      placeholder="选择当前节点停留时长"
                      value={this.state.node_stay_duration_filter || undefined}
                      onChange={(value) => this.setState({ node_stay_duration_filter: value || '' }, () => {
                        this.updatePendingFilters();
                      })}
                      allowClear
                      options={[
                        { label: '超过12小时', value: 'over_12h' },
                        { label: '超过1天', value: 'over_1d' },
                        { label: '超过3天', value: 'over_3d' }
                      ]}
                      style={{ width: 200 }}
                    />
                  </StyledFormItem>
                </div>

                {/* 第二行：工单状态、抄送人、运维负责人 */}
                <div className="advanced-row">
                  <StyledFormItem label="工单状态">
                    <Select
                      mode="multiple"
                      placeholder="选择状态"
                      value={this.state.statuses}
                      onChange={(values) => {
                        this.setState({ statuses: values }, () => {
                          this.updatePendingFilters();
                        });
                      }}
                      options={(this.props.statusOptions || [
                        { label: '待审批', value: '待审批' },
                        { label: '处理中', value: '处理中' },
                        { label: '已完成', value: '已完成' },
                      ])}
                      style={{ width: 200 }}
                    />
                  </StyledFormItem>

                  <StyledFormItem label="抄送人">
                    <Select
                      mode="multiple"
                      placeholder="选择抄送人"
                      value={this.state.cc_list}
                      onChange={(values) => this.setState({ cc_list: values }, () => {
                        this.updatePendingFilters();
                      })}
                      options={(this.props.availableOptions.available_cc_users || []).map(item => ({
                        label: typeof item === 'string' ? item : item.name || item.username,
                        value: typeof item === 'string' ? item : item.username || item.name
                      }))}
                      style={{ width: 200 }}
                    />
                  </StyledFormItem>

                  <StyledFormItem label="运维负责人">
                    <Select
                      mode="multiple"
                      placeholder="选择运维负责人"
                      value={this.state.ops_leads}
                      onChange={(values) => this.setState({ ops_leads: values }, () => {
                        this.updatePendingFilters();
                      })}
                      options={(this.props.availableOptions.available_ops_leads || []).map(item => ({
                        label: typeof item === 'string' ? item : item.name || item.username,
                        value: typeof item === 'string' ? item : item.username || item.name
                      }))}
                      style={{ width: 200 }}
                    />
                  </StyledFormItem>
                </div>
              </>
            ) : (
              // 我的审批页面布局 - 只显示工单类型和工单状态，且在同一行
              <div className="advanced-row">
                <StyledFormItem label="工单类型">
                  <Select
                    mode="multiple"
                    placeholder="选择工单类型"
                    value={this.state.order_types}
                    onChange={(values) => this.setState({ order_types: values }, () => {
                      this.updatePendingFilters();
                    })}
                    options={(this.props.orderTypeFilters || []).map(f => ({ label: f.text, value: f.value }))}
                    style={{ width: 200 }}
                  />
                </StyledFormItem>

                <StyledFormItem label="工单状态">
                  <Select
                    mode="multiple"
                    placeholder="选择状态"
                    value={this.state.filter_by_role}
                    onChange={(values) => {
                      this.setState({ filter_by_role: values }, () => {
                        this.updatePendingFilters();
                      });
                    }}
                    options={(this.props.statusOptions || [
                      { label: '待审批', value: 'TO_BE_APPROVED' },
                      { label: '将被审批', value: 'WILL_BE_APPROVED' },
                      { label: '已审批', value: 'ALREADY_APPROVED' },
                      { label: '抄送给我', value: 'CC_TO_ME' },
                      { label: '已完成', value: 'COMPLETED' }
                    ])}
                    style={{ width: 200 }}
                  />
                </StyledFormItem>
              </div>
            )}
          </div>
        )}
      </SearchFormContainer>
    );
  }
}

export default SearchForm;
