import React from 'react';
import { Tag, Tooltip } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { isEqual } from 'lodash';

const FilterTagContainer = styled.div`
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  min-height: 20px;
`;

const FilterTag = styled(Tag)`
  margin-bottom: 0;
  padding: 4px 12px;
  border-radius: 16px;
  cursor: ${props => props.removable ? 'pointer' : 'default'};
  max-width: 280px;
  display: inline-flex;
  align-items: center;
  
  .tag-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    max-width: 240px;
    vertical-align: middle;
  }
  
  &.pending {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #666;
  }
  
  &.applied {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }
`;

/**
 * 审批工单页面的筛选条件标签列表组件
 * 根据 pendingFilters 和 appliedFilters 渲染灰色和蓝色的标签
 */
const FilterTagList = ({ 
  pendingFilters = {}, 
  appliedFilters = {}, 
  onRemoveTag,
  orderTypeFilters = []
}) => {
  // 判断字段是否为已生效状态（蓝色）
  const isFieldApplied = (fieldKey) => {
    return isEqual(appliedFilters[fieldKey], pendingFilters[fieldKey]);
  };

  // 获取字段显示名称
  const getFieldDisplayName = (fieldKey) => {
    const fieldNames = {
      order_id: '工单号',
      title: '标题',
      applied_start_date: '申请开始日期',
      applied_end_date: '申请结束日期',
      order_types: '工单类型',
      filter_by_role: '工单状态',
      statuses: '工单状态',
      applicants: '申请人',
      ops_leads: '运维负责人',
      cc_list: '抄送人',
      total_duration_filter: '总耗时',
      node_stay_duration_filter: '当前节点停留时长'
    };
    return fieldNames[fieldKey] || fieldKey;
  };

  // 获取值的显示文本
  const getValueDisplayText = (fieldKey, value) => {
    if (!value) return '';

    switch (fieldKey) {
      case 'order_types':
        if (Array.isArray(value)) {
          return value.map(val => {
            const found = orderTypeFilters.find(f => f.value === val);
            return found ? found.text : val;
          }).join('、');
        }
        return value;
      
          case 'filter_by_role':
      if (Array.isArray(value)) {
        const statusLabels = {
          'TO_BE_APPROVED': '待审批',
          'WILL_BE_APPROVED': '将被审批',
          'ALREADY_APPROVED': '已审批',
          'CC_TO_ME': '抄送给我',
          'COMPLETED': '已完成'
        };
        return value.map(v => statusLabels[v] || v).join('、');
      }
      return value;
      
    case 'statuses':
      if (Array.isArray(value)) {
        const statusLabels = {
          'approving': '审批中',
          'completed': '已完结',
          'rejected': '已驳回',
          'failed': '已失败'
        };
        return value.map(v => statusLabels[v] || v).join('、');
      }
      return value;
      
      case 'applicants':
      case 'ops_leads':
      case 'cc_list':
        if (Array.isArray(value)) {
          return value.join('、');
        }
        return value;
      
      case 'total_duration_filter':
        const durationLabels = {
          'over_1d': '超过1天',
          'over_3d': '超过3天',
          'over_1w': '超过1周'
        };
        return durationLabels[value] || value;
      
      case 'node_stay_duration_filter':
        const stayLabels = {
          'over_12h': '超过12小时',
          'over_1d': '超过1天',
          'over_3d': '超过3天'
        };
        return stayLabels[value] || value;
      
      case 'applied_start_date':
      case 'applied_end_date':
        // 日期格式化显示
        if (typeof value === 'string') {
          try {
            const date = new Date(value);
            return date.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
            });
          } catch (e) {
            return value;
          }
        }
        return value;
      
      default:
        return Array.isArray(value) ? value.join('、') : value;
    }
  };

  // 渲染单个标签
  const renderTag = (fieldKey, value) => {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      return null;
    }

    const isApplied = isFieldApplied(fieldKey);
    const displayName = getFieldDisplayName(fieldKey);
    const displayText = getValueDisplayText(fieldKey, value);
    const tagClass = isApplied ? 'applied' : 'pending';

    return (
      <FilterTag
        key={fieldKey}
        className={tagClass}
        closable
        closeIcon={<CloseOutlined />}
        onClose={() => onRemoveTag && onRemoveTag(fieldKey)}
      >
        <Tooltip title={`${displayName}: ${displayText}`} placement="top">
          <span className="tag-text">{displayName}: {displayText}</span>
        </Tooltip>
      </FilterTag>
    );
  };

  // 收集所有有值的筛选条件
  const allFilterKeys = new Set([
    ...Object.keys(pendingFilters),
    ...Object.keys(appliedFilters)
  ]);

  const tags = Array.from(allFilterKeys)
    .map(fieldKey => renderTag(fieldKey, pendingFilters[fieldKey]))
    .filter(Boolean);

  if (tags.length === 0) {
    return null;
  }

  return (
    <FilterTagContainer>
      {tags}
    </FilterTagContainer>
  );
};

export default FilterTagList;
