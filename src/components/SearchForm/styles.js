import styled from 'styled-components';
import { Form } from 'antd';

export const SearchFormContainer = styled.div`
  padding: 16px 20px 12px 20px; /* 减少顶部留白 */
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1px solid rgba(229,231,235,0.6);
  margin: 16px 16px 12px 16px;

  .header-layout {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .filter-toggle-btn {
    color: #6b7280;
    padding: 4px;
    
    &:hover {
      color: #374151;
      background: #f3f4f6;
    }
    
    &:focus {
      color: #374151;
      background: #f3f4f6;
    }
  }

  .search-layout {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
  }

  .filters-section {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 16px 24px; /* 增加间距 */
    align-items: center;
    min-width: 0; /* 确保flex收缩 */
  }

  .actions-section {
    display: flex;
    flex-wrap: wrap; /* 允许按钮换行 */
    gap: 12px; /* 按钮间距 */
    align-items: center;
    flex-shrink: 0; /* 防止按钮被压缩 */
    margin-left: 32px; /* 与筛选区域的间距 */
  }

  .ant-form-inline .ant-form-item {
    margin: 0;
    display: flex;
    align-items: center;
  }

  .ant-form-item-label {
    white-space: nowrap; // 防止标签换行
    label {
      color: #374151;
      font-weight: 600;
    }
  }

  /* 输入与日期选择器统一圆角与过渡 */
  .ant-input,
  .ant-picker,
  .ant-input-affix-wrapper {
    border-radius: 10px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }

  /* 聚焦与悬停的主色调（不影响全局，仅限容器内） */
  .ant-input:focus,
  .ant-input-focused,
  .ant-input-affix-wrapper-focused,
  .ant-picker-focused {
    border-color: #2563eb !important;
    box-shadow: none !important; /* 去除内部蓝色阴影，避免双框视觉 */
  }

  .ant-input:hover,
  .ant-picker:hover,
  .ant-input-affix-wrapper:hover {
    border-color: #5b86ff;
  }

  /* 下拉弹窗风格：圆角+柔和阴影，与页面匹配 */
  .ant-select-dropdown {
    border-radius: 12px !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.12), 0 4px 12px rgba(0,0,0,0.08) !important;
    padding: 6px 0 !important;
  }

  /* 下拉条目与选中态 */
  .ant-select-item {
    padding: 8px 12px;
  }
  .ant-select-item-option-active {
    background: #f5f7fb !important;
  }
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background: #eaf2ff !important;
  }

  /* 搜索按钮样式 - 蓝色背景，带图标 */
  .search-btn {
    background: #2563eb !important;
    border: none !important;
    color: white !important;
    border-radius: 8px;
    height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    &:hover, &:focus, &:active {
      background: #1d4ed8 !important;
      border: none !important;
      color: white !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    &:disabled {
      background: #9ca3af !important;
      transform: none;
      box-shadow: none;
    }
  }

  /* 高级筛选按钮样式 - 白色背景，带边框和图标 */
  .advanced-filter-btn {
    background: white !important;
    border: 1px solid #d1d5db !important;
    color: #374151 !important;
    border-radius: 8px;
    height: 40px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    &:hover, &:focus, &:active {
      border-color: #9ca3af !important;
      color: #374151 !important;
      background: #f9fafb !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    &:disabled {
      background: #f3f4f6 !important;
      border-color: #e5e7eb !important;
      color: #9ca3af !important;
      transform: none;
      box-shadow: none;
    }
  }

  /* 重置按钮样式 - 白色背景，带边框和图标 */
  .reset-btn {
    background: white !important;
    border: 1px solid #d1d5db !important;
    color: #374151 !important;
    border-radius: 8px;
    height: 40px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    &:hover, &:focus, &:active {
      border-color: #9ca3af !important;
      color: #374151 !important;
      background: #f9fafb !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    &:disabled {
      background: #f3f4f6 !important;
      border-color: #e5e7eb !important;
      color: #9ca3af !important;
      transform: none;
      box-shadow: none;
    }
  }

  /* 一般按钮基础样式 */
  .ant-btn { 
    border-radius: 8px; 
    height: 36px; 
  }

  /* 高级筛选区域布局 - 与默认筛选条件保持一致 */
  .advanced-area, .advanced-row {
    display: flex;
    flex-wrap: wrap;
    gap: 12px 16px;
    margin-top: 16px;
    align-items: flex-start; /* 统一顶部对齐 */
    
    .ant-form-item {
      margin: 0;
      display: flex;
      align-items: center; /* 表单项内部居中对齐 */
      height: auto; /* 自动高度 */
    }
    
    /* 确保标签和输入框对齐 */
    .ant-form-item-label {
      display: flex;
      align-items: center;
      height: 32px; /* 固定标签高度与输入框一致 */
      line-height: 32px;
    }
    
    /* 确保输入控件对齐 */
    .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 32px; /* 固定控件高度 */
    }
  }
  
  .advanced-row {
    margin-top: 12px; /* 行之间的间距 */
    
    &:first-child {
      margin-top: 16px; /* 第一行与上方内容的间距 */
    }
  }
  
  /* 应用于所有高级筛选区域的样式 */
  .advanced-area, .advanced-row {
    /* 高级筛选输入框样式 - 胶囊状，与默认筛选条件完全一致 */
    .ant-select,
    .ant-select-selector,
    .ant-input {
      width: 200px !important;
      min-width: 200px !important;
      max-width: 200px !important;
      height: 32px !important; /* 固定高度确保对齐 */
      border-radius: 10px !important; /* 胶囊状圆角 */
      transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
      vertical-align: middle !important; /* 垂直居中对齐 */
    }
    
    /* Select组件的胶囊状样式 */
    .ant-select .ant-select-selector {
      width: 200px !important;
      height: 32px !important; /* 固定高度确保对齐 */
      border-radius: 10px !important;
      transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
      display: flex !important;
      align-items: center !important;
    }
    
    /* Input输入框的胶囊状样式 */
    .ant-input {
      height: 32px !important; /* 固定高度确保对齐 */
      border-radius: 10px !important;
      transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
      line-height: 30px !important; /* 内容垂直居中 */
    }
    
    /* 聚焦状态样式 - 与默认筛选条件一致 */
    .ant-select-focused .ant-select-selector,
    .ant-select:focus .ant-select-selector,
    .ant-input:focus,
    .ant-input-focused {
      border-color: #2563eb !important;
      box-shadow: none !important;
    }
    
    /* 悬停状态样式 - 与默认筛选条件一致 */
    .ant-select:hover .ant-select-selector,
    .ant-input:hover {
      border-color: #5b86ff !important;
    }
    
    /* 覆盖任何可能的Ant Design默认样式 */
    .ant-form-item-control-input {
      width: 200px !important;
      height: 32px !important;
      display: flex !important;
      align-items: center !important;
    }
    
    .ant-form-item-control-input-content {
      width: 200px !important;
      height: 32px !important;
      display: flex !important;
      align-items: center !important;
    }
    
    /* 最暴力的覆盖 - 直接针对高级筛选区域 */
    > .ant-form-item > .ant-form-item-control > .ant-form-item-control-input > .ant-form-item-control-input-content > .ant-select {
      width: 200px !important;
      height: 32px !important;
      display: inline-block !important;
      
      .ant-select-selector {
        border-radius: 10px !important;
        height: 32px !important;
        display: flex !important;
        align-items: center !important;
      }
    }
    
    /* 确保所有Select内部的选择项都对齐 */
    .ant-select-selection-item {
      display: flex !important;
      align-items: center !important;
      line-height: 30px !important;
    }
    
    .ant-select-selection-placeholder {
      display: flex !important;
      align-items: center !important;
      line-height: 30px !important;
    }
  }
  
  /* 响应式调整 - 保持与主筛选区域一致 */
  @media (max-width: 768px) {
    .advanced-area, .advanced-row {
      flex-direction: column;
      gap: 12px;
      
      .ant-form-item {
        width: 100%;
        .ant-select, .ant-input {
          width: 100% !important;
        }
      }
    }
  }

  /* 保持原有的应用筛选颜色样式 */

  /* 高级筛选收起展开区域 */
  .advanced-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f3f4f6;
  }

  .advanced-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
  }

  /* placeholder 色彩更柔和 */
  .ant-input::placeholder,
  .ant-picker-input input::placeholder {
    color: #9ca3af;
  }
  
  /* 响应式布局 */
  @media (max-width: 1400px) {
    .search-layout {
      gap: 24px;
    }
    
    .actions-section {
      margin-left: 24px;
    }
  }
  
  @media (max-width: 1200px) {
    .search-layout {
      flex-direction: column;
      gap: 20px;
    }
    
    .filters-section {
      justify-content: flex-start;
      gap: 12px 20px;
    }
    
    .actions-section {
      justify-content: flex-start;
      margin-left: 0;
    }
  }
  
  @media (max-width: 900px) {
    .filters-section {
      gap: 12px 16px;
      
      .ant-form-item {
        .ant-input, .ant-picker {
          width: 180px;
        }
      }
    }
    
    .actions-section {
      gap: 12px;
    }
  }
  
  @media (max-width: 600px) {
    .filters-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      
      .ant-form-item {
        width: 100%;
        .ant-input, .ant-picker {
          width: 100%;
        }
      }
    }
    
    .actions-section {
      width: 100%;
      gap: 12px;
      
      .search-btn, .advanced-filter-btn {
        flex: 1;
      }
    }
    

  }
`;

export const StyledFormItem = styled(Form.Item)`
  // 迭代二：已填写/未填写状态的视觉区分
  &.has-value .ant-input-affix-wrapper,
  &.has-value .ant-picker {
    border-color: #2563eb; // 更贴合页面主色
    transition: border-color 0.2s ease;
  }
  &.no-value .ant-input-affix-wrapper,
  &.no-value .ant-picker {
    border-color: #d9d9d9; // 无值时边框恢复默认
    transition: border-color 0.3s ease; // 添加平滑过渡
  }

  // 防止浏览器自动填充样式干扰
  .ant-input:-webkit-autofill,
  .ant-input:-webkit-autofill:hover,
  .ant-input:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px white inset !important;
    -webkit-text-fill-color: #000000 !important;
    transition: background-color 5000s ease-in-out 0s;
  }

  // 确保输入框内部没有异常背景
  .ant-input-affix-wrapper .ant-input {
    background-color: transparent !important;
  }

  // 优化focus状态，避免与has-value样式冲突
  &.has-value .ant-input-affix-wrapper:focus,
  &.has-value .ant-input-affix-wrapper-focused {
    border-color: #2563eb !important;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.15) !important;
  }

  &.has-value .ant-input-affix-wrapper:hover {
    border-color: #2563eb !important;
  }

  // 确保表单项垂直对齐
  .ant-form-item-control {
    position: relative;
  }

  // 日期范围选择器的特殊样式
  &.date-range-item {
    min-width: 280px; // 确保日期范围选择器有足够宽度
  }
  
  // 独立日期选择器的样式
  &.date-item {
    min-width: 200px; // 为独立日期选择器设置合适宽度
  }

  // 错误状态下的样式
  &.ant-form-item-has-error .ant-input-affix-wrapper,
  &.ant-form-item-has-error .ant-picker {
    border-color: #ff4d4f !important;
  }
  
  // 错误状态下的hover效果
  &.ant-form-item-has-error .ant-input-affix-wrapper:hover,
  &.ant-form-item-has-error .ant-picker:hover {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
  }
  
  // 错误状态下的focus效果
  &.ant-form-item-has-error .ant-input-affix-wrapper:focus,
  &.ant-form-item-has-error .ant-input-affix-wrapper-focused,
  &.ant-form-item-has-error .ant-picker:focus,
  &.ant-form-item-has-error .ant-picker-focused {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
  }
`;

export const DateRangeErrorTip = styled.div`
  color: #ff4d4f; /* Ant Design 错误提示的红色 */
  font-size: 12px;
  margin-top: 4px;
`;
