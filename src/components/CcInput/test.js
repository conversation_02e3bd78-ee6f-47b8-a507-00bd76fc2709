// 简单的功能测试文件
// 这个文件可以用于手动测试CcInput组件的功能

import React, { useState } from 'react';
import CcInput from './index';

const TestCcInput = () => {
  const [ccList, setCcList] = useState([]);
  const currentUserEmail = '<EMAIL>';

  const handleCcChange = (newList) => {
    console.log('抄送人列表变化:', newList);
    setCcList(newList);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px' }}>
      <h3>CcInput组件测试</h3>
      <p>当前用户邮箱: {currentUserEmail}</p>
      <CcInput
        value={ccList}
        onChange={handleCcChange}
        currentUserEmail={currentUserEmail}
      />
      <div style={{ marginTop: '20px' }}>
        <h4>当前抄送人列表:</h4>
        <pre>{JSON.stringify(ccList, null, 2)}</pre>
      </div>
    </div>
  );
};

export default TestCcInput;
