import React, { useState, useEffect, useRef } from 'react';
import { Input, Tag } from 'antd';
import { validateUser } from '../../request/api';
import './style.css';

const CcInput = ({ value = [], onChange, currentUserEmail, fixedCount = 0, fixedEmails = [] }) => {
  const [inputValue, setInputValue] = useState('');
  const [errorMsg, setErrorMsg] = useState(null);
  const [showAllTags, setShowAllTags] = useState(false);
  const inputRef = useRef(null);
  const isProcessingRef = useRef(false);

  // 清除错误提示的定时器
  useEffect(() => {
    if (errorMsg) {
      const timer = setTimeout(() => {
        setErrorMsg(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [errorMsg]);

  // 简单的邮箱格式验证
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

    // 添加用户的核心逻辑
  const addUser = async (email) => {
    if (!email) return;
    
    // 防止重复处理
    if (isProcessingRef.current) return;
    isProcessingRef.current = true;

    try {
      // 基本邮箱格式验证
      if (!isValidEmail(email)) {
        setErrorMsg('请输入有效的邮箱格式');
        triggerShakeAnimation();
        isProcessingRef.current = false;
        return;
      }

      // 前端自校验：检查是否为当前用户
      if (email === currentUserEmail) {
        setErrorMsg('不能抄送给自己');
        triggerShakeAnimation();
        isProcessingRef.current = false;
        return;
      }

      // 前端自校验：检查是否已在列表中（含固定抄送）
      const existsInUser = value.some(user => user.email === email);
      const existsInFixed = Array.isArray(fixedEmails) && fixedEmails.includes(email);
      if (existsInUser || existsInFixed) {
        setErrorMsg('该用户已抄送');
        triggerShakeAnimation();
        isProcessingRef.current = false;
        return;
      }

      // 检查是否达到上限（固定抄送 + 用户抄送）
      const totalBeforeAdd = (Array.isArray(value) ? value.length : 0) + (Number.isFinite(fixedCount) ? fixedCount : 0);
      if (totalBeforeAdd >= 50) {
        setErrorMsg('抄送人数已达上限（50人）');
        triggerShakeAnimation();
        isProcessingRef.current = false;
        return;
      }

      // 调用后端校验
      const response = await validateUser(email);
      
      // 生成显示文本和完整信息
      let displayText = response.name;
      let fullInfo = response.name;
      
      if (response.department) {
        displayText = `${response.name}-${response.department}`;
        fullInfo = `${response.name}-${response.department}`;
      }
      
      // 如果显示文本太长，进行截断
      if (displayText.length > 8) {
        if (response.department) {
          // 有部门信息的情况下，优先保留姓名，部门名称截断
          const maxDeptLength = 8 - response.name.length - 1; // 减去"-"的长度
          if (maxDeptLength > 0) {
            const truncatedDept = response.department.length > maxDeptLength 
              ? response.department.substring(0, maxDeptLength) + '...'
              : response.department;
            displayText = `${response.name}-${truncatedDept}`;
          } else {
            // 如果姓名本身就很长，只显示姓名并截断
            displayText = response.name.length > 5 
              ? response.name.substring(0, 5) + '...'
              : response.name;
          }
        } else {
          // 没有部门信息，只截断姓名
          displayText = response.name.substring(0, 5) + '...';
        }
      }

      const newUser = {
        open_id: response.open_id,
        email: email,
        display_text: displayText,
        full_info: fullInfo
      };

      // 调用父组件的onChange回调
      onChange([...value, newUser]);

      // 清空输入框和错误提示
      setInputValue('');
      setErrorMsg(null);
    } catch (error) {
      setErrorMsg(error);
      triggerShakeAnimation();
    } finally {
      // 重置处理标记
      isProcessingRef.current = false;
    }
  };

  // 处理键盘事件
  const handleKeyDown = async (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      const email = inputValue.trim();
      await addUser(email);
    }
  };

  // 处理失焦事件
  const handleBlur = async (event) => {
    const email = inputValue.trim();
    if (email) {
      await addUser(email);
    }
  };

  // 触发抖动动画
  const triggerShakeAnimation = () => {
    const inputElement = inputRef.current?.input || inputRef.current;
    if (inputElement) {
      inputElement.classList.add('cc-input-shake');
      setTimeout(() => {
        inputElement.classList.remove('cc-input-shake');
      }, 600);
    }
  };

  // 处理移除用户
  const handleRemove = (emailToRemove) => {
    const newList = value.filter(user => user.email !== emailToRemove);
    onChange(newList);
    
    // 如果删除后数量不超过6个，自动收起
    if (newList.length <= 6) {
      setShowAllTags(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
    // 用户开始输入时清除错误提示
    if (errorMsg) {
      setErrorMsg(null);
    }
  };

  const totalCount = (Array.isArray(value) ? value.length : 0) + (Number.isFinite(fixedCount) ? fixedCount : 0);

  return (
    <div className="cc-input-container">
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Input
          ref={inputRef}
          placeholder="请输入抄送人邮箱"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          disabled={totalCount >= 50}
          style={{ flex: 1 }}
        />
        <span className="cc-count">{totalCount}/50</span>
      </div>

      {/* 错误提示区域 */}
      <div className={`cc-error-message ${errorMsg ? 'show' : ''}`}>
        {errorMsg}
      </div>

      {/* 已选用户标签区域 */}
      {value.length > 0 && (
        <div className="cc-selected-users">
          {(showAllTags ? value : value.slice(0, 6)).map((user) => (
            <Tag
              key={user.email}
              closable
              onClose={() => handleRemove(user.email)}
              title={user.full_info || user.display_text}
            >
              {user.display_text}
            </Tag>
          ))}
          {value.length > 6 && (
            <Tag
              style={{
                cursor: 'pointer',
                backgroundColor: '#f0f0f0',
                border: '1px dashed #d9d9d9',
                color: '#666',
                userSelect: 'none'
              }}
              onClick={() => setShowAllTags(!showAllTags)}
              title={showAllTags ? '点击收起标签' : `点击显示全部${value.length}个抄送人员`}
            >
              {showAllTags ? '收起' : `显示更多 (${value.length - 6}个)`}
            </Tag>
          )}
        </div>
      )}
    </div>
  );
};

export default CcInput;
