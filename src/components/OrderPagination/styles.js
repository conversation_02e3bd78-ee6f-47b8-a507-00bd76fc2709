import styled from 'styled-components';

export const PaginationContainer = styled.div`
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0; /* 去掉外层额外留白，不再出现多一层框的视觉感 */

  .ant-pagination {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: flex-end; /* 右对齐 */
  }

  // 分页器样式微调
  .ant-pagination-item {
    border-radius: 4px;
  }

  .ant-pagination-total-text {
    margin-right: auto; /* 总数信息靠左，其他元素靠右 */
    color: #6b7280;
  }

  .ant-pagination-item {
    background: #ffffff;
    border-color: #e5e7eb;
    color: #374151;
    transition: all 0.2s ease;
    border-radius: 8px;
    min-width: 32px;
    height: 32px;
    line-height: 30px;
  }
  .ant-pagination-item a {
    color: #374151;
  }
  .ant-pagination-item:hover {
    border-color: #2563eb;
  }

  .ant-pagination-item-active {
    border-color: #111827;
    background-color: #111827;
  }
  .ant-pagination-item-active a {
    color: #fff !important;
  }

  /* 上一页 / 下一页按钮圆角与悬停 */
  .ant-pagination-prev .ant-pagination-item-link,
  .ant-pagination-next .ant-pagination-item-link {
    border-radius: 8px;
    border-color: #e5e7eb;
    background: #ffffff;
    height: 32px;
    line-height: 30px;
  }
  .ant-pagination-prev:hover .ant-pagination-item-link,
  .ant-pagination-next:hover .ant-pagination-item-link {
    border-color: #2563eb;
    color: #2563eb;
  }

  // 页码输入框样式
  .ant-pagination-options-quick-jumper input {
    border-radius: 4px;
  }

  // 每页显示数量选择器样式
  .ant-select-selector {
    border-radius: 8px;
  }

  /* 快速跳转输入框 */
  .ant-pagination-options-quick-jumper input {
    height: 32px;
    border-radius: 8px;
  }

  // 响应式设计：小屏幕时调整布局
  @media (max-width: 768px) {
    justify-content: center;
    flex-direction: column;
    gap: 8px;

    .ant-pagination {
      flex-wrap: wrap;
      justify-content: center;
    }
  }
`;
