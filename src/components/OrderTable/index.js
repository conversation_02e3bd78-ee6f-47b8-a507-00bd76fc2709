import React, { Component } from 'react';
import { Table, Tooltip } from 'antd';
import { AuditedTable } from './styles';
import OrderDetailDrawer from '../OrderDetailDrawer';
import AuditRoleTagCell from '../RoleTag/AuditRoleTagCell';

class OrderTable extends Component {
  // 构造表格列配置
  getColumns = () => {
    return [
      {
        title: '单号',
        dataIndex: 'orderId',
        key: 'orderId',
        width: 120,
        align: 'center',
        ellipsis: true,
        render: (text) => (
          <span className="order-id-em">{text}</span>
        ),
      },
      {
        title: '工单标题',
        dataIndex: 'title',
        key: 'title',
        width: 200,
        align: 'center',
        ellipsis: {
          showTitle: false,
        },
        render: (text) => (
          <Tooltip placement="topLeft" title={text || ''}>
            <span>{text || ''}</span>
          </Tooltip>
        ),
      },
      {
        title: '工单类型',
        dataIndex: 'orderType',
        key: 'orderType',
        width: 120,
        align: 'center',
        ellipsis: true,
      },
      {
        title: '总节点数',
        dataIndex: 'totalNodes',
        key: 'totalNodes',
        width: 100,
        align: 'center',
      },
      {
        title: '当前节点',
        dataIndex: 'currentNode',
        key: 'currentNode',
        width: 120,
        align: 'center',
        ellipsis: true,
      },
      {
        title: '工单状态',
        dataIndex: 'orderStatus',
        key: 'orderStatus',
        width: 100,
        align: 'center',
        render: (text, record) => (
          <AuditRoleTagCell 
            approvalStatusForMe={record.approval_status_for_me}
            isCC={record.is_cc}
          >
            <span>{text}</span>
          </AuditRoleTagCell>
        ),
      },
      {
        title: '申请人',
        dataIndex: 'applicant',
        key: 'applicant',
        width: 100,
        align: 'center',
        ellipsis: true,
      },
      {
        title: '运维负责人',
        dataIndex: 'opsOwner',
        key: 'opsOwner',
        width: 120,
        align: 'center',
        ellipsis: true,
      },
      {
        title: '申请时间',
        dataIndex: 'appliedTime',
        key: 'appliedTime',
        width: 150,
        align: 'center',
      },
      {
        title: '详情',
        key: 'detail',
        width: 80,
        fixed: 'right', // 固定列位置确保宽度生效
        align: 'center',
        render: (_, record) => (
          <OrderDetailDrawer
            title="详情"
            orderID={record.orderId}
            orderType={record.orderType}
            roleType={record.role_type} // 传递角色类型
          />
        ),
      },
    ];
  };

  
  
  // 处理表格变化（分页、排序、筛选）
  handleTableChange = (pagination, filters, sorter) => {
    // 优先使用统一的表格变化处理方法
    if (this.props.onTableChange) {
      this.props.onTableChange(pagination, filters, sorter);
      return;
    }

    // 兼容旧的分离式处理方法
    // 处理工单类型筛选变化
    if (this.props.onOrderTypeFilterChange) {
      // filters.orderType 可能是数组、null或undefined
      // reset时会是null，需要转换为空数组
      const selectedTypes = filters.orderType || [];
      this.props.onOrderTypeFilterChange(selectedTypes);
    }

    // 处理角色筛选变化
    if (this.props.onRoleFilterChange) {
      // filters.orderId 是"单号"列的筛选值（角色筛选）
      const selectedRoles = filters.orderId || [];
      this.props.onRoleFilterChange(selectedRoles);
    }
  };

  render() {
    const { dataSource = [], loading = false, header = null, footer = null } = this.props;

    return (
      <AuditedTable>
        {header && (
          <div className="table-header">{header}</div>
        )}
        <Table
          dataSource={dataSource}
          columns={this.getColumns()}
          rowKey="orderId" // 使用orderId作为唯一key
          size="middle"
          pagination={false} // 禁用内置分页，由父组件处理分页
          scroll={{ x: 1200 }} // 设置固定表格宽度，强制省略号生效
          loading={loading} // 加载状态
          onChange={this.handleTableChange} // 处理表格变化
        />
        {footer && (
          <div className="table-footer">{footer}</div>
        )}
      </AuditedTable>
    );
  }
}

export default OrderTable;
