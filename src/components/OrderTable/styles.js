import styled from 'styled-components';

export const AuditedTable = styled.div`
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1px solid rgba(229,231,235,0.6);

  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 0 16px 12px 16px; /* 与页面左右保持间距 */

  /* 表格卡片化、去重边 */
  .ant-table {
    background: transparent;
    border-radius: 10px;
  }

  /* 表头：简约、浅灰背景，与参考样式对齐 */
  .ant-table-thead > tr > th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    text-align: center;
  }

  /* 单元格间距与分隔线更轻 */
  .ant-table-tbody > tr > td {
    padding: 12px 12px;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
  }

  /* 斑马纹 + 悬停轻灰底 */
  .ant-table-tbody > tr:nth-child(odd) > td { background-color: #ffffff; }
  .ant-table-tbody > tr:nth-child(even) > td { background-color: #fafafa; }
  .ant-table-tbody > tr:hover > td { background-color: #f5f7fb; }

  /* 排序列背景与表头一致 */
  .ant-table-column-sort {
    background-color: #f9fafb !important;
  }

  /* 详情按钮与参考蓝色保持一致（仅限当前表格作用域） */
  .ant-btn-primary {
    background-color: #2563eb;
    border-color: #2563eb;
  }
  .ant-btn-primary:hover,
  .ant-btn-primary:focus {
    background-color: #1e40af;
    border-color: #1e40af;
  }

  /* 单号强调：等宽、小底色、圆角胶囊 */
  .order-id-em {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 12px;
    color: #374151;
    background-color: #f3f4f6;
    padding: 2px 6px;
    border-radius: 6px;
  }

  /* 状态徽章尺寸微调 */
  .status-badge {
    border-radius: 9999px;
    padding: 0 8px;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
  }

  /* 头部/底部槽位区域样式 */
  .table-header {
    padding: 4px 4px 0 4px;
  }
  .table-footer {
    padding: 8px 4px 0 4px;
  }
`;
