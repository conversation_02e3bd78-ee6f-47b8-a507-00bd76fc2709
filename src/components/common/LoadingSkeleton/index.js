import React, { Component } from 'react';
import { Skeleton } from 'antd';
import { SkeletonContainer } from './styles';

class LoadingSkeleton extends Component {
  render() {
    return (
      <SkeletonContainer>
        {/* active 属性表示有动画效果，paragraph 决定行数，title 决定是否显示标题 */}
        <Skeleton active paragraph={{ rows: 8 }} title={false} />
      </SkeletonContainer>
    );
  }
}

export default LoadingSkeleton;
