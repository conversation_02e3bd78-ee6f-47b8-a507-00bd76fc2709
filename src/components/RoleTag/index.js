import React from 'react';
import { Tag } from 'antd';
import './style.css';

/**
 * RoleTag 组件 - 用于展示工单身份标签
 * @param {Object} props
 * @param {string} props.roleType - 角色类型枚举值
 * @param {boolean} props.withContainer - 是否包含容器，用于表格单元格中的定位
 * @returns {JSX.Element|null} 返回对应的标签组件或null
 */
const RoleTag = ({ roleType, withContainer = false }) => {
  // 角色映射配置
  const ROLE_CONFIG = {
    // doingOrder 和 doneOrder 使用的标签
    APPLICANT: {
      text: '[申请]',
      color: 'blue'
    },
    CC_TO_ME: {
      text: '[抄送]',
      color: 'default'
    },
    // auditOrder 使用的标签
    TO_BE_APPROVED: {
      text: '[待审]',
      color: 'red'
    },
    WILL_BE_APPROVED: {
      text: '[将审]',
      color: 'orange'
    },
    ALREADY_APPROVED: {
      text: '[已审]',
      color: 'default'
    },
    COMPLETED: {
      text: '[完结]',
      color: 'default'
    },
    // 兼容旧的标签类型
    ALREADY_APPROVED_OLD: {
      text: '[我已审批]',
      color: 'default'
    },
    TO_BE_APPROVED_OLD: {
      text: '[待我审批]',
      color: 'orange'
    }
  };

  // 根据roleType获取配置
  const config = ROLE_CONFIG[roleType];

  // 如果找不到配置，返回null（不渲染任何内容）
  if (!config) {
    return null;
  }

  const tagElement = (
    <Tag
      className="role-tag"
      color={config.color}
    >
      {config.text}
    </Tag>
  );

  // 如果需要容器，则包装在容器中
  if (withContainer) {
    return (
      <div className="role-tag-container">
        {tagElement}
      </div>
    );
  }

  return tagElement;
};

export default RoleTag;
