import React from 'react';
import { Tag } from 'antd';
import './style.css';

/**
 * AuditRoleTagCell 组件 - 用于auditOrder表格单元格中展示组合角色标签和内容
 * @param {Object} props
 * @param {number} props.approvalStatusForMe - 审批状态 (3:待审, 2:将审, 1:已审, 0:完结)
 * @param {number} props.isCC - 是否抄送 (1:抄送, 0:非抄送)
 * @param {React.ReactNode} props.children - 单元格主要内容
 * @returns {JSX.Element} 返回包含组合角色标签的单元格组件
 */
const AuditRoleTagCell = ({ approvalStatusForMe, isCC, children }) => {
  // 审批状态标签配置
  const APPROVAL_STATUS_CONFIG = {
    3: { text: '[待审]', color: 'red' },
    2: { text: '[将审]', color: 'orange' },
    1: { text: '[已审]', color: 'default' },
    0: { text: '[完结]', color: 'default' }
  };

  // 抄送标签配置
  const CC_CONFIG = {
    text: '[抄送]',
    color: 'default'
  };

  // 生成标签
  const generateTags = () => {
    const tags = [];
    
    // 添加审批状态标签
    if (approvalStatusForMe !== undefined && approvalStatusForMe !== null) {
      const statusConfig = APPROVAL_STATUS_CONFIG[approvalStatusForMe];
      if (statusConfig) {
        tags.push(
          <Tag key="approval" className="role-tag" color={statusConfig.color} style={{ top: '2px', left: '2px' }}>
            {statusConfig.text}
          </Tag>
        );
      }
    }
    
    // 添加抄送标签（如果is_cc为1）
    if (isCC === 1) {
      tags.push(
        <Tag key="cc" className="role-tag" color={CC_CONFIG.color} style={{ top: '2px', left: tags.length > 0 ? '50px' : '2px' }}>
          {CC_CONFIG.text}
        </Tag>
      );
    }
    
    return tags;
  };

  const tags = generateTags();

  return (
    <div className="role-tag-cell">
      {tags}
      <div className="role-tag-cell-content">
        {children}
      </div>
    </div>
  );
};

export default AuditRoleTagCell; 