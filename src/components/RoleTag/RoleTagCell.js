import React from 'react';
import RoleTag from './index';
import './style.css';

/**
 * RoleTagCell 组件 - 用于表格单元格中展示角色标签和内容
 * @param {Object} props
 * @param {string} props.roleType - 角色类型枚举值
 * @param {React.ReactNode} props.children - 单元格主要内容
 * @returns {JSX.Element} 返回包含角色标签的单元格组件
 */
const RoleTagCell = ({ roleType, children }) => {
  return (
    <div className="role-tag-cell">
      <RoleTag roleType={roleType} />
      <div className="role-tag-cell-content">
        {children}
      </div>
    </div>
  );
};

export default RoleTagCell;
