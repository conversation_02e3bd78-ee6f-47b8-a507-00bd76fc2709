/* RoleTag组件样式 */
.role-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 9999px;
  border: 1px solid #e5e7eb;
  display: inline-block;
  line-height: 1;
  user-select: none;
  position: absolute;
  top: 2px;
  left: 2px;
  z-index: 1;
  background-color: #ffffff;
  white-space: nowrap;
  margin: 0;
  box-shadow: 0 1px 2px rgba(0,0,0,0.04);
}

/* 确保标签容器支持相对定位 */
.role-tag-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 20px;
  padding-top: 14px; /* 为顶部标签留出空间 */
}

/* 表格单元格容器样式 */
.role-tag-cell {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 28px;
  padding-top: 14px; /* 为左上角标签留出空间 */
}

/* 单元格主要内容样式 */
.role-tag-cell-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-tag {
    font-size: 9px;
    padding: 1px 3px;
    top: 1px;
    left: 1px;
  }

  .role-tag-cell {
    padding-top: 10px;
    min-height: 20px;
  }
}
