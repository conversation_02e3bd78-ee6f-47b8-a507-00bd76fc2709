"use client"
import React, { useState, useEffect } from "react";
import { message } from "antd";
import { requestFlowApproval, requestFlowAuditTurn, requestOrderDetail, requestUpdateOrderRemark } from "@/request/api";
import styled from "styled-components";
import Cookies from "js-cookie";
import {
  X,
  User,
  FileText,
  MessageSquare,
  CheckCircle,
  Clock,
  AlertCircle,
  UserCheck,
  ThumbsUp,
  ThumbsDown,
} from "lucide-react";

// 自定义UI组件样式 - 模仿shadcn/ui
const CustomButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: 1px solid transparent;
  padding: 8px 16px;
  height: 36px;

  &.variant-default {
    background-color: #0f172a;
    color: #f8fafc;
    &:hover {
      background-color: #1e293b;
    }
  }

  &.variant-outline {
    border: 1px solid #e2e8f0;
    background-color: transparent;
    &:hover {
      background-color: #f1f5f9;
    }
  }

  &.variant-ghost {
    background-color: transparent;
    &:hover {
      background-color: #f1f5f9;
    }
  }

  &.variant-destructive {
    background-color: #dc2626;
    color: #f8fafc;
    &:hover {
      background-color: #b91c1c;
    }
  }

  &.size-sm {
    height: 32px;
    padding: 6px 12px;
    font-size: 13px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const Card = styled.div`
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
`;

const CardHeader = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-bottom: 0;
`;

const CardTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  line-height: 1;
  margin: 0;
`;

const CardContent = styled.div`
  padding: 24px;
  padding-top: 0;
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid transparent;

  &.bg-green-100 {
    background-color: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
  }

  &.bg-blue-100 {
    background-color: #dbeafe;
    color: #1e40af;
    border-color: #bfdbfe;
  }

  &.bg-orange-100 {
    background-color: #fed7aa;
    color: #c2410c;
    border-color: #fdba74;
  }

  &.bg-red-100 {
    background-color: #fee2e2;
    color: #dc2626;
    border-color: #fecaca;
  }

  &.bg-yellow-100 {
    background-color: #fef3c7;
    color: #d97706;
    border-color: #fde68a;
  }

  &.bg-gray-100 {
    background-color: #f3f4f6;
    color: #374151;
    border-color: #e5e7eb;
  }
`;

const CustomTextarea = styled.textarea`
  display: flex;
  min-height: 80px;
  width: 100%;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  padding: 12px;
  font-size: 14px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
`;

const CustomInput = styled.input`
  display: flex;
  height: 36px;
  width: 100%;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  padding: 8px 12px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
`;

const CustomLabel = styled.label`
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  color: #374151;
`;

// Dialog组件样式
const DialogOverlay = styled.div`
  position: fixed;
  inset: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
`;

const DialogContent = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 32rem;
  max-height: 85vh;
  overflow-y: auto;
`;

const DialogHeader = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-bottom: 16px;
`;

const DialogTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const DialogFooter = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 24px;
  padding-top: 16px;

  @media (min-width: 640px) {
    flex-direction: row;
    justify-content: flex-end;
  }
`;

const columns = [
  {
    title: "节点ID",
    dataIndex: "stageNum",
    key: "stageNum",
    align: "center",
  },
  {
    title: "节点名称",
    dataIndex: "stageName",
    key: "stageName",
    align: "center",
  },
  {
    title: "节点处理人",
    dataIndex: "stageOperator",
    key: "stageOperator",
    align: "center",
  },
  {
    title: "结果",
    dataIndex: "stageResult",
    key: "stageResult",
    align: "center",
  },
];

export default function OrderDetailDrawer({ orderID, orderType, visible, onClose }) {
  // 状态管理 - 保持原有的所有状态变量
  const [auditButtonVisible, setAuditButtonVisible] = useState(false);
  const [turnAuditButtonVisible, setTurnAuditButtonVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [auditVisible, setAuditVisible] = useState(false);
  const [turnAuditVisible, setTurnAuditVisible] = useState(false);
  const [auditEmail, setAuditEmail] = useState("");
  const [remark, setRemark] = useState("");
  const [stageInfos, setStageInfos] = useState([]);
  const [orderInfo, setOrderInfo] = useState({ info: {} });
  const [columnData, setColumnData] = useState([]);
  const [comment, setComment] = useState("");
  const [showOwnerChangeModal, setShowOwnerChangeModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [newOwnerEmail, setNewOwnerEmail] = useState("");
  const [approvalAction, setApprovalAction] = useState("approve");

  // 常量映射
  const stageResultMap = {
    0: "wait",
    1: "finish",
    2: "error",
  };

  const orderResultMap = {
    0: "wait",
    1: "finish",
    2: "wait",
  };

  const cnMap = {
    "common": {
      "title": [3, "标题"],
      "ops_audit_email": [3, "运维审批人"],
      "apply_msg": [6, "申请理由"],
    },
    "sql_audit_execute": {
      "db_host": [3, "数据库IP"],
      "check_md5": [3, "校验ID"],
      "sql": [6, "sql语句"]
    },
    "sql_audit_file_execute": {
      "db_host": [3, "数据库IP"],
      "check_md5": [3, "校验ID"],
      "sql": [6, "sql语句"]
    },
    "server_jump_impower": {
      "server_ip": [6, "申请登陆权限IP"],
      "apply_msg": [6, "申请理由"],
    },
    "pointed_approver": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "cdn_create_execute": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "domain_resolve_execute": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "long_term_token_new_refresh": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "long_term_token_view": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    }
  };

  // 工具函数 - 获取中文映射
  const getCnMap = (orderType) => {
    if (orderType in cnMap) {
      return cnMap[orderType];
    }
    return {
      "apply_msg": [6, "申请理由"]
    };
  };

  // 数据获取函数
  const fetchOrderDetail = async (orderID) => {
    try {
      const userEmail = Cookies.get("user_email");
      const data = await requestOrderDetail({ order_id: orderID });

      if (data === null) {
        return;
      }

      const stageInfosData = data.stage_infos;
      const orderInfoData = data.order_info;

      // 初始化按钮显示状态
      let auditButtonVisibleState = false;
      let turnAuditButtonVisibleState = false;

      if (stageInfosData[orderInfoData.current_stage - 1].stage_operator === userEmail && orderInfoData.result === 0) {
        auditButtonVisibleState = true;
      }
      if (stageInfosData[orderInfoData.current_stage - 1].stage_operator === userEmail && orderInfoData.result === 0 && orderInfoData.current_stage < orderInfoData.total_stage_num) {
        turnAuditButtonVisibleState = true;
      }

      // 解析详情字段
      orderInfoData.info = JSON.parse(orderInfoData.info);
      orderInfoData.info["apply_msg"] = orderInfoData.apply_msg;

      const columnDataMapped = stageInfosData.map((item, index) => ({
        key: index,
        stageNum: item.stage_num,
        stageName: item.stage_name,
        stageOperator: item.stage_operator,
        stageResult: item.stage_result_desc,
      }));

      setAuditButtonVisible(auditButtonVisibleState);
      setTurnAuditButtonVisible(turnAuditButtonVisibleState);
      setStageInfos(stageInfosData);
      setOrderInfo(orderInfoData);
      setColumnData(columnDataMapped);
      setDrawerVisible(true);
    } catch (err) {
      console.log(err);
    }
  };

  // useEffect替代componentDidMount和componentDidUpdate
  useEffect(() => {
    const isVisible = visible === true;
    if (isVisible && orderID) {
      fetchOrderDetail(orderID);
    }
  }, [visible, orderID]);
  componentDidUpdate(prevProps, prevState) {
    // 判断抽屉是否“被打开”：
    // 1) 非受控：内部 drawerVisible 从 false -> true
    const becameOpenByState = prevState.drawerVisible === false && this.state.drawerVisible === true;
    // 2) 受控：外部 visible 从 false/undefined -> true
    const becameOpenByProp = (prevProps.visible === false || prevProps.visible === undefined) && this.props.visible === true;
    // 3) 抽屉已打开且 orderID 发生变化（切换行查看详情）
    const isOpenNow = (this.props.visible !== undefined ? this.props.visible : this.state.drawerVisible) === true;
    const orderChangedWhileOpen = isOpenNow && prevProps.orderID !== this.props.orderID && !!this.props.orderID;

    if (becameOpenByState || becameOpenByProp || orderChangedWhileOpen) {
      let userEmail = Cookies.get("user_email")
      requestOrderDetail({ order_id: this.props.orderID })
        .then((data) => {
          if (data === null) {
            return;
          }
          const stageInfos = data.stage_infos;
          const orderInfo = data.order_info;
          
          // 先重置按钮状态，避免缓存问题
          let auditButtonVisible = false;
          let turnAuditButtonVisible = false;
          
          // 审批按钮显示条件：当前用户是操作员且工单未完成
          if (stageInfos[orderInfo.current_stage - 1].stage_operator === userEmail && orderInfo.result === 0) {
            auditButtonVisible = true;
          }
          // 审批人变更按钮显示条件：当前用户是操作员且工单未完成且未到最后阶段
          if (stageInfos[orderInfo.current_stage - 1].stage_operator === userEmail && orderInfo.result === 0 && orderInfo.current_stage < orderInfo.total_stage_num) {
            turnAuditButtonVisible = true;
          }
          
          // 统一设置按钮状态
          this.setState({
            auditButtonVisible,
            turnAuditButtonVisible
          });
          console.log(data)
          // 将 order info 字符串转为json对象
          orderInfo.info = JSON.parse(orderInfo.info)
          orderInfo.info["apply_msg"] = orderInfo.apply_msg
          const columnData = stageInfos.map((item, index) => {
            return {
              key: index,
              stageNum: item.stage_num,
              stageName: item.stage_name,
              stageOperator: item.stage_operator,
              stageResult: item.stage_result_desc,
            };
          });
          this.setState({
            stageInfos: stageInfos,
            orderInfo: orderInfo,
            columnData: columnData,
          });
        })
        .catch((err) => console.log(err));
    }
  }

  // 组件销毁前调用，清除一些事件(比如定时事件)
  componentWillUnmount() {
    this.setState = (state, callback) => {
      return
    }
  }

  // 开启左侧抽屉
  showDrawer = () => {
    this.setState({
      drawerVisible: true,
    });
  };
  // 关闭左侧抽屉
  closeDrawer = () => {
    this.setState({
      drawerVisible: false,
    });
  };
  // 统一关闭抽屉（兼容受控/非受控）
  closeDrawerImmediate = () => {
    // 受控模式（父组件通过 visible 控制），优先调用 onClose 通知父组件关闭
    if (this.props.visible !== undefined) {
      this.props.onClose && this.props.onClose();
    } else {
      // 非受控模式，使用本地状态关闭
      this.closeDrawer();
    }
  };
  // 开启审批对话框
  showAuditModel = () => {
    this.setState({
      auditVisible: true,
    });
  };
  // 关闭审批对话框
  closeAuditModel = () => {
    this.setState({
      auditVisible: false,
    });
  };
  // 提交审批结果 todo 需要处理审批后的刷新
  handleAudit = (result) => {
    let args = {
      order_id: this.state.orderInfo.order_id,
      stage_num: this.state.orderInfo.current_stage,
      chosen_action: result
    }
    
    // 立即关闭弹窗
    this.closeAuditModel();

    // 审批人变更成功后关闭详情页弹窗
    this.closeDrawerImmediate();
    
    // 异步处理后端请求，响应结果在页面顶部显示
    requestFlowApproval(args)
      .then((data) => {
        if (data.resp_common && data.resp_common.ret === 0) {
          message.success('审批成功');
        } else {
          message.error(`审批失败: ${(data.resp_common && data.resp_common.msg) || '未知错误'}`);
        }
      })
      .catch((err) => {
        message.error('审批失败');
        console.log(err);
      });
  }
  // 开启审批人变更对话框
  showTurnAuditVisible = () => {
    this.setState({
      turnAuditVisible: true,
    });
  };
  // 关闭审批人变更对话框
  closeTurnAuditVisible = () => {
    this.setState({
      turnAuditVisible: false,
    });
  };
  handleTurnAudit = () => {
    // 检查邮箱是否为空
    if (!this.state.auditEmail || this.state.auditEmail.trim() === '') {
      message.error('请输入审批人邮箱');
      return;
    }

    // 检查邮箱格式是否正确
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(this.state.auditEmail)) {
      message.error('请输入正确的邮箱格式');
      return;
    }
    
    let args = {
      order_id: this.state.orderInfo.order_id,
      stage_num: this.state.orderInfo.current_stage,
      new_operator_email: this.state.auditEmail
    }
    
    // 立即关闭弹窗
    this.closeTurnAuditVisible();

    // 审批人变更成功后关闭详情页弹窗
    this.closeDrawerImmediate();
    
    // 异步处理后端请求，响应结果在页面顶部显示
    requestFlowAuditTurn(args)
      .then((data) => {
        if (data.resp_common && data.resp_common.ret === 0) {
          message.success('审批人变更成功');
        } else {
          message.error(`审批人变更失败: ${(data.resp_common && data.resp_common.msg) || '未知错误'}`);
        }
      })
      .catch((err) => {
        message.error('审批人变更失败');
        console.log(err);
      });
  }
  handleAuditEmailChange = (e) => {
    this.setState({
      auditEmail: e.target.value,
    });
  };
  saveRemark = (e) => {
    requestUpdateOrderRemark({
      order_id: this.state.orderInfo.order_id,
      remark: this.state.remark.trim()
    })
  }
  render() {
    // 如果传入了visible prop，则使用外部控制；否则使用内部状态
    const drawerVisible = this.props.visible !== undefined ? this.props.visible : this.state.drawerVisible;
    const handleClose = this.props.onClose || this.closeDrawer;
    
    return (
      <>
        {/* 只有在没有外部控制时才显示按钮 */}
        {this.props.visible === undefined && (
          <Button type="primary" onClick={this.showDrawer}>
            <MenuFoldOutlined />
            {this.props.title}
          </Button>
        )}
        <Drawer
          width={800}
          placement="right"
          closable={true}
          onClose={handleClose}
          open={drawerVisible}
        >
          <OrderDetailHeader>
            审批流程跟踪
            <OrderAuditAction className={(this.state.auditButtonVisible || this.state.turnAuditButtonVisible) ? "" : "hide"}>
              <Button 
                type="primary" 
                onClick={this.showTurnAuditVisible}
                className={this.state.turnAuditButtonVisible ? "" : "hide"}
              >
                审批人变更
              </Button>
              <Button 
                type="primary" 
                onClick={this.showAuditModel}
                className={this.state.auditButtonVisible ? "" : "hide"}
              >
                审批
              </Button>
            </OrderAuditAction>
          </OrderDetailHeader>
          <Divider />
          <Steps>
            <Step status="finish" title="提交" icon={<UserOutlined />} />
            {
              this.state.stageInfos.map((item, index) => {
                // 审批人去除邮箱后缀，减少长度
                var reg = new RegExp("@.*", "g");
                item.stage_operator = item.stage_operator.replace(reg, "")
                if (item.stage_result > 1) {
                  return (
                    <Step
                      status={this.state.stageResultMap[item.stage_result]}
                      title={item.stage_name}
                      // subTitle={"("+item.stage_operator+")"}
                      description={item.stage_operator}
                      icon={<CloseCircleOutlined />}
                      key={index}
                    />
                  );
                }
                if (
                  this.state.orderInfo.current_stage === index + 1 &&
                  item.stage_result === 0
                ) {
                  return (
                    <Step
                      status="finish"
                      title={item.stage_name}
                      // subTitle={"("+item.stage_operator+")"}
                      description={item.stage_operator}
                      icon={<LoadingOutlined />}
                      key={index}
                    />
                  );
                }
                return (
                  <Step
                    status={this.state.stageResultMap[item.stage_result]}
                    title={item.stage_name}
                    // subTitle={"("+item.stage_operator+")"}
                    description={item.stage_operator}
                    icon={<SolutionOutlined />}
                    key={index}
                  />
                );
              })
            }
            <Step
              status={this.state.orderResultMap[this.state.orderInfo.result]}
              title="Done"
              icon={<SmileOutlined />}
            />
          </Steps>

          <FlowInfoTable
            dataSource={this.state.columnData}
            columns={columns}
            pagination={false}
          />
          <OrderFlowInfoHeader>
            工单详情
          </OrderFlowInfoHeader>
          <Divider />
          <Descriptions bordered layout="vertical" column={6} labelStyle={{ "fontWeight": "bolder", "fontSize": 15 }}>
            {
              Object.keys(this.getCnMap(this.props.orderType)).map((label, index) => {
                // const cnMap = this.state.cnMap[this.props.orderType]
                const cnMap = this.getCnMap(this.props.orderType)
                const spanNum = cnMap[label][0]
                const cnName = cnMap[label][1]
                const content = this.state.orderInfo.info[label]
                return (
                  <Descriptions.Item
                    key={index}
                    label={cnName}
                    span={spanNum}
                    style={{ "whiteSpace": "pre-line" }}
                  >
                    {content}
                  </Descriptions.Item>
                )
              })
            }
          </Descriptions>

          
          <OrderFlowInfoHeader>
            评论
          </OrderFlowInfoHeader>
          <Divider />
          {/* 只有抽屉显示且orderId存在时才渲染CommentSection */}
          {this.state.drawerVisible && this.state.orderInfo.order_id && (
            <CommentSection
              orderId={this.state.orderInfo.order_id}
              canComment={this.state.orderInfo.result === 0}
              currentStage={this.state.orderInfo.current_stage}
            />
          )}
          <Divider />
        </Drawer>
        <Modal
          title="审批工单"
          open={this.state.auditVisible}
          onCancel={this.closeAuditModel}
          footer={[
            <Button key="audit-reject" type="primary" danger onClick={() => this.handleAudit("no")}>
              驳回
            </Button>,
            <Button key="audit-agree" type="primary" onClick={() => this.handleAudit("yes")}>
              同意
            </Button>
          ]}
        >
          <h1>申请理由：</h1>
          <p>{this.state.orderInfo.apply_msg}</p>
        </Modal>
        <Modal
          title="审批人变更"
          open={this.state.turnAuditVisible}
          okText="提交"
          onOk={this.handleTurnAudit}
          onCancel={this.closeTurnAuditVisible}
        >
          <TurnAuditDiv>
            新审批人邮箱：
            <Input
              placeholder="请输入OA邮箱"
              size="middle"
              prefix={<UserOutlined />}
              value={this.state.auditEmail}
              onChange={this.handleAuditEmailChange}
            />
          </TurnAuditDiv>
        </Modal>
      </>
    );
  }
}
